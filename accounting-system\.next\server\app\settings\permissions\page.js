/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/settings/permissions/page";
exports.ids = ["app/settings/permissions/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsettings%2Fpermissions%2Fpage&page=%2Fsettings%2Fpermissions%2Fpage&appPaths=%2Fsettings%2Fpermissions%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpermissions%2Fpage.tsx&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsettings%2Fpermissions%2Fpage&page=%2Fsettings%2Fpermissions%2Fpage&appPaths=%2Fsettings%2Fpermissions%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpermissions%2Fpage.tsx&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/permissions/page.tsx */ \"(rsc)/./src/app/settings/permissions/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'settings',\n        {\n        children: [\n        'permissions',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/settings/permissions/page\",\n        pathname: \"/settings/permissions\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsettings%2Fpermissions%2Fpage&page=%2Fsettings%2Fpermissions%2Fpage&appPaths=%2Fsettings%2Fpermissions%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpermissions%2Fpage.tsx&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers/SessionProvider.tsx */ \"(rsc)/./src/components/Providers/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0FjY291bnRpbmclNUMlNUNhY2NvdW50aW5nLXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDUHJvdmlkZXJzJTVDJTVDU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdNQUFvSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXEFjY291bnRpbmdcXFxcYWNjb3VudGluZy1zeXN0ZW1cXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcUHJvdmlkZXJzXFxcXFNlc3Npb25Qcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpermissions%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpermissions%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/permissions/page.tsx */ \"(rsc)/./src/app/settings/permissions/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNzZXR0aW5ncyU1QyU1Q3Blcm1pc3Npb25zJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBMQUFpSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQWNjb3VudGluZ1xcXFxhY2NvdW50aW5nLXN5c3RlbVxcXFxzcmNcXFxcYXBwXFxcXHNldHRpbmdzXFxcXHBlcm1pc3Npb25zXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpermissions%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxBY2NvdW50aW5nXFxhY2NvdW50aW5nLXN5c3RlbVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcQWNjb3VudGluZ1xcYWNjb3VudGluZy1zeXN0ZW1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Providers_SessionProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Providers/SessionProvider */ \"(rsc)/./src/components/Providers/SessionProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"نظام المحاسبة الاحترافي\",\n    description: \"نظام محاسبة شامل للشركات الصغيرة والمتوسطة\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers_SessionProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGlCO0FBQzhDO0FBSTlELE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsS0FBSTtrQkFDbEIsNEVBQUNDO1lBQUtDLFdBQVdYLCtKQUFlO3NCQUM5Qiw0RUFBQ0MsNkVBQWVBOzBCQUNiSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJEOlxcQWNjb3VudGluZ1xcYWNjb3VudGluZy1zeXN0ZW1cXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuaW1wb3J0IFNlc3Npb25Qcm92aWRlciBmcm9tIFwiQC9jb21wb25lbnRzL1Byb3ZpZGVycy9TZXNzaW9uUHJvdmlkZXJcIjtcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFtcImxhdGluXCJdIH0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCLZhti42KfZhSDYp9mE2YXYrdin2LPYqNipINin2YTYp9it2KrYsdin2YHZilwiLFxuICBkZXNjcmlwdGlvbjogXCLZhti42KfZhSDZhdit2KfYs9io2Kkg2LTYp9mF2YQg2YTZhNi02LHZg9in2Kog2KfZhNi12LrZitix2Kkg2YjYp9mE2YXYqtmI2LPYt9ipXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJhclwiIGRpcj1cInJ0bFwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8U2Vzc2lvblByb3ZpZGVyPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9TZXNzaW9uUHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwiU2Vzc2lvblByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJkaXIiLCJib2R5IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/settings/permissions/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/settings/permissions/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Accounting\\accounting-system\\src\\app\\settings\\permissions\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/Providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Providers\\\\SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Accounting\\accounting-system\\src\\components\\Providers\\SessionProvider.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers/SessionProvider.tsx */ \"(ssr)/./src/components/Providers/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0FjY291bnRpbmclNUMlNUNhY2NvdW50aW5nLXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDUHJvdmlkZXJzJTVDJTVDU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdNQUFvSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXEFjY291bnRpbmdcXFxcYWNjb3VudGluZy1zeXN0ZW1cXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcUHJvdmlkZXJzXFxcXFNlc3Npb25Qcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpermissions%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpermissions%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/permissions/page.tsx */ \"(ssr)/./src/app/settings/permissions/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNzZXR0aW5ncyU1QyU1Q3Blcm1pc3Npb25zJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBMQUFpSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQWNjb3VudGluZ1xcXFxhY2NvdW50aW5nLXN5c3RlbVxcXFxzcmNcXFxcYXBwXFxcXHNldHRpbmdzXFxcXHBlcm1pc3Npb25zXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpermissions%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/settings/permissions/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/settings/permissions/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PermissionsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(ssr)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building,Check,Edit,FileText,Plus,Save,Settings,Shield,Trash2,UserCheck,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building,Check,Edit,FileText,Plus,Save,Settings,Shield,Trash2,UserCheck,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building,Check,Edit,FileText,Plus,Save,Settings,Shield,Trash2,UserCheck,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building,Check,Edit,FileText,Plus,Save,Settings,Shield,Trash2,UserCheck,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building,Check,Edit,FileText,Plus,Save,Settings,Shield,Trash2,UserCheck,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building,Check,Edit,FileText,Plus,Save,Settings,Shield,Trash2,UserCheck,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building,Check,Edit,FileText,Plus,Save,Settings,Shield,Trash2,UserCheck,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building,Check,Edit,FileText,Plus,Save,Settings,Shield,Trash2,UserCheck,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building,Check,Edit,FileText,Plus,Save,Settings,Shield,Trash2,UserCheck,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building,Check,Edit,FileText,Plus,Save,Settings,Shield,Trash2,UserCheck,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building,Check,Edit,FileText,Plus,Save,Settings,Shield,Trash2,UserCheck,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building,Check,Edit,FileText,Plus,Save,Settings,Shield,Trash2,UserCheck,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building,Check,Edit,FileText,Plus,Save,Settings,Shield,Trash2,UserCheck,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building,Check,Edit,FileText,Plus,Save,Settings,Shield,Trash2,UserCheck,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Briefcase,Building,Check,Edit,FileText,Plus,Save,Settings,Shield,Trash2,UserCheck,Users,Wallet,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction PermissionsPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('roles');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [permissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        // Dashboard Permissions\n        {\n            id: 'dashboard_view',\n            name: 'عرض لوحة التحكم',\n            description: 'عرض لوحة التحكم الرئيسية',\n            module: 'dashboard',\n            action: 'view'\n        },\n        // Accounting Permissions\n        {\n            id: 'invoices_view',\n            name: 'عرض الفواتير',\n            description: 'عرض قائمة الفواتير',\n            module: 'accounting',\n            action: 'view'\n        },\n        {\n            id: 'invoices_create',\n            name: 'إنشاء الفواتير',\n            description: 'إنشاء فواتير جديدة',\n            module: 'accounting',\n            action: 'create'\n        },\n        {\n            id: 'invoices_edit',\n            name: 'تعديل الفواتير',\n            description: 'تعديل الفواتير الموجودة',\n            module: 'accounting',\n            action: 'edit'\n        },\n        {\n            id: 'invoices_delete',\n            name: 'حذف الفواتير',\n            description: 'حذف الفواتير',\n            module: 'accounting',\n            action: 'delete'\n        },\n        {\n            id: 'expenses_view',\n            name: 'عرض المصروفات',\n            description: 'عرض قائمة المصروفات',\n            module: 'accounting',\n            action: 'view'\n        },\n        {\n            id: 'expenses_create',\n            name: 'إنشاء المصروفات',\n            description: 'إنشاء مصروفات جديدة',\n            module: 'accounting',\n            action: 'create'\n        },\n        {\n            id: 'expenses_edit',\n            name: 'تعديل المصروفات',\n            description: 'تعديل المصروفات الموجودة',\n            module: 'accounting',\n            action: 'edit'\n        },\n        {\n            id: 'expenses_delete',\n            name: 'حذف المصروفات',\n            description: 'حذف المصروفات',\n            module: 'accounting',\n            action: 'delete'\n        },\n        // HR Permissions\n        {\n            id: 'hr_view',\n            name: 'عرض الموارد البشرية',\n            description: 'عرض بيانات الموظفين',\n            module: 'hr',\n            action: 'view'\n        },\n        {\n            id: 'hr_create',\n            name: 'إضافة موظفين',\n            description: 'إضافة موظفين جدد',\n            module: 'hr',\n            action: 'create'\n        },\n        {\n            id: 'hr_edit',\n            name: 'تعديل بيانات الموظفين',\n            description: 'تعديل بيانات الموظفين',\n            module: 'hr',\n            action: 'edit'\n        },\n        {\n            id: 'hr_delete',\n            name: 'حذف الموظفين',\n            description: 'حذف الموظفين',\n            module: 'hr',\n            action: 'delete'\n        },\n        {\n            id: 'payroll_view',\n            name: 'عرض الرواتب',\n            description: 'عرض بيانات الرواتب',\n            module: 'hr',\n            action: 'view'\n        },\n        {\n            id: 'payroll_process',\n            name: 'معالجة الرواتب',\n            description: 'معالجة وصرف الرواتب',\n            module: 'hr',\n            action: 'process'\n        },\n        // Banking Permissions\n        {\n            id: 'banking_view',\n            name: 'عرض البنوك',\n            description: 'عرض الحسابات البنكية',\n            module: 'banking',\n            action: 'view'\n        },\n        {\n            id: 'banking_create',\n            name: 'إضافة حسابات بنكية',\n            description: 'إضافة حسابات بنكية جديدة',\n            module: 'banking',\n            action: 'create'\n        },\n        {\n            id: 'banking_edit',\n            name: 'تعديل الحسابات البنكية',\n            description: 'تعديل الحسابات البنكية',\n            module: 'banking',\n            action: 'edit'\n        },\n        {\n            id: 'banking_delete',\n            name: 'حذف الحسابات البنكية',\n            description: 'حذف الحسابات البنكية',\n            module: 'banking',\n            action: 'delete'\n        },\n        // Treasury Permissions\n        {\n            id: 'treasury_view',\n            name: 'عرض الخزينة',\n            description: 'عرض الصناديق النقدية',\n            module: 'treasury',\n            action: 'view'\n        },\n        {\n            id: 'treasury_create',\n            name: 'إضافة صناديق نقدية',\n            description: 'إضافة صناديق نقدية جديدة',\n            module: 'treasury',\n            action: 'create'\n        },\n        {\n            id: 'treasury_edit',\n            name: 'تعديل الصناديق النقدية',\n            description: 'تعديل الصناديق النقدية',\n            module: 'treasury',\n            action: 'edit'\n        },\n        {\n            id: 'treasury_delete',\n            name: 'حذف الصناديق النقدية',\n            description: 'حذف الصناديق النقدية',\n            module: 'treasury',\n            action: 'delete'\n        },\n        // Assets Permissions\n        {\n            id: 'assets_view',\n            name: 'عرض الأصول',\n            description: 'عرض قائمة الأصول',\n            module: 'assets',\n            action: 'view'\n        },\n        {\n            id: 'assets_create',\n            name: 'إضافة أصول',\n            description: 'إضافة أصول جديدة',\n            module: 'assets',\n            action: 'create'\n        },\n        {\n            id: 'assets_edit',\n            name: 'تعديل الأصول',\n            description: 'تعديل الأصول الموجودة',\n            module: 'assets',\n            action: 'edit'\n        },\n        {\n            id: 'assets_delete',\n            name: 'حذف الأصول',\n            description: 'حذف الأصول',\n            module: 'assets',\n            action: 'delete'\n        },\n        // Settings Permissions\n        {\n            id: 'settings_view',\n            name: 'عرض الإعدادات',\n            description: 'عرض إعدادات النظام',\n            module: 'settings',\n            action: 'view'\n        },\n        {\n            id: 'settings_edit',\n            name: 'تعديل الإعدادات',\n            description: 'تعديل إعدادات النظام',\n            module: 'settings',\n            action: 'edit'\n        },\n        {\n            id: 'users_manage',\n            name: 'إدارة المستخدمين',\n            description: 'إدارة المستخدمين والصلاحيات',\n            module: 'settings',\n            action: 'manage'\n        },\n        // Reports Permissions\n        {\n            id: 'reports_view',\n            name: 'عرض التقارير',\n            description: 'عرض جميع التقارير',\n            module: 'reports',\n            action: 'view'\n        },\n        {\n            id: 'reports_export',\n            name: 'تصدير التقارير',\n            description: 'تصدير التقارير',\n            module: 'reports',\n            action: 'export'\n        }\n    ]);\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 'admin',\n            name: 'مدير النظام',\n            description: 'صلاحيات كاملة لجميع أجزاء النظام',\n            permissions: permissions.map({\n                \"PermissionsPage.useState\": (p)=>p.id\n            }[\"PermissionsPage.useState\"]),\n            userCount: 1,\n            isSystem: true\n        },\n        {\n            id: 'manager',\n            name: 'مدير',\n            description: 'صلاحيات إدارية للمحاسبة والموارد البشرية',\n            permissions: [\n                'dashboard_view',\n                'invoices_view',\n                'invoices_create',\n                'invoices_edit',\n                'expenses_view',\n                'expenses_create',\n                'expenses_edit',\n                'hr_view',\n                'hr_create',\n                'hr_edit',\n                'payroll_view',\n                'payroll_process',\n                'banking_view',\n                'banking_create',\n                'banking_edit',\n                'treasury_view',\n                'treasury_create',\n                'treasury_edit',\n                'assets_view',\n                'assets_create',\n                'assets_edit',\n                'reports_view',\n                'reports_export'\n            ],\n            userCount: 1,\n            isSystem: true\n        },\n        {\n            id: 'accountant',\n            name: 'محاسب',\n            description: 'صلاحيات المحاسبة والتقارير المالية',\n            permissions: [\n                'dashboard_view',\n                'invoices_view',\n                'invoices_create',\n                'invoices_edit',\n                'expenses_view',\n                'expenses_create',\n                'expenses_edit',\n                'banking_view',\n                'treasury_view',\n                'assets_view',\n                'reports_view'\n            ],\n            userCount: 1,\n            isSystem: true\n        },\n        {\n            id: 'hr_specialist',\n            name: 'أخصائي موارد بشرية',\n            description: 'صلاحيات الموارد البشرية والرواتب',\n            permissions: [\n                'dashboard_view',\n                'hr_view',\n                'hr_create',\n                'hr_edit',\n                'payroll_view',\n                'payroll_process',\n                'reports_view'\n            ],\n            userCount: 1,\n            isSystem: false\n        }\n    ]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            name: 'أحمد محمد',\n            email: '<EMAIL>',\n            role: 'admin',\n            permissions: [],\n            isActive: true\n        },\n        {\n            id: '2',\n            name: 'فاطمة أحمد',\n            email: '<EMAIL>',\n            role: 'manager',\n            permissions: [],\n            isActive: true\n        },\n        {\n            id: '3',\n            name: 'خالد علي',\n            email: '<EMAIL>',\n            role: 'accountant',\n            permissions: [],\n            isActive: true\n        },\n        {\n            id: '4',\n            name: 'سارة محمود',\n            email: '<EMAIL>',\n            role: 'hr_specialist',\n            permissions: [\n                'reports_export'\n            ],\n            isActive: true\n        }\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PermissionsPage.useEffect\": ()=>{\n            setLoading(false);\n        }\n    }[\"PermissionsPage.useEffect\"], []);\n    const getModuleIcon = (module)=>{\n        switch(module){\n            case 'dashboard':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 16\n                }, this);\n            case 'accounting':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 16\n                }, this);\n            case 'hr':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 16\n                }, this);\n            case 'banking':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 16\n                }, this);\n            case 'treasury':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 16\n                }, this);\n            case 'assets':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 16\n                }, this);\n            case 'settings':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 16\n                }, this);\n            case 'reports':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getModuleColor = (module)=>{\n        switch(module){\n            case 'dashboard':\n                return 'bg-blue-100 text-blue-800';\n            case 'accounting':\n                return 'bg-green-100 text-green-800';\n            case 'hr':\n                return 'bg-purple-100 text-purple-800';\n            case 'banking':\n                return 'bg-indigo-100 text-indigo-800';\n            case 'treasury':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'assets':\n                return 'bg-orange-100 text-orange-800';\n            case 'settings':\n                return 'bg-gray-100 text-gray-800';\n            case 'reports':\n                return 'bg-pink-100 text-pink-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const handleRolePermissionToggle = (roleId, permissionId)=>{\n        setRoles(roles.map((role)=>{\n            if (role.id === roleId && !role.isSystem) {\n                const hasPermission = role.permissions.includes(permissionId);\n                return {\n                    ...role,\n                    permissions: hasPermission ? role.permissions.filter((p)=>p !== permissionId) : [\n                        ...role.permissions,\n                        permissionId\n                    ]\n                };\n            }\n            return role;\n        }));\n    };\n    const handleUserPermissionToggle = (userId, permissionId)=>{\n        setUsers(users.map((user)=>{\n            if (user.id === userId) {\n                const hasPermission = user.permissions.includes(permissionId);\n                return {\n                    ...user,\n                    permissions: hasPermission ? user.permissions.filter((p)=>p !== permissionId) : [\n                        ...user.permissions,\n                        permissionId\n                    ]\n                };\n            }\n            return user;\n        }));\n    };\n    const getUserEffectivePermissions = (user)=>{\n        const role = roles.find((r)=>r.id === user.role);\n        const rolePermissions = role ? role.permissions : [];\n        return [\n            ...new Set([\n                ...rolePermissions,\n                ...user.permissions\n            ])\n        ];\n    };\n    const hasPermission = (user, permissionId)=>{\n        const effectivePermissions = getUserEffectivePermissions(user);\n        return effectivePermissions.includes(permissionId);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n            lineNumber: 295,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/settings'),\n                            className: \"flex items-center gap-2 text-gray-600 hover:text-gray-900\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                \"العودة للإعدادات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"إدارة الصلاحيات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-gray-600\",\n                                    children: \"إدارة الأدوار والصلاحيات للمستخدمين\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        \"aria-label\": \"Tabs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('roles'),\n                                className: `${activeTab === 'roles' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"الأدوار\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('users'),\n                                className: `${activeTab === 'users' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"المستخدمين\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('permissions'),\n                                className: `${activeTab === 'permissions' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"الصلاحيات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this),\n                activeTab === 'roles' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"إدارة الأدوار\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"إضافة دور جديد\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: roles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium text-gray-900 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    role.name,\n                                                                    role.isSystem && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\",\n                                                                        children: \"نظام\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mt-1\",\n                                                                children: role.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-2\",\n                                                                children: [\n                                                                    role.userCount,\n                                                                    \" مستخدم • \",\n                                                                    role.permissions.length,\n                                                                    \" صلاحية\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    !role.isSystem && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"text-blue-600 hover:text-blue-900\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"text-red-600 hover:text-red-900\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: \"الصلاحيات:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-h-48 overflow-y-auto space-y-2\",\n                                                        children: permissions.map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between py-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: `inline-flex px-2 py-1 text-xs rounded-full ${getModuleColor(permission.module)}`,\n                                                                                children: [\n                                                                                    getModuleIcon(permission.module),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-1\",\n                                                                                        children: permission.module\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                                        lineNumber: 410,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                                lineNumber: 408,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-700\",\n                                                                                children: permission.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                                lineNumber: 412,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: role.isSystem ? role.permissions.includes(permission.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                            lineNumber: 417,\n                                                                            columnNumber: 35\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                            lineNumber: 419,\n                                                                            columnNumber: 35\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: role.permissions.includes(permission.id),\n                                                                            onChange: ()=>handleRolePermissionToggle(role.id, permission.id),\n                                                                            className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                            lineNumber: 422,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, permission.id, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 21\n                                            }, this),\n                                            !role.isSystem && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 pt-4 border-t border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        \"حفظ التغييرات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 19\n                                    }, this)\n                                }, role.id, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 11\n                }, this),\n                activeTab === 'users' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: \"صلاحيات المستخدمين\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: users.map((user)=>{\n                                const userRole = roles.find((r)=>r.id === user.role);\n                                const effectivePermissions = getUserEffectivePermissions(user);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                user.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: user.email\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\",\n                                                                    children: userRole?.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                                                                    children: user.isActive ? 'نشط' : 'غير نشط'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: [\n                                                            \"الصلاحيات الفعالة (\",\n                                                            effectivePermissions.length,\n                                                            \"):\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-h-48 overflow-y-auto space-y-2\",\n                                                        children: permissions.map((permission)=>{\n                                                            const hasFromRole = userRole?.permissions.includes(permission.id) || false;\n                                                            const hasFromUser = user.permissions.includes(permission.id);\n                                                            const hasEffective = hasPermission(user, permission.id);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between py-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: `inline-flex px-2 py-1 text-xs rounded-full ${getModuleColor(permission.module)}`,\n                                                                                children: [\n                                                                                    getModuleIcon(permission.module),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-1\",\n                                                                                        children: permission.module\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                                        lineNumber: 497,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                                lineNumber: 495,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-700\",\n                                                                                children: permission.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                                lineNumber: 499,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            hasFromRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-blue-600\",\n                                                                                children: \"(من الدور)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                                lineNumber: 501,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                        lineNumber: 494,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            hasEffective && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-green-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                                lineNumber: 506,\n                                                                                columnNumber: 37\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: hasFromUser,\n                                                                                onChange: ()=>handleUserPermissionToggle(user.id, permission.id),\n                                                                                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\",\n                                                                                title: \"صلاحية إضافية للمستخدم\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                                lineNumber: 508,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                        lineNumber: 504,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, permission.id, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 31\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 pt-4 border-t border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Briefcase_Building_Check_Edit_FileText_Plus_Save_Settings_Shield_Trash2_UserCheck_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        \"حفظ صلاحيات المستخدم\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 21\n                                    }, this)\n                                }, user.id, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 451,\n                    columnNumber: 11\n                }, this),\n                activeTab === 'permissions' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: \"جميع الصلاحيات\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"الصلاحية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"الوحدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"الوصف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"الأدوار المخولة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: permissions.map((permission)=>{\n                                                const authorizedRoles = roles.filter((role)=>role.permissions.includes(permission.id));\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: permission.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: permission.id\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `inline-flex px-2 py-1 text-xs rounded-full ${getModuleColor(permission.module)}`,\n                                                                children: [\n                                                                    getModuleIcon(permission.module),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-1\",\n                                                                        children: permission.module\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: permission.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1\",\n                                                                children: authorizedRoles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\",\n                                                                        children: role.name\n                                                                    }, role.id, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                        lineNumber: 583,\n                                                                        columnNumber: 33\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, permission.id, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 537,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-blue-900 mb-2\",\n                            children: \"نصائح لإدارة الصلاحيات:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                            lineNumber: 604,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-sm text-blue-800 space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• امنح كل مستخدم الصلاحيات المناسبة لدوره فقط\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• استخدم الأدوار لتجميع الصلاحيات المتشابهة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• يمكن إضافة صلاحيات إضافية للمستخدمين عند الحاجة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• راجع الصلاحيات دورياً وأزل غير المستخدمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• الأدوار النظام محمية ولا يمكن تعديلها\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                            lineNumber: 605,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n                    lineNumber: 603,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n            lineNumber: 305,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\permissions\\\\page.tsx\",\n        lineNumber: 304,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/settings/permissions/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/MainLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/Layout/MainLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MainLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/Layout/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction MainLayout({ children }) {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"MainLayout.useEffect\": ()=>{\n            if (status === 'loading') return; // Still loading\n            if (!session) {\n                router.push('/auth/signin');\n                return;\n            }\n        }\n    }[\"MainLayout.useEffect\"], [\n        session,\n        status,\n        router\n    ]);\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-100\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/MainLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/Layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst navigation = [\n    {\n        name: 'لوحة التحكم',\n        href: '/',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: 'العملاء',\n        href: '/customers',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'الفواتير',\n        href: '/invoices',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'المنتجات',\n        href: '/products',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'المصروفات',\n        href: '/expenses',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'البنوك',\n        href: '/banking',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'الخزينة',\n        href: '/treasury',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: 'الأصول',\n        href: '/assets',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: 'الموارد البشرية',\n        href: '/hr',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: 'التقارير',\n        href: '/reports',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        name: 'المستخدمين',\n        href: '/users',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        adminOnly: true\n    },\n    {\n        name: 'الإعدادات',\n        href: '/settings',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const handleSignOut = ()=>{\n        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)({\n            callbackUrl: '/auth/signin'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full w-64 flex-col bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center justify-center bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-bold text-white\",\n                    children: \"نظام المحاسبة\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 space-y-1 px-2 py-4\",\n                children: navigation.map((item)=>{\n                    // Hide admin-only items for non-admin users\n                    if (item.adminOnly && session?.user?.role !== 'ADMIN') {\n                        return null;\n                    }\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: item.href,\n                        className: `\n                group flex items-center px-2 py-2 text-sm font-medium rounded-md\n                ${isActive ? 'bg-gray-800 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'}\n              `,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: `\n                  mr-3 h-5 w-5 flex-shrink-0\n                  ${isActive ? 'text-white' : 'text-gray-400 group-hover:text-white'}\n                `\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 15\n                            }, this),\n                            item.name\n                        ]\n                    }, item.name, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-700 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-full bg-gray-600 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-white\",\n                                        children: session?.user?.name || session?.user?.email\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: session?.user?.role === 'ADMIN' ? 'مشرف' : session?.user?.role === 'MANAGER' ? 'مدير' : 'محاسب'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSignOut,\n                        className: \"mt-3 w-full flex items-center px-2 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-gray-700 hover:text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"mr-3 h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            \"تسجيل الخروج\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/Providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Providers\\\\SessionProvider.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Qcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNEU7QUFNN0QsU0FBU0EsZ0JBQWdCLEVBQUVFLFFBQVEsRUFBd0I7SUFDeEUscUJBQ0UsOERBQUNELDREQUF1QkE7a0JBQ3JCQzs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkQ6XFxBY2NvdW50aW5nXFxhY2NvdW50aW5nLXN5c3RlbVxcc3JjXFxjb21wb25lbnRzXFxQcm92aWRlcnNcXFNlc3Npb25Qcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciBhcyBOZXh0QXV0aFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcblxuaW50ZXJmYWNlIFNlc3Npb25Qcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTZXNzaW9uUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiBTZXNzaW9uUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxOZXh0QXV0aFNlc3Npb25Qcm92aWRlcj5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L05leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Providers/SessionProvider.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsettings%2Fpermissions%2Fpage&page=%2Fsettings%2Fpermissions%2Fpage&appPaths=%2Fsettings%2Fpermissions%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpermissions%2Fpage.tsx&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();