'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import MainLayout from '@/components/Layout/MainLayout'
import { Plus, Search, Edit, Trash2, Eye, Wallet, DollarSign, MapPin, User, AlertTriangle, CheckCircle } from 'lucide-react'

interface CashBox {
  id: string
  name: string
  description?: string
  currency: string
  balance: number
  location?: string
  responsible?: string
  isActive: boolean
  dailyLimit?: number
  notes?: string
  createdAt: string
}

export default function CashBoxesPage() {
  const [cashBoxes, setCashBoxes] = useState<CashBox[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('ALL')

  useEffect(() => {
    const fetchCashBoxes = async () => {
      try {
        const response = await fetch('/api/treasury/cash-boxes')
        if (response.ok) {
          const data = await response.json()
          setCashBoxes(data)
        } else {
          // Fallback to demo data if API fails
          setCashBoxes([
            {
              id: '1',
              name: 'الصندوق الرئيسي',
              description: 'الصندوق الرئيسي للشركة',
              currency: 'SAR',
              balance: 25000,
              location: 'مكتب المحاسبة',
              responsible: 'أحمد محمد',
              isActive: true,
              dailyLimit: 50000,
              notes: 'الصندوق الأساسي لجميع المعاملات',
              createdAt: '2024-01-15'
            },
            {
              id: '2',
              name: 'صندوق الاستقبال',
              description: 'صندوق استقبال العملاء',
              currency: 'SAR',
              balance: 8500,
              location: 'مكتب الاستقبال',
              responsible: 'فاطمة أحمد',
              isActive: true,
              dailyLimit: 20000,
              notes: 'لاستقبال مدفوعات العملاء',
              createdAt: '2024-02-01'
            },
            {
              id: '3',
              name: 'صندوق المصروفات النثرية',
              description: 'للمصروفات الصغيرة والنثرية',
              currency: 'SAR',
              balance: 2500,
              location: 'مكتب الإدارة',
              responsible: 'خالد علي',
              isActive: true,
              dailyLimit: 5000,
              notes: 'للمصروفات اليومية الصغيرة',
              createdAt: '2024-02-15'
            },
            {
              id: '4',
              name: 'صندوق الطوارئ',
              description: 'صندوق احتياطي للطوارئ',
              currency: 'SAR',
              balance: 10000,
              location: 'الخزنة الرئيسية',
              responsible: 'سارة محمود',
              isActive: false,
              dailyLimit: 100000,
              notes: 'للاستخدام في الحالات الطارئة فقط',
              createdAt: '2024-01-01'
            }
          ])
        }
      } catch (error) {
        console.error('Error fetching cash boxes:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchCashBoxes()
  }, [])

  const filteredCashBoxes = cashBoxes.filter(cashBox => {
    const matchesSearch = 
      cashBox.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cashBox.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cashBox.location?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cashBox.responsible?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'ALL' || 
      (statusFilter === 'ACTIVE' && cashBox.isActive) ||
      (statusFilter === 'INACTIVE' && !cashBox.isActive)
    
    return matchesSearch && matchesStatus
  })

  const handleDeleteCashBox = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا الصندوق النقدي؟')) {
      try {
        const response = await fetch(`/api/treasury/cash-boxes/${id}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          setCashBoxes(cashBoxes.filter(box => box.id !== id))
          alert('تم حذف الصندوق النقدي بنجاح!')
        } else {
          alert('حدث خطأ أثناء حذف الصندوق النقدي')
        }
      } catch (error) {
        console.error('Error deleting cash box:', error)
        alert('حدث خطأ أثناء حذف الصندوق النقدي')
      }
    }
  }

  const getStatusColor = (isActive: boolean) => {
    return isActive 
      ? 'bg-green-100 text-green-800'
      : 'bg-red-100 text-red-800'
  }

  const getStatusLabel = (isActive: boolean) => {
    return isActive ? 'نشط' : 'غير نشط'
  }

  const isLowBalance = (balance: number, dailyLimit?: number) => {
    if (!dailyLimit) return false
    return balance < (dailyLimit * 0.1) // Less than 10% of daily limit
  }

  const totalBalance = filteredCashBoxes.reduce((sum, box) => sum + box.balance, 0)
  const activeCashBoxes = filteredCashBoxes.filter(box => box.isActive).length
  const lowBalanceBoxes = filteredCashBoxes.filter(box => 
    box.isActive && isLowBalance(box.balance, box.dailyLimit)
  ).length

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">الصناديق النقدية</h1>
            <p className="mt-2 text-gray-600">إدارة الصناديق النقدية وأرصدتها</p>
          </div>
          <Link
            href="/treasury/cash-boxes/new"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
          >
            <Plus className="h-5 w-5" />
            إضافة صندوق نقدي
          </Link>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-500 p-3 rounded-lg">
                <Wallet className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الصناديق</p>
                <p className="text-2xl font-bold text-blue-600">{filteredCashBoxes.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-500 p-3 rounded-lg">
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الصناديق النشطة</p>
                <p className="text-2xl font-bold text-green-600">{activeCashBoxes}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-purple-500 p-3 rounded-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الأرصدة</p>
                <p className="text-xl font-bold text-purple-600">
                  {totalBalance.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-red-500 p-3 rounded-lg">
                <AlertTriangle className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">رصيد منخفض</p>
                <p className="text-2xl font-bold text-red-600">{lowBalanceBoxes}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="البحث في الصناديق..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="ALL">جميع الحالات</option>
              <option value="ACTIVE">نشط</option>
              <option value="INACTIVE">غير نشط</option>
            </select>
          </div>
        </div>

        {/* Cash Boxes Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCashBoxes.map((cashBox) => (
            <div key={cashBox.id} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow duration-200">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <div className="bg-blue-100 p-3 rounded-lg">
                      <Wallet className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="mr-3">
                      <h3 className="text-lg font-medium text-gray-900">{cashBox.name}</h3>
                      <p className="text-sm text-gray-500">{cashBox.description}</p>
                    </div>
                  </div>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(cashBox.isActive)}`}>
                    {getStatusLabel(cashBox.isActive)}
                  </span>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">الرصيد الحالي:</span>
                    <div className="flex items-center">
                      <span className="text-lg font-bold text-gray-900">
                        {cashBox.balance.toLocaleString()} {cashBox.currency}
                      </span>
                      {isLowBalance(cashBox.balance, cashBox.dailyLimit) && (
                        <AlertTriangle className="h-4 w-4 text-red-500 mr-2" />
                      )}
                    </div>
                  </div>

                  {cashBox.dailyLimit && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">الحد اليومي:</span>
                      <span className="text-sm font-medium text-gray-700">
                        {cashBox.dailyLimit.toLocaleString()} ر.س
                      </span>
                    </div>
                  )}

                  {cashBox.location && (
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 text-gray-400 ml-2" />
                      <span className="text-sm text-gray-600">{cashBox.location}</span>
                    </div>
                  )}

                  {cashBox.responsible && (
                    <div className="flex items-center">
                      <User className="h-4 w-4 text-gray-400 ml-2" />
                      <span className="text-sm text-gray-600">{cashBox.responsible}</span>
                    </div>
                  )}

                  {cashBox.notes && (
                    <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                      <p className="text-xs text-gray-600">{cashBox.notes}</p>
                    </div>
                  )}
                </div>

                <div className="mt-6 flex justify-between items-center pt-4 border-t border-gray-200">
                  <div className="flex gap-2">
                    <Link
                      href={`/treasury/cash-boxes/${cashBox.id}`}
                      className="text-blue-600 hover:text-blue-900"
                      title="عرض التفاصيل"
                    >
                      <Eye className="h-4 w-4" />
                    </Link>
                    <Link
                      href={`/treasury/cash-boxes/${cashBox.id}/transactions`}
                      className="text-green-600 hover:text-green-900"
                      title="المعاملات"
                    >
                      <DollarSign className="h-4 w-4" />
                    </Link>
                    <Link
                      href={`/treasury/cash-boxes/${cashBox.id}/edit`}
                      className="text-yellow-600 hover:text-yellow-900"
                      title="تعديل"
                    >
                      <Edit className="h-4 w-4" />
                    </Link>
                    <button 
                      onClick={() => handleDeleteCashBox(cashBox.id)}
                      className="text-red-600 hover:text-red-900"
                      title="حذف"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                  
                  <div className="text-xs text-gray-500">
                    {new Date(cashBox.createdAt).toLocaleDateString('ar-SA')}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {filteredCashBoxes.length === 0 && (
          <div className="text-center py-12">
            <Wallet className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد صناديق نقدية</h3>
            <p className="mt-1 text-sm text-gray-500">ابدأ بإضافة صندوق نقدي جديد</p>
            <div className="mt-6">
              <Link
                href="/treasury/cash-boxes/new"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 ml-2" />
                إضافة صندوق نقدي
              </Link>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
