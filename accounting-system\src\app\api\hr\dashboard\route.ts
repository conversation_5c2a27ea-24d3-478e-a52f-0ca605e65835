import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get employee statistics
    const totalEmployees = await prisma.employee.count()
    const activeEmployees = await prisma.employee.count({
      where: { status: 'ACTIVE' }
    })

    // Get unique departments count
    const departments = await prisma.employee.findMany({
      select: { department: true },
      distinct: ['department']
    })
    const departmentsCount = departments.length

    // Get leave statistics
    const pendingLeaves = await prisma.leave.count({
      where: { status: 'PENDING' }
    })

    // Get salary statistics
    const pendingSalaries = await prisma.salary.count({
      where: { status: 'PENDING' }
    })

    const currentMonth = new Date().getMonth() + 1
    const currentYear = new Date().getFullYear()

    const currentMonthSalaries = await prisma.salary.findMany({
      where: {
        month: currentMonth,
        year: currentYear
      }
    })

    const totalSalariesAmount = currentMonthSalaries.reduce(
      (sum, salary) => sum + salary.netSalary, 
      0
    )

    const stats = {
      totalEmployees,
      activeEmployees,
      pendingLeaves,
      pendingSalaries,
      totalSalariesAmount,
      departmentsCount
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching HR dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
