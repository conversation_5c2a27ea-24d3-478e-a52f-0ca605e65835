import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const sortBy = searchParams.get('sortBy') || 'issueDate'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const skip = (page - 1) * limit

    // Build where clause
    let whereClause: any = {}

    if (search) {
      whereClause.OR = [
        { number: { contains: search, mode: 'insensitive' } },
        { customerName: { contains: search, mode: 'insensitive' } },
        { customerEmail: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (status && status !== 'ALL') {
      whereClause.status = status
    }

    // Get invoices with pagination
    const [invoices, total] = await Promise.all([
      prisma.invoice.findMany({
        where: whereClause,
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
        include: {
          items: {
            select: {
              id: true,
              description: true,
              quantity: true,
              unitPrice: true,
              total: true
            }
          }
        }
      }),
      prisma.invoice.count({ where: whereClause })
    ])

    // Calculate statistics
    const allInvoices = await prisma.invoice.findMany({
      select: {
        status: true,
        total: true
      }
    })

    const stats = {
      totalInvoices: allInvoices.length,
      totalAmount: allInvoices.reduce((sum, inv) => sum + inv.total, 0),
      byStatus: {
        DRAFT: {
          count: allInvoices.filter(inv => inv.status === 'DRAFT').length,
          amount: allInvoices.filter(inv => inv.status === 'DRAFT').reduce((sum, inv) => sum + inv.total, 0)
        },
        SENT: {
          count: allInvoices.filter(inv => inv.status === 'SENT').length,
          amount: allInvoices.filter(inv => inv.status === 'SENT').reduce((sum, inv) => sum + inv.total, 0)
        },
        PAID: {
          count: allInvoices.filter(inv => inv.status === 'PAID').length,
          amount: allInvoices.filter(inv => inv.status === 'PAID').reduce((sum, inv) => sum + inv.total, 0)
        },
        OVERDUE: {
          count: allInvoices.filter(inv => inv.status === 'OVERDUE').length,
          amount: allInvoices.filter(inv => inv.status === 'OVERDUE').reduce((sum, inv) => sum + inv.total, 0)
        },
        CANCELLED: {
          count: allInvoices.filter(inv => inv.status === 'CANCELLED').length,
          amount: allInvoices.filter(inv => inv.status === 'CANCELLED').reduce((sum, inv) => sum + inv.total, 0)
        }
      }
    }

    return NextResponse.json({
      invoices,
      stats,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching invoices:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      number,
      customerId,
      issueDate,
      dueDate,
      status,
      subtotal,
      taxAmount,
      total,
      notes,
      items
    } = body

    // Validate required fields
    if (!customerId || !items || items.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Create invoice with items
    const invoice = await prisma.invoice.create({
      data: {
        number,
        customerId,
        userId: session.user.id,
        issueDate: new Date(issueDate),
        dueDate: new Date(dueDate),
        status,
        subtotal,
        taxAmount,
        total,
        notes,
        items: {
          create: items.map((item: any) => ({
            productId: item.productId,
            quantity: item.quantity,
            price: item.price,
            total: item.total
          }))
        }
      },
      include: {
        customer: true,
        user: true,
        items: {
          include: {
            product: true
          }
        }
      }
    })

    return NextResponse.json(invoice, { status: 201 })
  } catch (error) {
    console.error('Error creating invoice:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
