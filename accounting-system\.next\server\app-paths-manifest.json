{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/products/route": "app/api/products/route.js", "/page": "app/page.js", "/settings/page": "app/settings/page.js", "/products/page": "app/products/page.js", "/customers/page": "app/customers/page.js", "/expenses/page": "app/expenses/page.js", "/invoices/page": "app/invoices/page.js", "/products/new/page": "app/products/new/page.js"}