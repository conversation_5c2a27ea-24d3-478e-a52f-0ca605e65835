/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/assets/route";
exports.ids = ["app/api/assets/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fassets%2Froute&page=%2Fapi%2Fassets%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassets%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fassets%2Froute&page=%2Fapi%2Fassets%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassets%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Accounting_accounting_system_src_app_api_assets_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/assets/route.ts */ \"(rsc)/./src/app/api/assets/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/assets/route\",\n        pathname: \"/api/assets\",\n        filename: \"route\",\n        bundlePath: \"app/api/assets/route\"\n    },\n    resolvedPagePath: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\api\\\\assets\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Accounting_accounting_system_src_app_api_assets_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fassets%2Froute&page=%2Fapi%2Fassets%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassets%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/assets/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/assets/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const assets = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.asset.findMany({\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(assets);\n    } catch (error) {\n        console.error('Error fetching assets:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const formData = await request.formData();\n        // Extract form data\n        const assetCode = formData.get('assetCode');\n        const name = formData.get('name');\n        const description = formData.get('description');\n        const category = formData.get('category');\n        const subCategory = formData.get('subCategory');\n        const brand = formData.get('brand');\n        const model = formData.get('model');\n        const serialNumber = formData.get('serialNumber');\n        const purchaseDate = formData.get('purchaseDate');\n        const purchasePrice = formData.get('purchasePrice');\n        const depreciationRate = formData.get('depreciationRate');\n        const depreciationMethod = formData.get('depreciationMethod');\n        const usefulLife = formData.get('usefulLife');\n        const location = formData.get('location');\n        const department = formData.get('department');\n        const assignedTo = formData.get('assignedTo');\n        const condition = formData.get('condition');\n        const warrantyExpiry = formData.get('warrantyExpiry');\n        const insuranceExpiry = formData.get('insuranceExpiry');\n        const supplier = formData.get('supplier');\n        const invoiceNumber = formData.get('invoiceNumber');\n        const notes = formData.get('notes');\n        const imageFile = formData.get('image');\n        // Validate required fields\n        if (!assetCode || !name || !category || !purchaseDate || !purchasePrice) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // Validate purchase price\n        const price = parseFloat(purchasePrice);\n        if (price <= 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Purchase price must be greater than zero'\n            }, {\n                status: 400\n            });\n        }\n        // Handle image upload (simplified - in production, use cloud storage)\n        let imagePath = null;\n        if (imageFile && imageFile.size > 0) {\n            // For demo purposes, we'll just store the filename\n            // In production, upload to cloud storage and store the URL\n            imagePath = `assets/${Date.now()}-${imageFile.name}`;\n        }\n        // Calculate current value based on depreciation\n        const purchaseDateObj = new Date(purchaseDate);\n        const now = new Date();\n        const yearsElapsed = (now.getTime() - purchaseDateObj.getTime()) / (1000 * 60 * 60 * 24 * 365);\n        let currentValue = price;\n        if (depreciationRate && parseFloat(depreciationRate) > 0) {\n            const rate = parseFloat(depreciationRate) / 100;\n            if (depreciationMethod === 'STRAIGHT_LINE') {\n                currentValue = Math.max(0, price - price * rate * yearsElapsed);\n            } else if (depreciationMethod === 'DECLINING_BALANCE') {\n                currentValue = price * Math.pow(1 - rate, yearsElapsed);\n            }\n        }\n        const asset = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.asset.create({\n            data: {\n                assetCode,\n                name,\n                description: description || null,\n                category,\n                subCategory: subCategory || null,\n                brand: brand || null,\n                model: model || null,\n                serialNumber: serialNumber || null,\n                purchaseDate: new Date(purchaseDate),\n                purchasePrice: price,\n                currentValue: Math.round(currentValue * 100) / 100,\n                depreciationRate: depreciationRate ? parseFloat(depreciationRate) : 0,\n                depreciationMethod: depreciationMethod || 'STRAIGHT_LINE',\n                usefulLife: usefulLife ? parseInt(usefulLife) : 5,\n                location: location || null,\n                department: department || null,\n                assignedTo: assignedTo || null,\n                condition: condition || 'GOOD',\n                status: 'ACTIVE',\n                warrantyExpiry: warrantyExpiry ? new Date(warrantyExpiry) : null,\n                insuranceExpiry: insuranceExpiry ? new Date(insuranceExpiry) : null,\n                supplier: supplier || null,\n                invoiceNumber: invoiceNumber || null,\n                notes: notes || null,\n                image: imagePath\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(asset, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating asset:', error);\n        // Handle unique constraint violations\n        if (error instanceof Error && error.message.includes('Unique constraint')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Asset code already exists'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/assets/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user) {\n                    return null;\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"].compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        signUp: '/auth/signup'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkQ6XFxBY2NvdW50aW5nXFxhY2NvdW50aW5nLXN5c3RlbVxcc3JjXFxsaWJcXHByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/@next-auth","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fassets%2Froute&page=%2Fapi%2Fassets%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fassets%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();