'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import MainLayout from '@/components/Layout/MainLayout'
import { Plus, Search, Edit, Trash2, Eye, Download, Send, FileText } from 'lucide-react'

interface Invoice {
  id: string
  number: string
  issueDate: string
  dueDate: string
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED'
  total: number
  customer: {
    id: string
    name: string
    email: string
    phone: string
  }
  items: Array<{
    id: string
    productId: string
    quantity: number
    price: number
    total: number
    product: {
      name: string
      description: string
    }
  }>
}

const statusColors = {
  DRAFT: 'bg-gray-100 text-gray-800',
  SENT: 'bg-blue-100 text-blue-800',
  PAID: 'bg-green-100 text-green-800',
  OVERDUE: 'bg-red-100 text-red-800',
  CANCELLED: 'bg-red-100 text-red-800'
}

const statusLabels = {
  DRAFT: 'مسودة',
  SENT: 'مرسلة',
  PAID: 'مدفوعة',
  OVERDUE: 'متأخرة',
  CANCELLED: 'ملغية'
}

export default function InvoicesPage() {
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('ALL')
  const [stats, setStats] = useState<any>({})

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 500)

    return () => clearTimeout(timer)
  }, [searchTerm])

  useEffect(() => {
    const fetchInvoices = async () => {
      setLoading(true)
      try {
        const params = new URLSearchParams()
        if (debouncedSearchTerm) params.append('search', debouncedSearchTerm)
        if (statusFilter !== 'ALL') params.append('status', statusFilter)

        const response = await fetch(`/api/invoices?${params.toString()}`)
        if (response.ok) {
          const data = await response.json()
          setInvoices(data.invoices || [])
          setStats(data.stats || {})
        } else {
          // Fallback to demo data if API fails
          setInvoices([
            {
              id: '1',
              number: 'INV-001',
              issueDate: '2024-01-15',
              dueDate: '2024-02-15',
              status: 'PAID',
              total: 15000,
              customer: {
                id: '1',
                name: 'شركة الأمل للتجارة',
                email: '<EMAIL>',
                phone: '0501234567'
              },
              items: []
            },
            {
              id: '2',
              number: 'INV-002',
              issueDate: '2024-01-20',
              dueDate: '2024-02-20',
              status: 'SENT',
              total: 8500,
              customer: {
                id: '2',
                name: 'مؤسسة النور للخدمات',
                email: '<EMAIL>',
                phone: '0507654321'
              },
              items: []
            },
            {
              id: '3',
              number: 'INV-003',
              issueDate: '2024-02-01',
              dueDate: '2024-02-15',
              status: 'OVERDUE',
              total: 12000,
              customer: {
                id: '3',
                name: 'شركة الفجر للتطوير',
                email: '<EMAIL>',
                phone: '0501112233'
              },
              items: []
            },
            {
              id: '4',
              number: 'INV-004',
              issueDate: '2024-02-10',
              dueDate: '2024-03-10',
              status: 'DRAFT',
              total: 5500,
              customer: {
                id: '1',
                name: 'شركة الأمل للتجارة',
                email: '<EMAIL>',
                phone: '0501234567'
              },
              items: []
            }
          ])
          setStats({
            totalInvoices: 4,
            byStatus: {
              PAID: { count: 1, amount: 15000 },
              SENT: { count: 1, amount: 8500 },
              OVERDUE: { count: 1, amount: 12000 },
              DRAFT: { count: 1, amount: 5500 },
              CANCELLED: { count: 0, amount: 0 }
            }
          })
        }
      } catch (error) {
        console.error('Error fetching invoices:', error)
        setInvoices([])
        setStats({})
      } finally {
        setLoading(false)
      }
    }

    fetchInvoices()
  }, [debouncedSearchTerm, statusFilter])

  const filteredInvoices = invoices // API already handles filtering

  const handleDeleteInvoice = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
      try {
        const response = await fetch(`/api/invoices/${id}`, {
          method: 'DELETE'
        })
        if (response.ok) {
          setInvoices(invoices.filter(invoice => invoice.id !== id))
        } else {
          alert('حدث خطأ أثناء حذف الفاتورة')
        }
      } catch (error) {
        console.error('Error deleting invoice:', error)
        alert('حدث خطأ أثناء حذف الفاتورة')
      }
    }
  }

  const getTotalByStatus = (status: string) => {
    if (stats.byStatus && stats.byStatus[status]) {
      return stats.byStatus[status].amount
    }
    return 0
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة الفواتير</h1>
            <p className="mt-2 text-gray-600">إنشاء وإدارة فواتير العملاء</p>
          </div>
          <Link
            href="/invoices/new"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
          >
            <Plus className="h-5 w-5" />
            إنشاء فاتورة جديدة
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الفواتير</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalInvoices || 0}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.totalAmount ? `${stats.totalAmount.toLocaleString()} ر.س` : '0 ر.س'}
                </p>
              </div>
              <div className="bg-blue-500 p-3 rounded-lg">
                <FileText className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المدفوعة</p>
                <p className="text-2xl font-bold text-green-600">
                  {getTotalByStatus('PAID').toLocaleString()} ر.س
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.byStatus?.PAID?.count || 0} فاتورة
                </p>
              </div>
              <div className="bg-green-500 p-3 rounded-lg">
                <FileText className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المرسلة</p>
                <p className="text-2xl font-bold text-blue-600">
                  {getTotalByStatus('SENT').toLocaleString()} ر.س
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.byStatus?.SENT?.count || 0} فاتورة
                </p>
              </div>
              <div className="bg-blue-500 p-3 rounded-lg">
                <FileText className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المتأخرة</p>
                <p className="text-2xl font-bold text-red-600">
                  {getTotalByStatus('OVERDUE').toLocaleString()} ر.س
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.byStatus?.OVERDUE?.count || 0} فاتورة
                </p>
              </div>
              <div className="bg-red-500 p-3 rounded-lg">
                <FileText className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المسودات</p>
                <p className="text-2xl font-bold text-gray-600">
                  {getTotalByStatus('DRAFT').toLocaleString()} ر.س
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.byStatus?.DRAFT?.count || 0} فاتورة
                </p>
              </div>
              <div className="bg-gray-500 p-3 rounded-lg">
                <FileText className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="البحث في الفواتير..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {loading && searchTerm && (
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                </div>
              )}
            </div>
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="ALL">جميع الحالات</option>
              <option value="DRAFT">مسودة</option>
              <option value="SENT">مرسلة</option>
              <option value="PAID">مدفوعة</option>
              <option value="OVERDUE">متأخرة</option>
              <option value="CANCELLED">ملغية</option>
            </select>
            <div className="flex items-center gap-2">
              <button
                onClick={() => {
                  setSearchTerm('')
                  setStatusFilter('ALL')
                }}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                إعادة تعيين
              </button>
              <span className="text-sm text-gray-500">
                {filteredInvoices.length} من {stats.totalInvoices || 0} فاتورة
              </span>
            </div>
          </div>
        </div>

        {/* Invoices Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    رقم الفاتورة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    العميل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    تاريخ الإصدار
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    تاريخ الاستحقاق
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المبلغ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredInvoices.map((invoice) => (
                  <tr key={invoice.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {invoice.number}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{invoice.customer.name}</div>
                      {invoice.customer.email && (
                        <div className="text-xs text-gray-500">{invoice.customer.email}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {new Date(invoice.issueDate).toLocaleDateString('ar-SA')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {new Date(invoice.dueDate).toLocaleDateString('ar-SA')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColors[invoice.status]}`}>
                        {statusLabels[invoice.status]}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {invoice.total.toLocaleString()} ر.س
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <button className="text-blue-600 hover:text-blue-900" title="عرض">
                          <Eye className="h-4 w-4" />
                        </button>
                        <button className="text-green-600 hover:text-green-900" title="تحميل">
                          <Download className="h-4 w-4" />
                        </button>
                        <button className="text-purple-600 hover:text-purple-900" title="إرسال">
                          <Send className="h-4 w-4" />
                        </button>
                        <button className="text-yellow-600 hover:text-yellow-900" title="تعديل">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button 
                          onClick={() => handleDeleteInvoice(invoice.id)}
                          className="text-red-600 hover:text-red-900"
                          title="حذف"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredInvoices.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد فواتير مطابقة لبحثك</p>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  )
}
