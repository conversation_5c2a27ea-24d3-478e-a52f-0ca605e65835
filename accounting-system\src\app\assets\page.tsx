'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import MainLayout from '@/components/Layout/MainLayout'
import { 
  Package, 
  Plus, 
  Search, 
  Filter,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3
} from 'lucide-react'

interface AssetsStats {
  totalAssets: number
  activeAssets: number
  totalValue: number
  depreciatedValue: number
  maintenanceDue: number
  warrantyExpiring: number
  categories: number
}

export default function AssetsDashboard() {
  const [stats, setStats] = useState<AssetsStats>({
    totalAssets: 0,
    activeAssets: 0,
    totalValue: 0,
    depreciatedValue: 0,
    maintenanceDue: 0,
    warrantyExpiring: 0,
    categories: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/assets/dashboard')
        if (response.ok) {
          const data = await response.json()
          setStats(data)
        } else {
          // Fallback to demo data
          setStats({
            totalAssets: 125,
            activeAssets: 118,
            totalValue: 850000,
            depreciatedValue: 680000,
            maintenanceDue: 8,
            warrantyExpiring: 5,
            categories: 6
          })
        }
      } catch (error) {
        console.error('Error fetching assets stats:', error)
        // Use demo data as fallback
        setStats({
          totalAssets: 125,
          activeAssets: 118,
          totalValue: 850000,
          depreciatedValue: 680000,
          maintenanceDue: 8,
          warrantyExpiring: 5,
          categories: 6
        })
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  const quickActions = [
    {
      title: 'إضافة أصل جديد',
      description: 'تسجيل أصل جديد في النظام',
      icon: Plus,
      href: '/assets/new',
      color: 'bg-blue-500'
    },
    {
      title: 'جدولة صيانة',
      description: 'جدولة صيانة دورية للأصول',
      icon: Calendar,
      href: '/assets/maintenance/schedule',
      color: 'bg-green-500'
    },
    {
      title: 'نقل أصل',
      description: 'نقل أصل بين الأقسام أو المواقع',
      icon: Package,
      href: '/assets/transfers/new',
      color: 'bg-purple-500'
    },
    {
      title: 'تقارير الأصول',
      description: 'عرض تقارير شاملة عن الأصول',
      icon: BarChart3,
      href: '/assets/reports',
      color: 'bg-orange-500'
    }
  ]

  const assetModules = [
    {
      title: 'إدارة الأصول',
      description: 'عرض وإدارة جميع أصول الشركة',
      icon: Package,
      href: '/assets/list',
      stats: `${stats.totalAssets} أصل`,
      color: 'border-blue-500'
    },
    {
      title: 'الصيانة والإصلاح',
      description: 'إدارة جدولة وتتبع صيانة الأصول',
      icon: Settings,
      href: '/assets/maintenance',
      stats: `${stats.maintenanceDue} صيانة مستحقة`,
      color: 'border-green-500'
    },
    {
      title: 'الإهلاك والتقييم',
      description: 'حساب إهلاك الأصول والقيمة الحالية',
      icon: TrendingDown,
      href: '/assets/depreciation',
      stats: `${((stats.totalValue - stats.depreciatedValue) / stats.totalValue * 100).toFixed(1)}% إهلاك`,
      color: 'border-red-500'
    },
    {
      title: 'نقل الأصول',
      description: 'إدارة نقل الأصول بين الأقسام',
      icon: TrendingUp,
      href: '/assets/transfers',
      stats: 'تتبع النقل',
      color: 'border-purple-500'
    }
  ]

  const depreciationRate = stats.totalValue > 0 
    ? ((stats.totalValue - stats.depreciatedValue) / stats.totalValue * 100)
    : 0

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">لوحة تحكم الأصول</h1>
          <p className="mt-2 text-gray-600">إدارة شاملة لأصول الشركة والصيانة والإهلاك</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-500 p-3 rounded-lg">
                <Package className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الأصول</p>
                <p className="text-2xl font-bold text-blue-600">{stats.totalAssets}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-500 p-3 rounded-lg">
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الأصول النشطة</p>
                <p className="text-2xl font-bold text-green-600">{stats.activeAssets}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-purple-500 p-3 rounded-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">القيمة الإجمالية</p>
                <p className="text-xl font-bold text-purple-600">
                  {stats.totalValue.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-indigo-500 p-3 rounded-lg">
                <TrendingDown className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">القيمة الحالية</p>
                <p className="text-xl font-bold text-indigo-600">
                  {stats.depreciatedValue.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-yellow-500 p-3 rounded-lg">
                <Clock className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">صيانة مستحقة</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.maintenanceDue}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-red-500 p-3 rounded-lg">
                <AlertTriangle className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">ضمان منتهي</p>
                <p className="text-2xl font-bold text-red-600">{stats.warrantyExpiring}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-orange-500 p-3 rounded-lg">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">معدل الإهلاك</p>
                <p className="text-xl font-bold text-orange-600">
                  {depreciationRate.toFixed(1)}%
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">الإجراءات السريعة</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action, index) => (
              <Link
                key={index}
                href={action.href}
                className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow duration-200"
              >
                <div className="flex items-center mb-4">
                  <div className={`${action.color} p-3 rounded-lg`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{action.title}</h3>
                <p className="text-sm text-gray-600">{action.description}</p>
              </Link>
            ))}
          </div>
        </div>

        {/* Asset Modules */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">وحدات إدارة الأصول</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {assetModules.map((module, index) => (
              <Link
                key={index}
                href={module.href}
                className={`bg-white rounded-lg shadow p-6 border-r-4 ${module.color} hover:shadow-lg transition-shadow duration-200`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    <module.icon className="h-8 w-8 text-gray-600 ml-4" />
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{module.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{module.description}</p>
                      <p className="text-sm font-medium text-blue-600 mt-2">{module.stats}</p>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Alerts and Notifications */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
                تنبيهات مهمة
              </h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-center p-3 bg-yellow-50 rounded-lg">
                  <Clock className="h-5 w-5 text-yellow-600 ml-3" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800">صيانة مستحقة</p>
                    <p className="text-sm text-yellow-600">{stats.maintenanceDue} أصول تحتاج صيانة</p>
                  </div>
                </div>
                
                <div className="flex items-center p-3 bg-red-50 rounded-lg">
                  <AlertTriangle className="h-5 w-5 text-red-600 ml-3" />
                  <div>
                    <p className="text-sm font-medium text-red-800">ضمان منتهي</p>
                    <p className="text-sm text-red-600">{stats.warrantyExpiring} أصول انتهى ضمانها</p>
                  </div>
                </div>
                
                <div className="flex items-center p-3 bg-blue-50 rounded-lg">
                  <Package className="h-5 w-5 text-blue-600 ml-3" />
                  <div>
                    <p className="text-sm font-medium text-blue-800">أصول جديدة</p>
                    <p className="text-sm text-blue-600">تم إضافة 3 أصول هذا الأسبوع</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">الأنشطة الأخيرة</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="bg-green-100 p-2 rounded-full">
                    <Plus className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="mr-3">
                    <p className="text-sm font-medium text-gray-900">أصل جديد</p>
                    <p className="text-sm text-gray-500">جهاز كمبيوتر محمول - قسم تقنية المعلومات</p>
                  </div>
                  <div className="mr-auto text-sm text-gray-500">منذ ساعتين</div>
                </div>
                
                <div className="flex items-center">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <Settings className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="mr-3">
                    <p className="text-sm font-medium text-gray-900">صيانة مكتملة</p>
                    <p className="text-sm text-gray-500">طابعة HP - قسم المحاسبة</p>
                  </div>
                  <div className="mr-auto text-sm text-gray-500">منذ 4 ساعات</div>
                </div>
                
                <div className="flex items-center">
                  <div className="bg-purple-100 p-2 rounded-full">
                    <Package className="h-4 w-4 text-purple-600" />
                  </div>
                  <div className="mr-3">
                    <p className="text-sm font-medium text-gray-900">نقل أصل</p>
                    <p className="text-sm text-gray-500">مكتب خشبي - من المخزن إلى قسم المبيعات</p>
                  </div>
                  <div className="mr-auto text-sm text-gray-500">أمس</div>
                </div>
                
                <div className="flex items-center">
                  <div className="bg-yellow-100 p-2 rounded-full">
                    <Calendar className="h-4 w-4 text-yellow-600" />
                  </div>
                  <div className="mr-3">
                    <p className="text-sm font-medium text-gray-900">جدولة صيانة</p>
                    <p className="text-sm text-gray-500">مكيف الهواء - صيانة دورية</p>
                  </div>
                  <div className="mr-auto text-sm text-gray-500">منذ يومين</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
