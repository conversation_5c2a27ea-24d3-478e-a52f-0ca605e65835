"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/assets/reports/page",{

/***/ "(app-pages-browser)/./src/app/assets/reports/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/assets/reports/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AssetsReportsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(app-pages-browser)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,FileText,Filter,Package,PieChart,Printer,RefreshCw,Search,TrendingDown!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AssetsReportsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [assets, setAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reportData, setReportData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalAssets: 0,\n        totalValue: 0,\n        totalDepreciation: 0,\n        activeAssets: 0,\n        maintenanceAssets: 0,\n        disposedAssets: 0,\n        categoryBreakdown: [],\n        conditionBreakdown: [],\n        monthlyDepreciation: []\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AssetsReportsPage.useEffect\": ()=>{\n            fetchReportData();\n        }\n    }[\"AssetsReportsPage.useEffect\"], [\n        dateRange,\n        selectedCategory\n    ]);\n    const fetchReportData = async ()=>{\n        try {\n            setLoading(true);\n            const params = new URLSearchParams();\n            if (selectedCategory !== 'all') {\n                params.append('category', selectedCategory);\n            }\n            if (dateRange !== 'all') {\n                params.append('dateRange', dateRange);\n            }\n            const response = await fetch(\"/api/assets/reports?\".concat(params.toString()));\n            if (response.ok) {\n                const data = await response.json();\n                setReportData({\n                    totalAssets: data.totalAssets,\n                    totalValue: data.totalValue,\n                    totalDepreciation: data.totalDepreciation,\n                    activeAssets: data.activeAssets,\n                    maintenanceAssets: data.maintenanceAssets,\n                    disposedAssets: data.disposedAssets,\n                    categoryBreakdown: data.categoryBreakdown,\n                    conditionBreakdown: data.conditionBreakdown,\n                    monthlyDepreciation: data.monthlyDepreciation\n                });\n                setAssets(data.assets);\n                setCategories(data.categories);\n            } else {\n                console.error('Failed to fetch report data');\n            }\n        } catch (error) {\n            console.error('Error fetching report data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return 'bg-green-100 text-green-800';\n            case 'MAINTENANCE':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'DISPOSED':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getStatusLabel = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return 'نشط';\n            case 'MAINTENANCE':\n                return 'صيانة';\n            case 'DISPOSED':\n                return 'مستبعد';\n            default:\n                return status;\n        }\n    };\n    const getConditionLabel = (condition)=>{\n        switch(condition){\n            case 'EXCELLENT':\n                return 'ممتاز';\n            case 'GOOD':\n                return 'جيد';\n            case 'FAIR':\n                return 'مقبول';\n            case 'POOR':\n                return 'ضعيف';\n            default:\n                return condition;\n        }\n    };\n    const filteredAssets = assets.filter((asset)=>{\n        const matchesSearch = asset.name.toLowerCase().includes(searchTerm.toLowerCase()) || asset.category.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesCategory = selectedCategory === 'all' || asset.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const handleExportReport = (format)=>{\n        alert(\"تم تصدير التقرير بصيغة \".concat(format === 'pdf' ? 'PDF' : 'Excel'));\n    };\n    const handlePrintReport = ()=>{\n        window.print();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n            lineNumber: 147,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/assets'),\n                                    className: \"flex items-center gap-2 text-gray-600 hover:text-gray-900\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"العودة للأصول\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: \"تقارير الأصول\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600\",\n                                            children: \"تقارير شاملة عن أصول الشركة وحالتها\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleExportReport('excel'),\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"تصدير Excel\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleExportReport('pdf'),\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"تصدير PDF\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePrintReport,\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"طباعة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-medium text-gray-900 mb-4 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                \"فلاتر التقرير\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 inline ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"الفترة الزمنية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: dateRange,\n                                            onChange: (e)=>setDateRange(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"جميع الفترات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"thisYear\",\n                                                    children: \"هذا العام\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"lastYear\",\n                                                    children: \"العام الماضي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"last6Months\",\n                                                    children: \"آخر 6 أشهر\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 inline ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"الفئة\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedCategory,\n                                            onChange: (e)=>setSelectedCategory(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"جميع الفئات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this),\n                                                categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: category,\n                                                        children: category\n                                                    }, category, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 inline ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"البحث\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            placeholder: \"ابحث في الأصول...\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setDateRange('all');\n                                            setSelectedCategory('all');\n                                            setSearchTerm('');\n                                        },\n                                        className: \"w-full flex items-center justify-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إعادة تعيين\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        \"aria-label\": \"Tabs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('overview'),\n                                className: \"\".concat(activeTab === 'overview' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', \" whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"نظرة عامة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('categories'),\n                                className: \"\".concat(activeTab === 'categories' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', \" whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"تحليل الفئات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('depreciation'),\n                                className: \"\".concat(activeTab === 'depreciation' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', \" whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"الإهلاك\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('details'),\n                                className: \"\".concat(activeTab === 'details' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', \" whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"التفاصيل\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this),\n                activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-500 p-3 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: \"إجمالي الأصول\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: reportData.totalAssets\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-500 p-3 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: \"القيمة الحالية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: [\n                                                            reportData.totalValue.toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-500 p-3 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: \"إجمالي الإهلاك\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: [\n                                                            reportData.totalDepreciation.toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-500 p-3 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: \"الأصول النشطة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-purple-600\",\n                                                        children: reportData.activeAssets\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"توزيع حالة الأصول\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"نشطة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: reportData.activeAssets\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-24 bg-gray-200 rounded-full h-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-green-600 h-2 rounded-full\",\n                                                                        style: {\n                                                                            width: \"\".concat(reportData.totalAssets > 0 ? reportData.activeAssets / reportData.totalAssets * 100 : 0, \"%\")\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                        lineNumber: 393,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-yellow-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"في الصيانة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: reportData.maintenanceAssets\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-24 bg-gray-200 rounded-full h-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-yellow-600 h-2 rounded-full\",\n                                                                        style: {\n                                                                            width: \"\".concat(reportData.totalAssets > 0 ? reportData.maintenanceAssets / reportData.totalAssets * 100 : 0, \"%\")\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-red-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"مستبعدة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: reportData.disposedAssets\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 423,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-24 bg-gray-200 rounded-full h-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-red-600 h-2 rounded-full\",\n                                                                        style: {\n                                                                            width: \"\".concat(reportData.totalAssets > 0 ? reportData.disposedAssets / reportData.totalAssets * 100 : 0, \"%\")\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"توزيع حالة الأصول\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: reportData.conditionBreakdown.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full bg-blue-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: getConditionLabel(item.condition)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: item.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"(\",\n                                                                        item.percentage.toFixed(1),\n                                                                        \"%)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, item.condition, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 11\n                }, this),\n                activeTab === 'categories' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: \"تحليل الأصول حسب الفئة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: reportData.categoryBreakdown.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: category.category\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    category.count,\n                                                                    \" أصل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-green-600\",\n                                                                children: [\n                                                                    category.value.toLocaleString(),\n                                                                    \" ر.س\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-600 h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(reportData.totalValue > 0 ? category.value / reportData.totalValue * 100 : 0, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-xs text-gray-500\",\n                                                children: [\n                                                    reportData.totalValue > 0 ? (category.value / reportData.totalValue * 100).toFixed(1) : 0,\n                                                    \"% من إجمالي القيمة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, category.category, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 11\n                }, this),\n                activeTab === 'depreciation' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: \"تقرير الإهلاك الشهري\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: reportData.monthlyDepreciation.map((month, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-gray-900\",\n                                                children: month.month\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-bold text-red-600\",\n                                                children: [\n                                                    month.amount.toLocaleString(),\n                                                    \" ر.س\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, month.month, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-gray-900 mb-2\",\n                                        children: \"ملخص الإهلاك\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"إجمالي الإهلاك المتراكم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-red-600\",\n                                                        children: [\n                                                            reportData.totalDepreciation.toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"متوسط الإهلاك الشهري\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-orange-600\",\n                                                        children: [\n                                                            reportData.monthlyDepreciation.length > 0 ? Math.round(reportData.monthlyDepreciation.reduce((sum, m)=>sum + m.amount, 0) / reportData.monthlyDepreciation.length).toLocaleString() : 0,\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                    lineNumber: 493,\n                    columnNumber: 11\n                }, this),\n                activeTab === 'details' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4 border-b border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"تفاصيل جميع الأصول\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: [\n                                            \"عرض \",\n                                            filteredAssets.length,\n                                            \" من أصل \",\n                                            assets.length,\n                                            \" أصل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"اسم الأصل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"الفئة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"سعر الشراء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"القيمة الحالية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"معدل الإهلاك\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"الموقع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: filteredAssets.map((asset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: asset.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: asset.category\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    asset.purchasePrice.toLocaleString(),\n                                                                    \" ر.س\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-green-600\",\n                                                                children: [\n                                                                    asset.currentValue.toLocaleString(),\n                                                                    \" ر.س\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    asset.depreciationRate,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(getStatusColor(asset.status)),\n                                                                children: getStatusLabel(asset.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: asset.location || 'غير محدد'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, asset.id, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 15\n                            }, this),\n                            filteredAssets.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_FileText_Filter_Package_PieChart_Printer_RefreshCw_Search_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"mt-2 text-sm font-medium text-gray-900\",\n                                        children: \"لا توجد أصول\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-gray-500\",\n                                        children: \"لم يتم العثور على أصول تطابق معايير البحث.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                        lineNumber: 533,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                    lineNumber: 532,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-blue-900 mb-2\",\n                            children: \"نصائح لتقارير الأصول:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                            lineNumber: 613,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-sm text-blue-800 space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• استخدم الفلاتر لتخصيص التقارير حسب احتياجاتك\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• راجع تقارير الإهلاك دورياً لتحديث القيم\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• صدر التقارير بصيغة PDF أو Excel للمشاركة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• راقب الأصول في الصيانة لضمان العودة للخدمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• استخدم تحليل الفئات لاتخاذ قرارات الشراء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n                    lineNumber: 612,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\assets\\\\reports\\\\page.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_s(AssetsReportsPage, \"tYCnJzye2GijxqJvNNVyEl8MWNo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AssetsReportsPage;\nvar _c;\n$RefreshReg$(_c, \"AssetsReportsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/assets/reports/page.tsx\n"));

/***/ })

});