import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const transactions = await prisma.bankTransaction.findMany({
      include: {
        account: true
      },
      orderBy: {
        transactionDate: 'desc'
      }
    })

    // Map to include account name
    const transactionsWithAccountName = transactions.map(transaction => ({
      ...transaction,
      accountName: transaction.account.accountName
    }))

    return NextResponse.json(transactionsWithAccountName)
  } catch (error) {
    console.error('Error fetching bank transactions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      accountId,
      type,
      amount,
      description,
      reference,
      category,
      transactionDate,
      relatedId,
      relatedType
    } = body

    // Validate required fields
    if (!accountId || !type || !amount || !description) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate amount
    if (amount <= 0) {
      return NextResponse.json(
        { error: 'Amount must be greater than zero' },
        { status: 400 }
      )
    }

    // Get current account balance
    const account = await prisma.bankAccount.findUnique({
      where: { id: accountId }
    })

    if (!account) {
      return NextResponse.json(
        { error: 'Account not found' },
        { status: 404 }
      )
    }

    // Calculate new balance
    let newBalance = account.balance
    if (type === 'DEPOSIT' || type === 'TRANSFER_IN') {
      newBalance += parseFloat(amount)
    } else if (type === 'WITHDRAWAL' || type === 'TRANSFER_OUT') {
      newBalance -= parseFloat(amount)
      
      // Check for sufficient funds
      if (newBalance < 0 && account.accountType !== 'CREDIT') {
        return NextResponse.json(
          { error: 'Insufficient funds' },
          { status: 400 }
        )
      }
    }

    // Create transaction
    const transaction = await prisma.bankTransaction.create({
      data: {
        accountId,
        type,
        amount: parseFloat(amount),
        balance: newBalance,
        description,
        reference: reference || null,
        category: category || null,
        transactionDate: transactionDate ? new Date(transactionDate) : new Date(),
        relatedId: relatedId || null,
        relatedType: relatedType || null,
        status: 'COMPLETED'
      },
      include: {
        account: true
      }
    })

    // Update account balance
    await prisma.bankAccount.update({
      where: { id: accountId },
      data: { balance: newBalance }
    })

    return NextResponse.json({
      ...transaction,
      accountName: transaction.account.accountName
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating bank transaction:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
