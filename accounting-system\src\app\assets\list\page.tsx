'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import MainLayout from '@/components/Layout/MainLayout'
import { Plus, Search, Edit, Trash2, Eye, Package, DollarSign, Calendar, MapPin, User, AlertTriangle } from 'lucide-react'

interface Asset {
  id: string
  assetCode: string
  name: string
  description?: string
  category: string
  brand?: string
  model?: string
  purchaseDate: string
  purchasePrice: number
  currentValue: number
  location?: string
  department?: string
  assignedTo?: string
  condition: string
  status: string
  warrantyExpiry?: string
  nextMaintenance?: string
  createdAt: string
}

export default function AssetsListPage() {
  const [assets, setAssets] = useState<Asset[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('ALL')
  const [statusFilter, setStatusFilter] = useState<string>('ALL')
  const [conditionFilter, setConditionFilter] = useState<string>('ALL')

  useEffect(() => {
    const fetchAssets = async () => {
      try {
        const response = await fetch('/api/assets')
        if (response.ok) {
          const data = await response.json()
          setAssets(data)
        } else {
          // Fallback to demo data if API fails
          setAssets([
            {
              id: '1',
              assetCode: 'AST-001',
              name: 'جهاز كمبيوتر محمول Dell',
              description: 'جهاز كمبيوتر محمول للعمل المكتبي',
              category: 'COMPUTER',
              brand: 'Dell',
              model: 'Latitude 5520',
              purchaseDate: '2023-01-15',
              purchasePrice: 4500,
              currentValue: 3600,
              location: 'مكتب الإدارة',
              department: 'تقنية المعلومات',
              assignedTo: 'أحمد محمد',
              condition: 'GOOD',
              status: 'ACTIVE',
              warrantyExpiry: '2026-01-15',
              nextMaintenance: '2024-06-15',
              createdAt: '2023-01-15'
            },
            {
              id: '2',
              assetCode: 'AST-002',
              name: 'طابعة HP LaserJet',
              description: 'طابعة ليزر للمكتب',
              category: 'EQUIPMENT',
              brand: 'HP',
              model: 'LaserJet Pro 400',
              purchaseDate: '2022-06-10',
              purchasePrice: 1200,
              currentValue: 800,
              location: 'قسم المحاسبة',
              department: 'المحاسبة',
              condition: 'FAIR',
              status: 'ACTIVE',
              warrantyExpiry: '2024-06-10',
              nextMaintenance: '2024-04-10',
              createdAt: '2022-06-10'
            },
            {
              id: '3',
              assetCode: 'AST-003',
              name: 'مكتب خشبي',
              description: 'مكتب خشبي للموظفين',
              category: 'FURNITURE',
              brand: 'IKEA',
              model: 'BEKANT',
              purchaseDate: '2023-03-20',
              purchasePrice: 800,
              currentValue: 720,
              location: 'قسم المبيعات',
              department: 'المبيعات',
              assignedTo: 'فاطمة أحمد',
              condition: 'EXCELLENT',
              status: 'ACTIVE',
              createdAt: '2023-03-20'
            }
          ])
        }
      } catch (error) {
        console.error('Error fetching assets:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchAssets()
  }, [])

  const categories = [...new Set(assets.map(asset => asset.category))]

  const filteredAssets = assets.filter(asset => {
    const matchesSearch = 
      asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.assetCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.brand?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.model?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesCategory = categoryFilter === 'ALL' || asset.category === categoryFilter
    const matchesStatus = statusFilter === 'ALL' || asset.status === statusFilter
    const matchesCondition = conditionFilter === 'ALL' || asset.condition === conditionFilter
    
    return matchesSearch && matchesCategory && matchesStatus && matchesCondition
  })

  const handleDeleteAsset = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا الأصل؟')) {
      try {
        const response = await fetch(`/api/assets/${id}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          setAssets(assets.filter(asset => asset.id !== id))
          alert('تم حذف الأصل بنجاح!')
        } else {
          alert('حدث خطأ أثناء حذف الأصل')
        }
      } catch (error) {
        console.error('Error deleting asset:', error)
        alert('حدث خطأ أثناء حذف الأصل')
      }
    }
  }

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'EQUIPMENT':
        return 'معدات'
      case 'FURNITURE':
        return 'أثاث'
      case 'VEHICLE':
        return 'مركبات'
      case 'BUILDING':
        return 'مباني'
      case 'COMPUTER':
        return 'حاسوب'
      case 'SOFTWARE':
        return 'برمجيات'
      default:
        return category
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'EQUIPMENT':
        return 'bg-blue-100 text-blue-800'
      case 'FURNITURE':
        return 'bg-green-100 text-green-800'
      case 'VEHICLE':
        return 'bg-purple-100 text-purple-800'
      case 'BUILDING':
        return 'bg-orange-100 text-orange-800'
      case 'COMPUTER':
        return 'bg-indigo-100 text-indigo-800'
      case 'SOFTWARE':
        return 'bg-pink-100 text-pink-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case 'EXCELLENT':
        return 'bg-green-100 text-green-800'
      case 'GOOD':
        return 'bg-blue-100 text-blue-800'
      case 'FAIR':
        return 'bg-yellow-100 text-yellow-800'
      case 'POOR':
        return 'bg-orange-100 text-orange-800'
      case 'DAMAGED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getConditionLabel = (condition: string) => {
    switch (condition) {
      case 'EXCELLENT':
        return 'ممتاز'
      case 'GOOD':
        return 'جيد'
      case 'FAIR':
        return 'مقبول'
      case 'POOR':
        return 'ضعيف'
      case 'DAMAGED':
        return 'تالف'
      default:
        return condition
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800'
      case 'INACTIVE':
        return 'bg-gray-100 text-gray-800'
      case 'DISPOSED':
        return 'bg-red-100 text-red-800'
      case 'SOLD':
        return 'bg-blue-100 text-blue-800'
      case 'LOST':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'نشط'
      case 'INACTIVE':
        return 'غير نشط'
      case 'DISPOSED':
        return 'مُتخلص منه'
      case 'SOLD':
        return 'مباع'
      case 'LOST':
        return 'مفقود'
      default:
        return status
    }
  }

  const isWarrantyExpiring = (warrantyExpiry?: string) => {
    if (!warrantyExpiry) return false
    const expiry = new Date(warrantyExpiry)
    const now = new Date()
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
    return expiry <= thirtyDaysFromNow
  }

  const isMaintenanceDue = (nextMaintenance?: string) => {
    if (!nextMaintenance) return false
    const maintenance = new Date(nextMaintenance)
    const now = new Date()
    return maintenance <= now
  }

  const totalValue = filteredAssets.reduce((sum, asset) => sum + asset.purchasePrice, 0)
  const currentValue = filteredAssets.reduce((sum, asset) => sum + asset.currentValue, 0)
  const depreciationAmount = totalValue - currentValue

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">قائمة الأصول</h1>
            <p className="mt-2 text-gray-600">عرض وإدارة جميع أصول الشركة</p>
          </div>
          <Link
            href="/assets/new"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
          >
            <Plus className="h-5 w-5" />
            إضافة أصل جديد
          </Link>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-500 p-3 rounded-lg">
                <Package className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الأصول</p>
                <p className="text-2xl font-bold text-blue-600">{filteredAssets.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-500 p-3 rounded-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">القيمة الأصلية</p>
                <p className="text-xl font-bold text-green-600">
                  {totalValue.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-purple-500 p-3 rounded-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">القيمة الحالية</p>
                <p className="text-xl font-bold text-purple-600">
                  {currentValue.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-red-500 p-3 rounded-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الإهلاك</p>
                <p className="text-xl font-bold text-red-600">
                  {depreciationAmount.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="البحث في الأصول..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
            >
              <option value="ALL">جميع الفئات</option>
              <option value="EQUIPMENT">معدات</option>
              <option value="FURNITURE">أثاث</option>
              <option value="VEHICLE">مركبات</option>
              <option value="BUILDING">مباني</option>
              <option value="COMPUTER">حاسوب</option>
              <option value="SOFTWARE">برمجيات</option>
            </select>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="ALL">جميع الحالات</option>
              <option value="ACTIVE">نشط</option>
              <option value="INACTIVE">غير نشط</option>
              <option value="DISPOSED">مُتخلص منه</option>
              <option value="SOLD">مباع</option>
              <option value="LOST">مفقود</option>
            </select>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={conditionFilter}
              onChange={(e) => setConditionFilter(e.target.value)}
            >
              <option value="ALL">جميع الأحوال</option>
              <option value="EXCELLENT">ممتاز</option>
              <option value="GOOD">جيد</option>
              <option value="FAIR">مقبول</option>
              <option value="POOR">ضعيف</option>
              <option value="DAMAGED">تالف</option>
            </select>
          </div>
        </div>

        {/* Assets Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الأصل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الفئة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الموقع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    القيمة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التنبيهات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredAssets.map((asset) => (
                  <tr key={asset.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{asset.name}</div>
                        <div className="text-sm text-gray-500">{asset.assetCode}</div>
                        {asset.brand && asset.model && (
                          <div className="text-xs text-gray-400">{asset.brand} {asset.model}</div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(asset.category)}`}>
                        {getCategoryLabel(asset.category)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{asset.location || '-'}</div>
                      <div className="text-sm text-gray-500">{asset.department || '-'}</div>
                      {asset.assignedTo && (
                        <div className="text-xs text-blue-600 flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {asset.assignedTo}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-bold text-gray-900">
                        {asset.currentValue.toLocaleString()} ر.س
                      </div>
                      <div className="text-xs text-gray-500">
                        أصلية: {asset.purchasePrice.toLocaleString()} ر.س
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(asset.status)}`}>
                          {getStatusLabel(asset.status)}
                        </span>
                        <br />
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getConditionColor(asset.condition)}`}>
                          {getConditionLabel(asset.condition)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col gap-1">
                        {isWarrantyExpiring(asset.warrantyExpiry) && (
                          <div className="flex items-center text-xs text-red-600">
                            <AlertTriangle className="h-3 w-3 ml-1" />
                            ضمان منتهي
                          </div>
                        )}
                        {isMaintenanceDue(asset.nextMaintenance) && (
                          <div className="flex items-center text-xs text-yellow-600">
                            <Calendar className="h-3 w-3 ml-1" />
                            صيانة مستحقة
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <Link
                          href={`/assets/${asset.id}`}
                          className="text-blue-600 hover:text-blue-900"
                          title="عرض التفاصيل"
                        >
                          <Eye className="h-4 w-4" />
                        </Link>
                        <Link
                          href={`/assets/${asset.id}/edit`}
                          className="text-yellow-600 hover:text-yellow-900"
                          title="تعديل"
                        >
                          <Edit className="h-4 w-4" />
                        </Link>
                        <button 
                          onClick={() => handleDeleteAsset(asset.id)}
                          className="text-red-600 hover:text-red-900"
                          title="حذف"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredAssets.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد أصول مطابقة لبحثك</p>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  )
}
