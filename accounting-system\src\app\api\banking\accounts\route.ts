import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const accounts = await prisma.bankAccount.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(accounts)
  } catch (error) {
    console.error('Error fetching bank accounts:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      accountName,
      accountNumber,
      bankName,
      branchName,
      accountType,
      currency,
      balance,
      isActive,
      description
    } = body

    // Validate required fields
    if (!accountName || !accountNumber || !bankName || !accountType) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate balance
    if (balance < 0) {
      return NextResponse.json(
        { error: 'Balance cannot be negative' },
        { status: 400 }
      )
    }

    const account = await prisma.bankAccount.create({
      data: {
        accountName,
        accountNumber,
        bankName,
        branchName: branchName || null,
        accountType,
        currency: currency || 'SAR',
        balance: parseFloat(balance) || 0,
        isActive: isActive !== false,
        description: description || null
      }
    })

    // Create initial transaction if balance > 0
    if (parseFloat(balance) > 0) {
      await prisma.bankTransaction.create({
        data: {
          accountId: account.id,
          type: 'DEPOSIT',
          amount: parseFloat(balance),
          balance: parseFloat(balance),
          description: 'الرصيد الابتدائي للحساب',
          transactionDate: new Date(),
          status: 'COMPLETED'
        }
      })
    }

    return NextResponse.json(account, { status: 201 })
  } catch (error) {
    console.error('Error creating bank account:', error)
    
    // Handle unique constraint violations
    if (error instanceof Error && error.message.includes('Unique constraint')) {
      return NextResponse.json(
        { error: 'Account number already exists' },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
