'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import MainLayout from '@/components/Layout/MainLayout'
import { Plus, Search, Edit, Eye, DollarSign, Calendar, CheckCircle, Clock, Download } from 'lucide-react'

interface Salary {
  id: string
  employeeId: string
  employeeName: string
  month: number
  year: number
  basicSalary: number
  allowances: number
  deductions: number
  overtime: number
  bonus: number
  netSalary: number
  status: string
  paidAt?: string
  notes?: string
  createdAt: string
}

const months = [
  'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
  'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
]

export default function SalariesPage() {
  const [salaries, setSalaries] = useState<Salary[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('ALL')
  const [monthFilter, setMonthFilter] = useState<string>('ALL')
  const [yearFilter, setYearFilter] = useState<string>(new Date().getFullYear().toString())

  useEffect(() => {
    const fetchSalaries = async () => {
      try {
        const response = await fetch('/api/hr/salaries')
        if (response.ok) {
          const data = await response.json()
          setSalaries(data)
        } else {
          // Fallback to demo data if API fails
          setSalaries([
            {
              id: '1',
              employeeId: 'EMP001',
              employeeName: 'أحمد محمد',
              month: 3,
              year: 2024,
              basicSalary: 8000,
              allowances: 1000,
              deductions: 500,
              overtime: 300,
              bonus: 0,
              netSalary: 8800,
              status: 'PAID',
              paidAt: '2024-03-31',
              createdAt: '2024-03-25'
            },
            {
              id: '2',
              employeeId: 'EMP002',
              employeeName: 'فاطمة أحمد',
              month: 3,
              year: 2024,
              basicSalary: 6500,
              allowances: 800,
              deductions: 300,
              overtime: 0,
              bonus: 500,
              netSalary: 7500,
              status: 'PENDING',
              createdAt: '2024-03-25'
            },
            {
              id: '3',
              employeeId: 'EMP003',
              employeeName: 'خالد علي',
              month: 3,
              year: 2024,
              basicSalary: 9500,
              allowances: 1200,
              deductions: 600,
              overtime: 400,
              bonus: 1000,
              netSalary: 11500,
              status: 'PENDING',
              createdAt: '2024-03-25'
            }
          ])
        }
      } catch (error) {
        console.error('Error fetching salaries:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchSalaries()
  }, [])

  const years = [...new Set(salaries.map(salary => salary.year.toString()))]

  const filteredSalaries = salaries.filter(salary => {
    const matchesSearch = 
      salary.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      salary.employeeId.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'ALL' || salary.status === statusFilter
    const matchesMonth = monthFilter === 'ALL' || salary.month.toString() === monthFilter
    const matchesYear = yearFilter === 'ALL' || salary.year.toString() === yearFilter
    
    return matchesSearch && matchesStatus && matchesMonth && matchesYear
  })

  const handleMarkAsPaid = async (id: string) => {
    try {
      const response = await fetch(`/api/hr/salaries/${id}/pay`, {
        method: 'POST',
      })

      if (response.ok) {
        setSalaries(salaries.map(salary => 
          salary.id === id 
            ? { ...salary, status: 'PAID', paidAt: new Date().toISOString() }
            : salary
        ))
        alert('تم تسجيل دفع الراتب!')
      } else {
        alert('حدث خطأ أثناء تسجيل دفع الراتب')
      }
    } catch (error) {
      console.error('Error marking salary as paid:', error)
      alert('حدث خطأ أثناء تسجيل دفع الراتب')
    }
  }

  const handleGeneratePayslip = (salary: Salary) => {
    // This would generate and download a payslip PDF
    alert(`سيتم إنشاء كشف راتب ${salary.employeeName} لشهر ${months[salary.month - 1]} ${salary.year}`)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'bg-green-100 text-green-800'
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'مدفوع'
      case 'PENDING':
        return 'في الانتظار'
      default:
        return status
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PAID':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-600" />
      default:
        return null
    }
  }

  const totalPendingSalaries = filteredSalaries
    .filter(salary => salary.status === 'PENDING')
    .reduce((sum, salary) => sum + salary.netSalary, 0)

  const totalPaidSalaries = filteredSalaries
    .filter(salary => salary.status === 'PAID')
    .reduce((sum, salary) => sum + salary.netSalary, 0)

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة الرواتب</h1>
            <p className="mt-2 text-gray-600">إدارة رواتب الموظفين وكشوف المرتبات</p>
          </div>
          <div className="flex gap-2">
            <Link
              href="/hr/salaries/generate"
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
            >
              <Calendar className="h-5 w-5" />
              إنشاء رواتب الشهر
            </Link>
            <Link
              href="/hr/salaries/new"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
            >
              <Plus className="h-5 w-5" />
              إضافة راتب
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-500 p-3 rounded-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الرواتب</p>
                <p className="text-2xl font-bold text-blue-600">
                  {(totalPendingSalaries + totalPaidSalaries).toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-yellow-500 p-3 rounded-lg">
                <Clock className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">في الانتظار</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {totalPendingSalaries.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-500 p-3 rounded-lg">
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">مدفوعة</p>
                <p className="text-2xl font-bold text-green-600">
                  {totalPaidSalaries.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-purple-500 p-3 rounded-lg">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">عدد الرواتب</p>
                <p className="text-2xl font-bold text-purple-600">{salaries.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="البحث في الرواتب..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="ALL">جميع الحالات</option>
              <option value="PENDING">في الانتظار</option>
              <option value="PAID">مدفوعة</option>
            </select>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={monthFilter}
              onChange={(e) => setMonthFilter(e.target.value)}
            >
              <option value="ALL">جميع الشهور</option>
              {months.map((month, index) => (
                <option key={index + 1} value={(index + 1).toString()}>{month}</option>
              ))}
            </select>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={yearFilter}
              onChange={(e) => setYearFilter(e.target.value)}
            >
              <option value="ALL">جميع السنوات</option>
              {years.map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Salaries Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الموظف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الشهر/السنة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الراتب الأساسي
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    البدلات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الخصومات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    صافي الراتب
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredSalaries.map((salary) => (
                  <tr key={salary.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{salary.employeeName}</div>
                      <div className="text-sm text-gray-500">{salary.employeeId}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {months[salary.month - 1]} {salary.year}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {salary.basicSalary.toLocaleString()} ر.س
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {(salary.allowances + salary.overtime + salary.bonus).toLocaleString()} ر.س
                      </div>
                      <div className="text-xs text-gray-500">
                        بدلات: {salary.allowances} | إضافي: {salary.overtime} | مكافآت: {salary.bonus}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-red-600">
                        {salary.deductions.toLocaleString()} ر.س
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-bold text-green-600">
                        {salary.netSalary.toLocaleString()} ر.س
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(salary.status)}
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(salary.status)}`}>
                          {getStatusLabel(salary.status)}
                        </span>
                      </div>
                      {salary.paidAt && (
                        <div className="text-xs text-gray-500 mt-1">
                          دُفع في: {new Date(salary.paidAt).toLocaleDateString('ar-SA')}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        {salary.status === 'PENDING' && (
                          <button
                            onClick={() => handleMarkAsPaid(salary.id)}
                            className="text-green-600 hover:text-green-900"
                            title="تسجيل كمدفوع"
                          >
                            <CheckCircle className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          onClick={() => handleGeneratePayslip(salary)}
                          className="text-purple-600 hover:text-purple-900"
                          title="تحميل كشف الراتب"
                        >
                          <Download className="h-4 w-4" />
                        </button>
                        <Link
                          href={`/hr/salaries/${salary.id}`}
                          className="text-blue-600 hover:text-blue-900"
                          title="عرض"
                        >
                          <Eye className="h-4 w-4" />
                        </Link>
                        <Link
                          href={`/hr/salaries/${salary.id}/edit`}
                          className="text-yellow-600 hover:text-yellow-900"
                          title="تعديل"
                        >
                          <Edit className="h-4 w-4" />
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredSalaries.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد رواتب مطابقة لبحثك</p>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  )
}
