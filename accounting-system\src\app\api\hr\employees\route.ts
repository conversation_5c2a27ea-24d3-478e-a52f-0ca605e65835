import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const employees = await prisma.employee.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(employees)
  } catch (error) {
    console.error('Error fetching employees:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await request.formData()
    
    // Extract form data
    const employeeId = formData.get('employeeId') as string
    const firstName = formData.get('firstName') as string
    const lastName = formData.get('lastName') as string
    const email = formData.get('email') as string
    const phone = formData.get('phone') as string
    const address = formData.get('address') as string
    const dateOfBirth = formData.get('dateOfBirth') as string
    const hireDate = formData.get('hireDate') as string
    const position = formData.get('position') as string
    const department = formData.get('department') as string
    const salary = formData.get('salary') as string
    const nationalId = formData.get('nationalId') as string
    const emergencyContact = formData.get('emergencyContact') as string
    const emergencyPhone = formData.get('emergencyPhone') as string
    const bankAccount = formData.get('bankAccount') as string
    const notes = formData.get('notes') as string
    const profileImage = formData.get('profileImage') as File

    // Validate required fields
    if (!firstName || !lastName || !email || !position || !department || !salary || !hireDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate salary
    if (parseFloat(salary) <= 0) {
      return NextResponse.json(
        { error: 'Salary must be greater than zero' },
        { status: 400 }
      )
    }

    let profileImagePath = null

    // Handle image upload
    if (profileImage && profileImage.size > 0) {
      try {
        const bytes = await profileImage.arrayBuffer()
        const buffer = Buffer.from(bytes)

        // Create uploads directory if it doesn't exist
        const uploadsDir = join(process.cwd(), 'public', 'uploads', 'employees')
        await mkdir(uploadsDir, { recursive: true })

        // Generate unique filename
        const timestamp = Date.now()
        const extension = profileImage.name.split('.').pop()
        const filename = `${employeeId || timestamp}.${extension}`
        const filepath = join(uploadsDir, filename)

        // Write file
        await writeFile(filepath, buffer)
        profileImagePath = `/uploads/employees/${filename}`
      } catch (error) {
        console.error('Error uploading image:', error)
        // Continue without image if upload fails
      }
    }

    // Generate employee ID if not provided
    const finalEmployeeId = employeeId || `EMP${Date.now().toString().slice(-6)}`

    const employee = await prisma.employee.create({
      data: {
        employeeId: finalEmployeeId,
        firstName,
        lastName,
        email,
        phone: phone || null,
        address: address || null,
        dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
        hireDate: new Date(hireDate),
        position,
        department,
        salary: parseFloat(salary),
        nationalId: nationalId || null,
        emergencyContact: emergencyContact || null,
        emergencyPhone: emergencyPhone || null,
        bankAccount: bankAccount || null,
        notes: notes || null,
        profileImage: profileImagePath,
        status: 'ACTIVE'
      }
    })

    return NextResponse.json(employee, { status: 201 })
  } catch (error) {
    console.error('Error creating employee:', error)
    
    // Handle unique constraint violations
    if (error instanceof Error && error.message.includes('Unique constraint')) {
      if (error.message.includes('email')) {
        return NextResponse.json(
          { error: 'Email already exists' },
          { status: 400 }
        )
      }
      if (error.message.includes('employeeId')) {
        return NextResponse.json(
          { error: 'Employee ID already exists' },
          { status: 400 }
        )
      }
      if (error.message.includes('nationalId')) {
        return NextResponse.json(
          { error: 'National ID already exists' },
          { status: 400 }
        )
      }
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
