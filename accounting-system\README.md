# نظام المحاسبة الاحترافي

نظام محاسبة شامل ومتطور مصمم خصيصاً للشركات الصغيرة والمتوسطة، يوفر جميع الأدوات اللازمة لإدارة الحسابات والفواتير والمصروفات والتقارير المالية.

## المميزات الرئيسية

### 🏠 لوحة التحكم
- نظرة عامة شاملة على الأداء المالي
- إحصائيات فورية للإيرادات والمصروفات
- عرض آخر الفواتير والمصروفات
- مؤشرات الأداء الرئيسية (KPIs)

### 👥 إدارة العملاء
- إضافة وتعديل وحذف بيانات العملاء
- حفظ معلومات الاتصال والعناوين
- تتبع الأرقام الضريبية
- البحث والتصفية المتقدمة

### 📄 إدارة الفواتير
- إنشاء فواتير احترافية
- تتبع حالات الفواتير (مسودة، مرسلة، مدفوعة، متأخرة)
- إرسال الفواتير عبر البريد الإلكتروني
- تحميل الفواتير بصيغة PDF
- تذكيرات الدفع التلقائية

### 📦 إدارة المنتجات والخدمات
- كتالوج شامل للمنتجات والخدمات
- تحديد الأسعار والوحدات
- تفعيل وإلغاء تفعيل المنتجات
- تصنيف المنتجات

### 💳 إدارة المصروفات
- تسجيل وتصنيف المصروفات
- رفع إيصالات المصروفات
- تقارير المصروفات حسب التصنيف
- تتبع الميزانيات

### 📊 التقارير المالية
- تقرير الأرباح والخسائر
- تقرير التدفق النقدي
- الميزانية العمومية
- تقارير دورية (شهرية، ربعية، سنوية)
- رسوم بيانية تفاعلية

### 👤 إدارة المستخدمين
- نظام أدوار متقدم (مشرف، مدير، محاسب)
- صلاحيات مخصصة لكل دور
- تتبع نشاط المستخدمين
- إدارة كلمات المرور

### 🔐 الأمان والمصادقة
- تسجيل دخول آمن
- تشفير كلمات المرور
- جلسات آمنة
- حماية من الوصول غير المصرح

## التقنيات المستخدمة

### Frontend
- **Next.js 15** - إطار عمل React للتطبيقات الحديثة
- **TypeScript** - للكتابة الآمنة والموثوقة
- **Tailwind CSS** - للتصميم السريع والمرن
- **Lucide React** - مكتبة الأيقونات الحديثة

### Backend
- **Next.js API Routes** - للخدمات الخلفية
- **Prisma ORM** - لإدارة قاعدة البيانات
- **PostgreSQL** - قاعدة بيانات قوية وموثوقة

### المصادقة والأمان
- **NextAuth.js** - نظام مصادقة شامل
- **bcryptjs** - تشفير كلمات المرور
- **JWT** - رموز الوصول الآمنة

### أدوات إضافية
- **React Hook Form** - إدارة النماذج
- **Chart.js** - الرسوم البيانية
- **Date-fns** - معالجة التواريخ

## متطلبات التشغيل

- Node.js 18 أو أحدث
- PostgreSQL 12 أو أحدث
- npm أو yarn

## التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd accounting-system
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. إعداد قاعدة البيانات
```bash
# إنشاء ملف البيئة
cp .env.local.example .env.local

# تحديث متغيرات البيئة في .env.local
DATABASE_URL="postgresql://username:password@localhost:5432/accounting_db"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
```

### 4. إعداد Prisma
```bash
# إنشاء قاعدة البيانات
npx prisma db push

# توليد Prisma Client
npx prisma generate

# (اختياري) إضافة بيانات تجريبية
npx prisma db seed
```

### 5. تشغيل التطبيق
```bash
npm run dev
```

التطبيق سيكون متاحاً على: `http://localhost:3000`

## الاستخدام

### إنشاء حساب المشرف الأول
1. انتقل إلى `/auth/signup`
2. أنشئ حساب جديد واختر دور "مشرف"
3. سجل الدخول باستخدام بياناتك

### إعداد النظام
1. أضف تصنيفات المصروفات
2. أنشئ المنتجات والخدمات
3. أضف بيانات العملاء
4. ابدأ في إنشاء الفواتير

## الأدوار والصلاحيات

### مشرف (Admin)
- الوصول الكامل لجميع الميزات
- إدارة المستخدمين
- إعدادات النظام
- جميع التقارير

### مدير (Manager)
- إدارة العملاء والفواتير
- التقارير المالية
- إدارة المنتجات
- عرض المصروفات

### محاسب (Accountant)
- إنشاء وتعديل الفواتير
- إدارة المصروفات
- التقارير الأساسية
- إدارة بيانات العملاء

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إضافة الاختبارات إذا لزم الأمر
5. إرسال Pull Request

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- افتح Issue جديد في GitHub
- راسلنا على البريد الإلكتروني

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الشكر والتقدير

شكر خاص لجميع المساهمين والمكتبات مفتوحة المصدر المستخدمة في هذا المشروع.
