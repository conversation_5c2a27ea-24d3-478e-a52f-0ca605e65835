'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import MainLayout from '@/components/Layout/MainLayout'
import { Plus, Search, Edit, Trash2, Eye, CreditCard, DollarSign, TrendingUp, TrendingDown, Building } from 'lucide-react'

interface BankAccount {
  id: string
  accountName: string
  accountNumber: string
  bankName: string
  branchName?: string
  accountType: string
  currency: string
  balance: number
  isActive: boolean
  description?: string
  createdAt: string
}

export default function BankAccountsPage() {
  const [accounts, setAccounts] = useState<BankAccount[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('ALL')
  const [statusFilter, setStatusFilter] = useState<string>('ALL')

  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        const response = await fetch('/api/banking/accounts')
        if (response.ok) {
          const data = await response.json()
          setAccounts(data)
        } else {
          // Fallback to demo data if API fails
          setAccounts([
            {
              id: '1',
              accountName: 'الحساب الجاري الرئيسي',
              accountNumber: '**********',
              bankName: 'البنك الأهلي السعودي',
              branchName: 'فرع الرياض الرئيسي',
              accountType: 'CHECKING',
              currency: 'SAR',
              balance: 150000,
              isActive: true,
              description: 'الحساب الجاري الرئيسي للشركة',
              createdAt: '2024-01-15'
            },
            {
              id: '2',
              accountName: 'حساب التوفير',
              accountNumber: '**********',
              bankName: 'بنك الرياض',
              branchName: 'فرع العليا',
              accountType: 'SAVINGS',
              currency: 'SAR',
              balance: 75000,
              isActive: true,
              description: 'حساب توفير للاحتياطي النقدي',
              createdAt: '2024-02-01'
            },
            {
              id: '3',
              accountName: 'حساب بالدولار',
              accountNumber: '**********',
              bankName: 'البنك السعودي للاستثمار',
              branchName: 'فرع الملك فهد',
              accountType: 'CHECKING',
              currency: 'USD',
              balance: 25000,
              isActive: true,
              description: 'حساب بالعملة الأجنبية',
              createdAt: '2024-02-15'
            }
          ])
        }
      } catch (error) {
        console.error('Error fetching bank accounts:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchAccounts()
  }, [])

  const accountTypes = [...new Set(accounts.map(acc => acc.accountType))]

  const filteredAccounts = accounts.filter(account => {
    const matchesSearch = 
      account.accountName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.accountNumber.includes(searchTerm) ||
      account.bankName.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = typeFilter === 'ALL' || account.accountType === typeFilter
    const matchesStatus = statusFilter === 'ALL' || 
      (statusFilter === 'ACTIVE' && account.isActive) ||
      (statusFilter === 'INACTIVE' && !account.isActive)
    
    return matchesSearch && matchesType && matchesStatus
  })

  const handleDeleteAccount = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا الحساب البنكي؟')) {
      try {
        const response = await fetch(`/api/banking/accounts/${id}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          setAccounts(accounts.filter(acc => acc.id !== id))
          alert('تم حذف الحساب البنكي بنجاح!')
        } else {
          alert('حدث خطأ أثناء حذف الحساب البنكي')
        }
      } catch (error) {
        console.error('Error deleting bank account:', error)
        alert('حدث خطأ أثناء حذف الحساب البنكي')
      }
    }
  }

  const getAccountTypeLabel = (type: string) => {
    switch (type) {
      case 'CHECKING':
        return 'حساب جاري'
      case 'SAVINGS':
        return 'حساب توفير'
      case 'CREDIT':
        return 'حساب ائتماني'
      case 'LOAN':
        return 'حساب قرض'
      default:
        return type
    }
  }

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'CHECKING':
        return 'bg-blue-100 text-blue-800'
      case 'SAVINGS':
        return 'bg-green-100 text-green-800'
      case 'CREDIT':
        return 'bg-purple-100 text-purple-800'
      case 'LOAN':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (isActive: boolean) => {
    return isActive 
      ? 'bg-green-100 text-green-800'
      : 'bg-red-100 text-red-800'
  }

  const getStatusLabel = (isActive: boolean) => {
    return isActive ? 'نشط' : 'غير نشط'
  }

  const totalBalance = filteredAccounts.reduce((sum, acc) => {
    if (acc.currency === 'SAR') {
      return sum + acc.balance
    }
    // Convert other currencies to SAR (simplified conversion)
    return sum + (acc.balance * 3.75) // USD to SAR approximate rate
  }, 0)

  const activeAccounts = accounts.filter(acc => acc.isActive).length
  const totalAccounts = accounts.length

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة الحسابات البنكية</h1>
            <p className="mt-2 text-gray-600">إدارة الحسابات البنكية والمعاملات المالية</p>
          </div>
          <div className="flex gap-2">
            <Link
              href="/banking/transfers/new"
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
            >
              <TrendingUp className="h-5 w-5" />
              تحويل جديد
            </Link>
            <Link
              href="/banking/accounts/new"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
            >
              <Plus className="h-5 w-5" />
              إضافة حساب بنكي
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-500 p-3 rounded-lg">
                <CreditCard className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الحسابات</p>
                <p className="text-2xl font-bold text-blue-600">{totalAccounts}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-500 p-3 rounded-lg">
                <Building className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الحسابات النشطة</p>
                <p className="text-2xl font-bold text-green-600">{activeAccounts}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-purple-500 p-3 rounded-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الأرصدة</p>
                <p className="text-xl font-bold text-purple-600">
                  {totalBalance.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-orange-500 p-3 rounded-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">البنوك</p>
                <p className="text-2xl font-bold text-orange-600">
                  {[...new Set(accounts.map(acc => acc.bankName))].length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="البحث في الحسابات..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
            >
              <option value="ALL">جميع الأنواع</option>
              <option value="CHECKING">حساب جاري</option>
              <option value="SAVINGS">حساب توفير</option>
              <option value="CREDIT">حساب ائتماني</option>
              <option value="LOAN">حساب قرض</option>
            </select>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="ALL">جميع الحالات</option>
              <option value="ACTIVE">نشط</option>
              <option value="INACTIVE">غير نشط</option>
            </select>
          </div>
        </div>

        {/* Accounts Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحساب
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    البنك
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    النوع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الرصيد
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredAccounts.map((account) => (
                  <tr key={account.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{account.accountName}</div>
                        <div className="text-sm text-gray-500">{account.accountNumber}</div>
                        {account.description && (
                          <div className="text-xs text-gray-400 mt-1">{account.description}</div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{account.bankName}</div>
                      {account.branchName && (
                        <div className="text-sm text-gray-500">{account.branchName}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getAccountTypeColor(account.accountType)}`}>
                        {getAccountTypeLabel(account.accountType)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-bold text-gray-900">
                        {account.balance.toLocaleString()} {account.currency}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(account.isActive)}`}>
                        {getStatusLabel(account.isActive)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <Link
                          href={`/banking/accounts/${account.id}`}
                          className="text-blue-600 hover:text-blue-900"
                          title="عرض التفاصيل"
                        >
                          <Eye className="h-4 w-4" />
                        </Link>
                        <Link
                          href={`/banking/accounts/${account.id}/transactions`}
                          className="text-green-600 hover:text-green-900"
                          title="المعاملات"
                        >
                          <TrendingUp className="h-4 w-4" />
                        </Link>
                        <Link
                          href={`/banking/accounts/${account.id}/edit`}
                          className="text-yellow-600 hover:text-yellow-900"
                          title="تعديل"
                        >
                          <Edit className="h-4 w-4" />
                        </Link>
                        <button 
                          onClick={() => handleDeleteAccount(account.id)}
                          className="text-red-600 hover:text-red-900"
                          title="حذف"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredAccounts.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد حسابات بنكية مطابقة لبحثك</p>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  )
}
