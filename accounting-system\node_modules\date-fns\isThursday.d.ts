import type { ContextOptions, DateArg } from "./types.js";
/**
 * The {@link isThursday} function options.
 */
export interface IsThursdayOptions extends ContextOptions<Date> {}
/**
 * @name isThursday
 * @category Weekday Helpers
 * @summary Is the given date Thursday?
 *
 * @description
 * Is the given date Thursday?
 *
 * @param date - The date to check
 * @param options - An object with options
 *
 * @returns The date is Thursday
 *
 * @example
 * // Is 25 September 2014 Thursday?
 * const result = isThursday(new Date(2014, 8, 25))
 * //=> true
 */
export declare function isThursday(
  date: DateArg<Date> & {},
  options?: IsThursdayOptions | undefined,
): boolean;
