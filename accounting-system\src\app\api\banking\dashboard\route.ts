import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get account statistics
    const totalAccounts = await prisma.bankAccount.count()
    const activeAccounts = await prisma.bankAccount.count({
      where: { isActive: true }
    })

    // Get total balance (sum of all active accounts)
    const accounts = await prisma.bankAccount.findMany({
      where: { isActive: true }
    })
    
    const totalBalance = accounts.reduce((sum, account) => {
      // Convert all balances to SAR for total calculation
      if (account.currency === 'SAR') {
        return sum + account.balance
      } else if (account.currency === 'USD') {
        return sum + (account.balance * 3.75) // USD to SAR
      } else if (account.currency === 'EUR') {
        return sum + (account.balance * 4.1) // EUR to SAR
      } else if (account.currency === 'GBP') {
        return sum + (account.balance * 4.7) // GBP to SAR
      }
      return sum + account.balance
    }, 0)

    // Get transaction statistics
    const totalTransactions = await prisma.bankTransaction.count()

    // Get transfer statistics
    const pendingTransfers = await prisma.bankTransfer.count({
      where: { status: 'PENDING' }
    })

    // Get monthly flow (current month)
    const currentMonth = new Date().getMonth() + 1
    const currentYear = new Date().getFullYear()
    const startOfMonth = new Date(currentYear, currentMonth - 1, 1)
    const endOfMonth = new Date(currentYear, currentMonth, 0)

    const monthlyTransactions = await prisma.bankTransaction.findMany({
      where: {
        transactionDate: {
          gte: startOfMonth,
          lte: endOfMonth
        },
        status: 'COMPLETED'
      }
    })

    const monthlyInflow = monthlyTransactions
      .filter(t => t.type === 'DEPOSIT' || t.type === 'TRANSFER_IN')
      .reduce((sum, t) => sum + t.amount, 0)

    const monthlyOutflow = monthlyTransactions
      .filter(t => t.type === 'WITHDRAWAL' || t.type === 'TRANSFER_OUT')
      .reduce((sum, t) => sum + t.amount, 0)

    const stats = {
      totalAccounts,
      activeAccounts,
      totalBalance: Math.round(totalBalance),
      totalTransactions,
      pendingTransfers,
      monthlyInflow,
      monthlyOutflow
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching banking dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
