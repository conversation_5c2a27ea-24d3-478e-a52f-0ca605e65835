"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/expenses/page",{

/***/ "(app-pages-browser)/./src/app/expenses/page.tsx":
/*!***********************************!*\
  !*** ./src/app/expenses/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExpensesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(app-pages-browser)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CreditCard_Edit_Plus_Receipt_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CreditCard,Edit,Plus,Receipt,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CreditCard_Edit_Plus_Receipt_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CreditCard,Edit,Plus,Receipt,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CreditCard_Edit_Plus_Receipt_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CreditCard,Edit,Plus,Receipt,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CreditCard_Edit_Plus_Receipt_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CreditCard,Edit,Plus,Receipt,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CreditCard_Edit_Plus_Receipt_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CreditCard,Edit,Plus,Receipt,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CreditCard_Edit_Plus_Receipt_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CreditCard,Edit,Plus,Receipt,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CreditCard_Edit_Plus_Receipt_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CreditCard,Edit,Plus,Receipt,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ExpensesPage() {\n    _s();\n    const [expenses, setExpenses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [categoryFilter, setCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [dateFilter, setDateFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ExpensesPage.useEffect\": ()=>{\n            const fetchExpenses = {\n                \"ExpensesPage.useEffect.fetchExpenses\": async ()=>{\n                    try {\n                        const response = await fetch('/api/expenses');\n                        if (response.ok) {\n                            const data = await response.json();\n                            // Map API data to match component interface\n                            const mappedExpenses = data.map({\n                                \"ExpensesPage.useEffect.fetchExpenses.mappedExpenses\": (expense)=>({\n                                        id: expense.id,\n                                        title: expense.description,\n                                        description: expense.notes || expense.description,\n                                        amount: expense.amount,\n                                        date: expense.date,\n                                        category: expense.category,\n                                        categoryColor: getCategoryColor(expense.category)\n                                    })\n                            }[\"ExpensesPage.useEffect.fetchExpenses.mappedExpenses\"]);\n                            setExpenses(mappedExpenses);\n                        } else {\n                            // Fallback to demo data if API fails\n                            setExpenses([\n                                {\n                                    id: '1',\n                                    title: 'إيجار المكتب',\n                                    description: 'إيجار شهر يناير 2024',\n                                    amount: 8000,\n                                    date: '2024-01-01',\n                                    category: 'إيجار',\n                                    categoryColor: 'bg-blue-500'\n                                },\n                                {\n                                    id: '2',\n                                    title: 'فواتير الكهرباء',\n                                    description: 'فاتورة كهرباء شهر يناير',\n                                    amount: 450,\n                                    date: '2024-01-15',\n                                    category: 'مرافق',\n                                    categoryColor: 'bg-yellow-500'\n                                },\n                                {\n                                    id: '3',\n                                    title: 'مواد مكتبية',\n                                    description: 'أقلام وأوراق ومستلزمات مكتبية',\n                                    amount: 320,\n                                    date: '2024-01-20',\n                                    category: 'مستلزمات',\n                                    categoryColor: 'bg-green-500'\n                                },\n                                {\n                                    id: '4',\n                                    title: 'وقود السيارات',\n                                    description: 'تعبئة وقود للسيارات الخدمية',\n                                    amount: 600,\n                                    date: '2024-02-01',\n                                    category: 'مواصلات',\n                                    categoryColor: 'bg-red-500'\n                                },\n                                {\n                                    id: '5',\n                                    title: 'اشتراك الإنترنت',\n                                    description: 'اشتراك شهري للإنترنت',\n                                    amount: 200,\n                                    date: '2024-02-05',\n                                    category: 'مرافق',\n                                    categoryColor: 'bg-yellow-500'\n                                }\n                            ]);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching expenses:', error);\n                    // Use demo data as fallback\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ExpensesPage.useEffect.fetchExpenses\"];\n            fetchExpenses();\n        }\n    }[\"ExpensesPage.useEffect\"], []);\n    const getCategoryColor = (category)=>{\n        const colors = {\n            'إيجار': 'bg-blue-500',\n            'مرافق': 'bg-yellow-500',\n            'مستلزمات': 'bg-green-500',\n            'مواصلات': 'bg-red-500',\n            'مصاريف إدارية': 'bg-purple-500',\n            'مصاريف تشغيلية': 'bg-indigo-500',\n            'مصاريف تسويق': 'bg-pink-500',\n            'مصاريف سفر': 'bg-orange-500',\n            'مصاريف مكتبية': 'bg-green-500',\n            'مصاريف صيانة': 'bg-gray-500',\n            'مصاريف اتصالات': 'bg-cyan-500',\n            'مصاريف أخرى': 'bg-slate-500'\n        };\n        return colors[category] || 'bg-gray-500';\n    };\n    const categories = [\n        ...new Set(expenses.map((expense)=>expense.category))\n    ];\n    const filteredExpenses = expenses.filter((expense)=>{\n        var _expense_description;\n        const matchesSearch = expense.title.toLowerCase().includes(searchTerm.toLowerCase()) || ((_expense_description = expense.description) === null || _expense_description === void 0 ? void 0 : _expense_description.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = categoryFilter === 'ALL' || expense.category === categoryFilter;\n        let matchesDate = true;\n        if (dateFilter !== 'ALL') {\n            const expenseDate = new Date(expense.date);\n            const now = new Date();\n            switch(dateFilter){\n                case 'THIS_MONTH':\n                    matchesDate = expenseDate.getMonth() === now.getMonth() && expenseDate.getFullYear() === now.getFullYear();\n                    break;\n                case 'LAST_MONTH':\n                    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1);\n                    matchesDate = expenseDate.getMonth() === lastMonth.getMonth() && expenseDate.getFullYear() === lastMonth.getFullYear();\n                    break;\n                case 'THIS_YEAR':\n                    matchesDate = expenseDate.getFullYear() === now.getFullYear();\n                    break;\n            }\n        }\n        return matchesSearch && matchesCategory && matchesDate;\n    });\n    const handleDeleteExpense = async (id)=>{\n        if (confirm('هل أنت متأكد من حذف هذا المصروف؟')) {\n            try {\n                const response = await fetch(\"/api/expenses/\".concat(id), {\n                    method: 'DELETE'\n                });\n                if (response.ok) {\n                    setExpenses(expenses.filter((expense)=>expense.id !== id));\n                    alert('تم حذف المصروف بنجاح!');\n                } else {\n                    alert('حدث خطأ أثناء حذف المصروف');\n                }\n            } catch (error) {\n                console.error('Error deleting expense:', error);\n                alert('حدث خطأ أثناء حذف المصروف');\n            }\n        }\n    };\n    const getTotalExpenses = ()=>{\n        return filteredExpenses.reduce((sum, expense)=>sum + expense.amount, 0);\n    };\n    const getExpensesByCategory = ()=>{\n        const categoryTotals = {};\n        filteredExpenses.forEach((expense)=>{\n            categoryTotals[expense.category] = (categoryTotals[expense.category] || 0) + expense.amount;\n        });\n        return categoryTotals;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                lineNumber: 188,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n            lineNumber: 187,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"إدارة المصروفات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-gray-600\",\n                                    children: \"تتبع وإدارة مصروفات الشركة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/expenses/new\",\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CreditCard_Edit_Plus_Receipt_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this),\n                                \"إضافة مصروف جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CreditCard_Edit_Plus_Receipt_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"إجمالي المصروفات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-red-600\",\n                                                children: [\n                                                    getTotalExpenses().toLocaleString(),\n                                                    \" ر.س\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CreditCard_Edit_Plus_Receipt_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"عدد المصروفات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: filteredExpenses.length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CreditCard_Edit_Plus_Receipt_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"متوسط المصروف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: [\n                                                    filteredExpenses.length > 0 ? Math.round(getTotalExpenses() / filteredExpenses.length).toLocaleString() : 0,\n                                                    \" ر.س\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CreditCard_Edit_Plus_Receipt_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"التصنيفات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-purple-600\",\n                                                children: categories.length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CreditCard_Edit_Plus_Receipt_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"البحث في المصروفات...\",\n                                        className: \"w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                value: categoryFilter,\n                                onChange: (e)=>setCategoryFilter(e.target.value),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"ALL\",\n                                        children: \"جميع التصنيفات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this),\n                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: category,\n                                            children: category\n                                        }, category, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                value: dateFilter,\n                                onChange: (e)=>setDateFilter(e.target.value),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"ALL\",\n                                        children: \"جميع الفترات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"THIS_MONTH\",\n                                        children: \"هذا الشهر\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"LAST_MONTH\",\n                                        children: \"الشهر الماضي\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"THIS_YEAR\",\n                                        children: \"هذا العام\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"عنوان المصروف\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"الوصف\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"التصنيف\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"المبلغ\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"التاريخ\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"الإجراءات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: filteredExpenses.map((expense)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: expense.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-900 max-w-xs truncate\",\n                                                            children: expense.description || '-'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full text-white \".concat(expense.categoryColor),\n                                                            children: expense.category\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-red-600\",\n                                                            children: [\n                                                                \"-\",\n                                                                expense.amount.toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: new Date(expense.date).toLocaleDateString('ar-SA')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-blue-600 hover:text-blue-900\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CreditCard_Edit_Plus_Receipt_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleDeleteExpense(expense.id),\n                                                                    className: \"text-red-600 hover:text-red-900\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CreditCard_Edit_Plus_Receipt_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, expense.id, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        filteredExpenses.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"لا توجد مصروفات مطابقة لبحثك\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                            children: \"ملخص المصروفات حسب التصنيف\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                            children: Object.entries(getExpensesByCategory()).map((param)=>{\n                                let [category, total] = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-900\",\n                                            children: category\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-bold text-red-600\",\n                                            children: [\n                                                total.toLocaleString(),\n                                                \" ر.س\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, category, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\expenses\\\\page.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n_s(ExpensesPage, \"0eAHydgU4ppNyT56bhmEJrWBpwc=\");\n_c = ExpensesPage;\nvar _c;\n$RefreshReg$(_c, \"ExpensesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/expenses/page.tsx\n"));

/***/ })

});