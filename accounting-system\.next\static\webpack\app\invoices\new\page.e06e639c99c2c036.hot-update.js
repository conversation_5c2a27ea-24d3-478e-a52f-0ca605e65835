"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/invoices/new/page",{

/***/ "(app-pages-browser)/./src/app/invoices/new/page.tsx":
/*!***************************************!*\
  !*** ./src/app/invoices/new/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewInvoicePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(app-pages-browser)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction NewInvoicePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [invoice, setInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: '',\n        issueDate: new Date().toISOString().split('T')[0],\n        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n        status: 'DRAFT',\n        notes: '',\n        taxRate: 15 // 15% VAT\n    });\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            productId: '',\n            productName: '',\n            quantity: 1,\n            price: 0,\n            total: 0\n        }\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NewInvoicePage.useEffect\": ()=>{\n            const fetchData = {\n                \"NewInvoicePage.useEffect.fetchData\": async ()=>{\n                    try {\n                        const [customersRes, productsRes] = await Promise.all([\n                            fetch('/api/customers'),\n                            fetch('/api/products')\n                        ]);\n                        if (customersRes.ok && productsRes.ok) {\n                            const customersData = await customersRes.json();\n                            const productsData = await productsRes.json();\n                            setCustomers(customersData);\n                            setProducts(productsData);\n                        } else {\n                            // Fallback to demo data if API fails\n                            setCustomers([\n                                {\n                                    id: '1',\n                                    name: 'شركة الأمل للتجارة',\n                                    email: '<EMAIL>',\n                                    phone: '+966501234567',\n                                    address: 'الرياض، المملكة العربية السعودية',\n                                    taxNumber: '*********'\n                                },\n                                {\n                                    id: '2',\n                                    name: 'مؤسسة النور للخدمات',\n                                    email: '<EMAIL>',\n                                    phone: '+966507654321',\n                                    address: 'جدة، المملكة العربية السعودية',\n                                    taxNumber: '*********'\n                                },\n                                {\n                                    id: '3',\n                                    name: 'شركة الفجر للتطوير',\n                                    email: '<EMAIL>',\n                                    phone: '+966551234567',\n                                    address: 'الدمام، المملكة العربية السعودية',\n                                    taxNumber: '*********'\n                                }\n                            ]);\n                            setProducts([\n                                {\n                                    id: '1',\n                                    name: 'خدمة استشارات إدارية',\n                                    price: 500,\n                                    unit: 'ساعة'\n                                },\n                                {\n                                    id: '2',\n                                    name: 'تصميم موقع إلكتروني',\n                                    price: 5000,\n                                    unit: 'مشروع'\n                                },\n                                {\n                                    id: '3',\n                                    name: 'خدمة التسويق الرقمي',\n                                    price: 2000,\n                                    unit: 'شهر'\n                                }\n                            ]);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching data:', error);\n                    // Use demo data as fallback\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"NewInvoicePage.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"NewInvoicePage.useEffect\"], []);\n    const addItem = ()=>{\n        const newItem = {\n            id: Date.now().toString(),\n            productId: '',\n            productName: '',\n            quantity: 1,\n            price: 0,\n            total: 0\n        };\n        setItems([\n            ...items,\n            newItem\n        ]);\n    };\n    const removeItem = (id)=>{\n        if (items.length > 1) {\n            setItems(items.filter((item)=>item.id !== id));\n        }\n    };\n    const updateItem = (id, field, value)=>{\n        setItems(items.map((item)=>{\n            if (item.id === id) {\n                const updatedItem = {\n                    ...item,\n                    [field]: value\n                };\n                // If product is selected, update price and name\n                if (field === 'productId') {\n                    const product = products.find((p)=>p.id === value);\n                    if (product) {\n                        updatedItem.productName = product.name;\n                        updatedItem.price = product.price;\n                    }\n                }\n                // Calculate total\n                updatedItem.total = updatedItem.quantity * updatedItem.price;\n                return updatedItem;\n            }\n            return item;\n        }));\n    };\n    const calculateSubtotal = ()=>{\n        return items.reduce((sum, item)=>sum + item.total, 0);\n    };\n    const calculateTax = ()=>{\n        return calculateSubtotal() * invoice.taxRate / 100;\n    };\n    const calculateTotal = ()=>{\n        return calculateSubtotal() + calculateTax();\n    };\n    const generateInvoiceNumber = ()=>{\n        const now = new Date();\n        const year = now.getFullYear();\n        const month = String(now.getMonth() + 1).padStart(2, '0');\n        const day = String(now.getDate()).padStart(2, '0');\n        const time = String(now.getTime()).slice(-4);\n        return \"INV-\".concat(year).concat(month).concat(day, \"-\").concat(time);\n    };\n    const handleSave = async (overrideStatus)=>{\n        if (!invoice.customerId) {\n            alert('يرجى اختيار العميل');\n            return;\n        }\n        if (items.some((item)=>!item.productId || item.quantity <= 0)) {\n            alert('يرجى التأكد من صحة جميع عناصر الفاتورة');\n            return;\n        }\n        setSaving(true);\n        try {\n            const finalStatus = overrideStatus || invoice.status;\n            const invoiceData = {\n                number: generateInvoiceNumber(),\n                customerId: invoice.customerId,\n                issueDate: invoice.issueDate,\n                dueDate: invoice.dueDate,\n                status: finalStatus,\n                subtotal: calculateSubtotal(),\n                taxAmount: calculateTax(),\n                total: calculateTotal(),\n                notes: invoice.notes,\n                items: items.filter((item)=>item.productId)\n            };\n            const response = await fetch('/api/invoices', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(invoiceData)\n            });\n            if (response.ok) {\n                const statusLabels = {\n                    DRAFT: 'حفظ كمسودة',\n                    SENT: 'إرسال',\n                    PAID: 'تسجيل كمدفوعة',\n                    OVERDUE: 'تسجيل كمتأخرة',\n                    CANCELLED: 'إلغاء'\n                };\n                alert(\"تم \".concat(statusLabels[finalStatus], \" الفاتورة بنجاح!\"));\n                router.push('/invoices');\n            } else {\n                const error = await response.json();\n                alert(\"حدث خطأ: \".concat(error.error || 'خطأ غير معروف'));\n            }\n        } catch (error) {\n            console.error('Error saving invoice:', error);\n            alert('حدث خطأ أثناء حفظ الفاتورة');\n        } finally{\n            setSaving(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n            lineNumber: 257,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/invoices'),\n                                className: \"flex items-center gap-2 text-gray-600 hover:text-gray-900\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"العودة للفواتير\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"إنشاء فاتورة جديدة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"إنشاء فاتورة جديدة للعملاء\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"العميل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: invoice.customerId,\n                                            onChange: (e)=>setInvoice({\n                                                    ...invoice,\n                                                    customerId: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            required: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"اختر العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: customer.id,\n                                                        children: customer.name\n                                                    }, customer.id, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"تاريخ الإصدار\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"date\",\n                                                    value: invoice.issueDate,\n                                                    onChange: (e)=>setInvoice({\n                                                            ...invoice,\n                                                            issueDate: e.target.value\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"تاريخ الاستحقاق\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"date\",\n                                                    value: invoice.dueDate,\n                                                    onChange: (e)=>setInvoice({\n                                                            ...invoice,\n                                                            dueDate: e.target.value\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"حالة الفاتورة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: invoice.status,\n                                                    onChange: (e)=>setInvoice({\n                                                            ...invoice,\n                                                            status: e.target.value\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"DRAFT\",\n                                                            children: \"مسودة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"SENT\",\n                                                            children: \"مرسلة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"PAID\",\n                                                            children: \"مدفوعة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"OVERDUE\",\n                                                            children: \"متأخرة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"CANCELLED\",\n                                                            children: \"ملغية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"عناصر الفاتورة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addItem,\n                                            className: \"flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"إضافة عنصر\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"min-w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"المنتج/الخدمة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"الكمية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"السعر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"المجموع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"الإجراءات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: item.productId,\n                                                                    onChange: (e)=>updateItem(item.id, 'productId', e.target.value),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"اختر المنتج/الخدمة\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 383,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: product.id,\n                                                                                children: [\n                                                                                    product.name,\n                                                                                    \" - \",\n                                                                                    product.price,\n                                                                                    \" ر.س/\",\n                                                                                    product.unit\n                                                                                ]\n                                                                            }, product.id, true, {\n                                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 385,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    min: \"1\",\n                                                                    value: item.quantity,\n                                                                    onChange: (e)=>updateItem(item.id, 'quantity', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    min: \"0\",\n                                                                    step: \"0.01\",\n                                                                    value: item.price,\n                                                                    onChange: (e)=>updateItem(item.id, 'price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4 font-medium\",\n                                                                children: [\n                                                                    item.total.toLocaleString(),\n                                                                    \" ر.س\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>removeItem(item.id),\n                                                                    disabled: items.length === 1,\n                                                                    className: \"text-red-600 hover:text-red-900 disabled:text-gray-400 disabled:cursor-not-allowed\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"ملاحظات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: invoice.notes,\n                                            onChange: (e)=>setInvoice({\n                                                    ...invoice,\n                                                    notes: e.target.value\n                                                }),\n                                            rows: 4,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"ملاحظات إضافية...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"المجموع الفرعي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            calculateSubtotal().toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            \"ضريبة القيمة المضافة (\",\n                                                            invoice.taxRate,\n                                                            \"%):\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            calculateTax().toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 pt-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-lg font-bold\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"المجموع الكلي:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-600\",\n                                                            children: [\n                                                                calculateTotal().toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-4 mt-8 pt-6 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/invoices'),\n                                    className: \"px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSave('DRAFT'),\n                                    disabled: saving,\n                                    className: \"px-6 py-2 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 disabled:opacity-50\",\n                                    children: saving ? 'جاري الحفظ...' : 'حفظ كمسودة'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSave('SENT'),\n                                    disabled: saving,\n                                    className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50\",\n                                    children: saving ? 'جاري الإرسال...' : 'حفظ وإرسال'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n            lineNumber: 267,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n        lineNumber: 266,\n        columnNumber: 5\n    }, this);\n}\n_s(NewInvoicePage, \"CUxGI2QNNfmCtzWNzINLVJHyqX8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewInvoicePage;\nvar _c;\n$RefreshReg$(_c, \"NewInvoicePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/invoices/new/page.tsx\n"));

/***/ })

});