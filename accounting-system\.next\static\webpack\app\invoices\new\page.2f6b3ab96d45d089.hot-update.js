"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/invoices/new/page",{

/***/ "(app-pages-browser)/./src/app/invoices/new/page.tsx":
/*!***************************************!*\
  !*** ./src/app/invoices/new/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewInvoicePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(app-pages-browser)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction NewInvoicePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [invoice, setInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: '',\n        issueDate: new Date().toISOString().split('T')[0],\n        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n        status: 'DRAFT',\n        notes: '',\n        taxRate: 15 // 15% VAT\n    });\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            productId: '',\n            productName: '',\n            quantity: 1,\n            price: 0,\n            total: 0\n        }\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NewInvoicePage.useEffect\": ()=>{\n            const fetchData = {\n                \"NewInvoicePage.useEffect.fetchData\": async ()=>{\n                    try {\n                        const [customersRes, productsRes] = await Promise.all([\n                            fetch('/api/customers'),\n                            fetch('/api/products')\n                        ]);\n                        if (customersRes.ok && productsRes.ok) {\n                            const customersData = await customersRes.json();\n                            const productsData = await productsRes.json();\n                            setCustomers(customersData);\n                            setProducts(productsData);\n                        } else {\n                            // Fallback to demo data if API fails\n                            setCustomers([\n                                {\n                                    id: '1',\n                                    name: 'شركة الأمل للتجارة',\n                                    email: '<EMAIL>',\n                                    phone: '+966501234567',\n                                    address: 'الرياض، المملكة العربية السعودية',\n                                    taxNumber: '*********'\n                                },\n                                {\n                                    id: '2',\n                                    name: 'مؤسسة النور للخدمات',\n                                    email: '<EMAIL>',\n                                    phone: '+966507654321',\n                                    address: 'جدة، المملكة العربية السعودية',\n                                    taxNumber: '*********'\n                                },\n                                {\n                                    id: '3',\n                                    name: 'شركة الفجر للتطوير',\n                                    email: '<EMAIL>',\n                                    phone: '+966551234567',\n                                    address: 'الدمام، المملكة العربية السعودية',\n                                    taxNumber: '*********'\n                                }\n                            ]);\n                            setProducts([\n                                {\n                                    id: '1',\n                                    name: 'خدمة استشارات إدارية',\n                                    price: 500,\n                                    unit: 'ساعة'\n                                },\n                                {\n                                    id: '2',\n                                    name: 'تصميم موقع إلكتروني',\n                                    price: 5000,\n                                    unit: 'مشروع'\n                                },\n                                {\n                                    id: '3',\n                                    name: 'خدمة التسويق الرقمي',\n                                    price: 2000,\n                                    unit: 'شهر'\n                                }\n                            ]);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching data:', error);\n                    // Use demo data as fallback\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"NewInvoicePage.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"NewInvoicePage.useEffect\"], []);\n    const addItem = ()=>{\n        const newItem = {\n            id: Date.now().toString(),\n            productId: '',\n            productName: '',\n            quantity: 1,\n            price: 0,\n            total: 0\n        };\n        setItems([\n            ...items,\n            newItem\n        ]);\n    };\n    const removeItem = (id)=>{\n        if (items.length > 1) {\n            setItems(items.filter((item)=>item.id !== id));\n        }\n    };\n    const updateItem = (id, field, value)=>{\n        setItems(items.map((item)=>{\n            if (item.id === id) {\n                const updatedItem = {\n                    ...item,\n                    [field]: value\n                };\n                // If product is selected, update price and name\n                if (field === 'productId') {\n                    const product = products.find((p)=>p.id === value);\n                    if (product) {\n                        updatedItem.productName = product.name;\n                        updatedItem.price = product.price;\n                    }\n                }\n                // Calculate total\n                updatedItem.total = updatedItem.quantity * updatedItem.price;\n                return updatedItem;\n            }\n            return item;\n        }));\n    };\n    const calculateSubtotal = ()=>{\n        return items.reduce((sum, item)=>sum + item.total, 0);\n    };\n    const calculateTax = ()=>{\n        return calculateSubtotal() * invoice.taxRate / 100;\n    };\n    const calculateTotal = ()=>{\n        return calculateSubtotal() + calculateTax();\n    };\n    const generateInvoiceNumber = ()=>{\n        const now = new Date();\n        const year = now.getFullYear();\n        const month = String(now.getMonth() + 1).padStart(2, '0');\n        const day = String(now.getDate()).padStart(2, '0');\n        const time = String(now.getTime()).slice(-4);\n        return \"INV-\".concat(year).concat(month).concat(day, \"-\").concat(time);\n    };\n    const handleSave = async (status)=>{\n        if (!invoice.customerId) {\n            alert('يرجى اختيار العميل');\n            return;\n        }\n        if (items.some((item)=>!item.productId || item.quantity <= 0)) {\n            alert('يرجى التأكد من صحة جميع عناصر الفاتورة');\n            return;\n        }\n        setSaving(true);\n        try {\n            const invoiceData = {\n                number: generateInvoiceNumber(),\n                customerId: invoice.customerId,\n                issueDate: invoice.issueDate,\n                dueDate: invoice.dueDate,\n                status,\n                subtotal: calculateSubtotal(),\n                taxAmount: calculateTax(),\n                total: calculateTotal(),\n                notes: invoice.notes,\n                items: items.filter((item)=>item.productId)\n            };\n            const response = await fetch('/api/invoices', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(invoiceData)\n            });\n            if (response.ok) {\n                alert(\"تم \".concat(status === 'DRAFT' ? 'حفظ' : 'إرسال', \" الفاتورة بنجاح!\"));\n                router.push('/invoices');\n            } else {\n                const error = await response.json();\n                alert(\"حدث خطأ: \".concat(error.error || 'خطأ غير معروف'));\n            }\n        } catch (error) {\n            console.error('Error saving invoice:', error);\n            alert('حدث خطأ أثناء حفظ الفاتورة');\n        } finally{\n            setSaving(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                lineNumber: 250,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n            lineNumber: 249,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/invoices'),\n                                className: \"flex items-center gap-2 text-gray-600 hover:text-gray-900\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"العودة للفواتير\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"إنشاء فاتورة جديدة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"إنشاء فاتورة جديدة للعملاء\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"العميل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: invoice.customerId,\n                                            onChange: (e)=>setInvoice({\n                                                    ...invoice,\n                                                    customerId: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            required: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"اختر العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: customer.id,\n                                                        children: customer.name\n                                                    }, customer.id, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"تاريخ الإصدار\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"date\",\n                                                    value: invoice.issueDate,\n                                                    onChange: (e)=>setInvoice({\n                                                            ...invoice,\n                                                            issueDate: e.target.value\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"تاريخ الاستحقاق\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"date\",\n                                                    value: invoice.dueDate,\n                                                    onChange: (e)=>setInvoice({\n                                                            ...invoice,\n                                                            dueDate: e.target.value\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"عناصر الفاتورة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addItem,\n                                            className: \"flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"إضافة عنصر\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"min-w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"المنتج/الخدمة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"الكمية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"السعر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"المجموع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"الإجراءات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: item.productId,\n                                                                    onChange: (e)=>updateItem(item.id, 'productId', e.target.value),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"اختر المنتج/الخدمة\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 358,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: product.id,\n                                                                                children: [\n                                                                                    product.name,\n                                                                                    \" - \",\n                                                                                    product.price,\n                                                                                    \" ر.س/\",\n                                                                                    product.unit\n                                                                                ]\n                                                                            }, product.id, true, {\n                                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 360,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    min: \"1\",\n                                                                    value: item.quantity,\n                                                                    onChange: (e)=>updateItem(item.id, 'quantity', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    min: \"0\",\n                                                                    step: \"0.01\",\n                                                                    value: item.price,\n                                                                    onChange: (e)=>updateItem(item.id, 'price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4 font-medium\",\n                                                                children: [\n                                                                    item.total.toLocaleString(),\n                                                                    \" ر.س\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>removeItem(item.id),\n                                                                    disabled: items.length === 1,\n                                                                    className: \"text-red-600 hover:text-red-900 disabled:text-gray-400 disabled:cursor-not-allowed\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 394,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"ملاحظات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: invoice.notes,\n                                            onChange: (e)=>setInvoice({\n                                                    ...invoice,\n                                                    notes: e.target.value\n                                                }),\n                                            rows: 4,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"ملاحظات إضافية...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"المجموع الفرعي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            calculateSubtotal().toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            \"ضريبة القيمة المضافة (\",\n                                                            invoice.taxRate,\n                                                            \"%):\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            calculateTax().toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 pt-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-lg font-bold\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"المجموع الكلي:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-600\",\n                                                            children: [\n                                                                calculateTotal().toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-4 mt-8 pt-6 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/invoices'),\n                                    className: \"px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSave('DRAFT'),\n                                    disabled: saving,\n                                    className: \"px-6 py-2 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 disabled:opacity-50\",\n                                    children: saving ? 'جاري الحفظ...' : 'حفظ كمسودة'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSave('SENT'),\n                                    disabled: saving,\n                                    className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50\",\n                                    children: saving ? 'جاري الإرسال...' : 'حفظ وإرسال'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n            lineNumber: 259,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n        lineNumber: 258,\n        columnNumber: 5\n    }, this);\n}\n_s(NewInvoicePage, \"CUxGI2QNNfmCtzWNzINLVJHyqX8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewInvoicePage;\nvar _c;\n$RefreshReg$(_c, \"NewInvoicePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/invoices/new/page.tsx\n"));

/***/ })

});