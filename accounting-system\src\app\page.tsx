'use client'

import { useEffect, useState } from 'react'
import MainLayout from '@/components/Layout/MainLayout'
import {
  Users,
  FileText,
  CreditCard,
  TrendingUp,
  DollarSign,
  Calendar,
  AlertCircle,
  Package,
  UserCheck,
  Building
} from 'lucide-react'

interface DashboardStats {
  totalCustomers: number
  totalInvoices: number
  totalRevenue: number
  totalExpenses: number
  pendingInvoices: number
  overdueInvoices: number
  totalProducts: number
  totalEmployees: number
  totalAssets: number
}

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalCustomers: 0,
    totalInvoices: 0,
    totalRevenue: 0,
    totalExpenses: 0,
    pendingInvoices: 0,
    overdueInvoices: 0,
    totalProducts: 0,
    totalEmployees: 0,
    totalAssets: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        const response = await fetch('/api/dashboard')
        if (response.ok) {
          const data = await response.json()
          setStats(data)
        } else {
          console.error('Failed to fetch dashboard stats')
        }
      } catch (error) {
        console.error('Error fetching dashboard stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardStats()
  }, [])

  const statCards = [
    {
      title: 'إجمالي العملاء',
      value: stats.totalCustomers,
      icon: Users,
      color: 'bg-blue-500',
      textColor: 'text-blue-600'
    },
    {
      title: 'إجمالي الفواتير',
      value: stats.totalInvoices,
      icon: FileText,
      color: 'bg-green-500',
      textColor: 'text-green-600'
    },
    {
      title: 'إجمالي الإيرادات',
      value: `${stats.totalRevenue.toLocaleString()} ر.س`,
      icon: TrendingUp,
      color: 'bg-purple-500',
      textColor: 'text-purple-600'
    },
    {
      title: 'إجمالي المصروفات',
      value: `${stats.totalExpenses.toLocaleString()} ر.س`,
      icon: CreditCard,
      color: 'bg-red-500',
      textColor: 'text-red-600'
    },
    {
      title: 'الفواتير المعلقة',
      value: stats.pendingInvoices,
      icon: Calendar,
      color: 'bg-yellow-500',
      textColor: 'text-yellow-600'
    },
    {
      title: 'الفواتير المتأخرة',
      value: stats.overdueInvoices,
      icon: AlertCircle,
      color: 'bg-orange-500',
      textColor: 'text-orange-600'
    },
    {
      title: 'إجمالي المنتجات',
      value: stats.totalProducts,
      icon: Package,
      color: 'bg-indigo-500',
      textColor: 'text-indigo-600'
    },
    {
      title: 'إجمالي الموظفين',
      value: stats.totalEmployees,
      icon: UserCheck,
      color: 'bg-pink-500',
      textColor: 'text-pink-600'
    },
    {
      title: 'إجمالي الأصول',
      value: stats.totalAssets,
      icon: Building,
      color: 'bg-teal-500',
      textColor: 'text-teal-600'
    }
  ]

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">لوحة التحكم</h1>
          <p className="mt-2 text-gray-600">نظرة عامة على أداء نشاطك التجاري</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {statCards.map((card, index) => (
            <div key={index} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className={`${card.color} p-3 rounded-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className={`text-2xl font-bold ${card.textColor}`}>
                    {card.value}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">آخر الفواتير</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {[1, 2, 3].map((item) => (
                  <div key={item} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        فاتورة #{1000 + item}
                      </p>
                      <p className="text-sm text-gray-500">عميل {item}</p>
                    </div>
                    <div className="text-left">
                      <p className="text-sm font-medium text-gray-900">
                        {(5000 + item * 1000).toLocaleString()} ر.س
                      </p>
                      <p className="text-sm text-gray-500">
                        {new Date().toLocaleDateString('ar-SA')}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">آخر المصروفات</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {[1, 2, 3].map((item) => (
                  <div key={item} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        مصروف {item}
                      </p>
                      <p className="text-sm text-gray-500">تصنيف {item}</p>
                    </div>
                    <div className="text-left">
                      <p className="text-sm font-medium text-red-600">
                        -{(500 + item * 200).toLocaleString()} ر.س
                      </p>
                      <p className="text-sm text-gray-500">
                        {new Date().toLocaleDateString('ar-SA')}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
