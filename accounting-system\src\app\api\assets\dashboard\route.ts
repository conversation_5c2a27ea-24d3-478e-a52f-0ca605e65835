import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get asset statistics
    const totalAssets = await prisma.asset.count()
    const activeAssets = await prisma.asset.count({
      where: { status: 'ACTIVE' }
    })

    // Get all assets for calculations
    const assets = await prisma.asset.findMany()

    // Calculate total values
    const totalValue = assets.reduce((sum, asset) => sum + asset.purchasePrice, 0)
    const depreciatedValue = assets.reduce((sum, asset) => sum + asset.currentValue, 0)

    // Get unique categories count
    const categories = await prisma.asset.findMany({
      select: { category: true },
      distinct: ['category']
    })
    const categoriesCount = categories.length

    // Check for maintenance due (assets with nextMaintenance date in the past or within 7 days)
    const now = new Date()
    const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
    
    const maintenanceDue = await prisma.asset.count({
      where: {
        AND: [
          { status: 'ACTIVE' },
          {
            OR: [
              {
                nextMaintenance: {
                  lte: sevenDaysFromNow
                }
              },
              {
                lastMaintenance: {
                  lte: new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000) // More than 1 year ago
                }
              }
            ]
          }
        ]
      }
    })

    // Check for warranty expiring (within 30 days)
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
    
    const warrantyExpiring = await prisma.asset.count({
      where: {
        AND: [
          { status: 'ACTIVE' },
          {
            warrantyExpiry: {
              lte: thirtyDaysFromNow,
              gte: now
            }
          }
        ]
      }
    })

    const stats = {
      totalAssets,
      activeAssets,
      totalValue: Math.round(totalValue),
      depreciatedValue: Math.round(depreciatedValue),
      maintenanceDue,
      warrantyExpiring,
      categories: categoriesCount
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching assets dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
