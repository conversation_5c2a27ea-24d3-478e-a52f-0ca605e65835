import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('بدء إنشاء البيانات التجريبية...')

  // إنشاء مستخدم مشرف
  const hashedPassword = await bcrypt.hash('admin123', 12)
  
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'المشرف العام',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'ADMIN'
    }
  })

  // إنشاء مستخدم مدير
  const managerPassword = await bcrypt.hash('manager123', 12)
  const manager = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'مدير الحسابات',
      email: '<EMAIL>',
      password: managerPassword,
      role: 'MANAGER'
    }
  })

  // إنشاء مستخدم محاسب
  const accountantPassword = await bcrypt.hash('accountant123', 12)
  const accountant = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'المحاسب الرئيسي',
      email: '<EMAIL>',
      password: accountantPassword,
      role: 'ACCOUNTANT'
    }
  })

  console.log('تم إنشاء المستخدمين...')

  // إنشاء تصنيفات المصروفات
  const categories = await Promise.all([
    prisma.expenseCategory.upsert({
      where: { name: 'إيجار' },
      update: {},
      create: { name: 'إيجار', color: 'bg-blue-500' }
    }),
    prisma.expenseCategory.upsert({
      where: { name: 'مرافق' },
      update: {},
      create: { name: 'مرافق', color: 'bg-yellow-500' }
    }),
    prisma.expenseCategory.upsert({
      where: { name: 'مستلزمات' },
      update: {},
      create: { name: 'مستلزمات', color: 'bg-green-500' }
    }),
    prisma.expenseCategory.upsert({
      where: { name: 'مواصلات' },
      update: {},
      create: { name: 'مواصلات', color: 'bg-red-500' }
    }),
    prisma.expenseCategory.upsert({
      where: { name: 'تسويق' },
      update: {},
      create: { name: 'تسويق', color: 'bg-purple-500' }
    })
  ])

  console.log('تم إنشاء تصنيفات المصروفات...')

  // إنشاء عملاء تجريبيين
  const customers = await Promise.all([
    prisma.customer.upsert({
      where: { name: 'شركة الأمل للتجارة' },
      update: {},
      create: {
        name: 'شركة الأمل للتجارة',
        email: '<EMAIL>',
        phone: '+966501234567',
        address: 'الرياض، المملكة العربية السعودية',
        taxNumber: '*********'
      }
    }),
    prisma.customer.upsert({
      where: { name: 'مؤسسة النور للخدمات' },
      update: {},
      create: {
        name: 'مؤسسة النور للخدمات',
        email: '<EMAIL>',
        phone: '+966507654321',
        address: 'جدة، المملكة العربية السعودية',
        taxNumber: '*********'
      }
    }),
    prisma.customer.upsert({
      where: { name: 'شركة الفجر للتطوير' },
      update: {},
      create: {
        name: 'شركة الفجر للتطوير',
        email: '<EMAIL>',
        phone: '+966551234567',
        address: 'الدمام، المملكة العربية السعودية',
        taxNumber: '*********'
      }
    })
  ])

  console.log('تم إنشاء العملاء...')

  // إنشاء منتجات وخدمات
  const products = await Promise.all([
    prisma.product.upsert({
      where: { name: 'خدمة استشارات إدارية' },
      update: {},
      create: {
        name: 'خدمة استشارات إدارية',
        description: 'استشارات إدارية متخصصة للشركات',
        price: 500,
        unit: 'ساعة',
        isActive: true
      }
    }),
    prisma.product.upsert({
      where: { name: 'تصميم موقع إلكتروني' },
      update: {},
      create: {
        name: 'تصميم موقع إلكتروني',
        description: 'تصميم وتطوير مواقع إلكترونية احترافية',
        price: 5000,
        unit: 'مشروع',
        isActive: true
      }
    }),
    prisma.product.upsert({
      where: { name: 'خدمة التسويق الرقمي' },
      update: {},
      create: {
        name: 'خدمة التسويق الرقمي',
        description: 'حملات تسويقية رقمية شاملة',
        price: 2000,
        unit: 'شهر',
        isActive: true
      }
    })
  ])

  console.log('تم إنشاء المنتجات والخدمات...')

  // إنشاء فواتير تجريبية
  const invoices = await Promise.all([
    prisma.invoice.create({
      data: {
        number: 'INV-001',
        customerId: customers[0].id,
        userId: admin.id,
        issueDate: new Date('2024-01-15'),
        dueDate: new Date('2024-02-15'),
        status: 'PAID',
        subtotal: 15000,
        taxAmount: 2250,
        total: 17250,
        notes: 'فاتورة خدمات استشارية',
        items: {
          create: [
            {
              productId: products[0].id,
              quantity: 30,
              price: 500,
              total: 15000
            }
          ]
        }
      }
    }),
    prisma.invoice.create({
      data: {
        number: 'INV-002',
        customerId: customers[1].id,
        userId: manager.id,
        issueDate: new Date('2024-01-20'),
        dueDate: new Date('2024-02-20'),
        status: 'SENT',
        subtotal: 5000,
        taxAmount: 750,
        total: 5750,
        notes: 'فاتورة تصميم موقع إلكتروني',
        items: {
          create: [
            {
              productId: products[1].id,
              quantity: 1,
              price: 5000,
              total: 5000
            }
          ]
        }
      }
    })
  ])

  console.log('تم إنشاء الفواتير...')

  // إنشاء مصروفات تجريبية
  const expenses = await Promise.all([
    prisma.expense.create({
      data: {
        title: 'إيجار المكتب',
        description: 'إيجار شهر يناير 2024',
        amount: 8000,
        date: new Date('2024-01-01'),
        categoryId: categories[0].id,
        userId: admin.id
      }
    }),
    prisma.expense.create({
      data: {
        title: 'فواتير الكهرباء',
        description: 'فاتورة كهرباء شهر يناير',
        amount: 450,
        date: new Date('2024-01-15'),
        categoryId: categories[1].id,
        userId: accountant.id
      }
    }),
    prisma.expense.create({
      data: {
        title: 'مواد مكتبية',
        description: 'أقلام وأوراق ومستلزمات مكتبية',
        amount: 320,
        date: new Date('2024-01-20'),
        categoryId: categories[2].id,
        userId: accountant.id
      }
    })
  ])

  console.log('تم إنشاء المصروفات...')

  console.log('✅ تم إنشاء جميع البيانات التجريبية بنجاح!')
  console.log('\n📋 بيانات تسجيل الدخول:')
  console.log('المشرف: <EMAIL> / admin123')
  console.log('المدير: <EMAIL> / manager123')
  console.log('المحاسب: <EMAIL> / accountant123')
}

main()
  .catch((e) => {
    console.error('❌ خطأ في إنشاء البيانات التجريبية:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
