'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import MainLayout from '@/components/Layout/MainLayout'
import { Save, ArrowLeft, CreditCard } from 'lucide-react'

const bankNames = [
  'البنك الأهلي السعودي',
  'بنك الرياض',
  'البنك السعودي للاستثمار',
  'البنك السعودي الفرنسي',
  'البنك العربي الوطني',
  'بنك ساب',
  'بنك الجزيرة',
  'البنك الأول',
  'بنك البلاد',
  'بنك الإنماء',
  'مصرف الراجحي',
  'بنك آخر'
]

const accountTypes = [
  { value: 'CHECKING', label: 'حساب جاري' },
  { value: 'SAVINGS', label: 'حساب توفير' },
  { value: 'CREDIT', label: 'حساب ائتماني' },
  { value: 'LOAN', label: 'حساب قرض' }
]

const currencies = [
  { value: 'SAR', label: 'ريال سعودي (SAR)' },
  { value: 'USD', label: 'دولار أمريكي (USD)' },
  { value: 'EUR', label: 'يورو (EUR)' },
  { value: 'GBP', label: 'جنيه إسترليني (GBP)' }
]

export default function NewBankAccountPage() {
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [account, setAccount] = useState({
    accountName: '',
    accountNumber: '',
    bankName: '',
    branchName: '',
    accountType: '',
    currency: 'SAR',
    balance: '',
    isActive: true,
    description: ''
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setAccount(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!account.accountName || !account.accountNumber || !account.bankName || !account.accountType) {
      alert('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    if (parseFloat(account.balance) < 0) {
      alert('لا يمكن أن يكون الرصيد سالباً')
      return
    }

    setSaving(true)

    try {
      const accountData = {
        accountName: account.accountName,
        accountNumber: account.accountNumber,
        bankName: account.bankName,
        branchName: account.branchName || null,
        accountType: account.accountType,
        currency: account.currency,
        balance: parseFloat(account.balance) || 0,
        isActive: account.isActive,
        description: account.description || null
      }

      const response = await fetch('/api/banking/accounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(accountData),
      })

      if (response.ok) {
        alert('تم إضافة الحساب البنكي بنجاح!')
        router.push('/banking/accounts')
      } else {
        const error = await response.json()
        alert(`حدث خطأ: ${error.error || 'خطأ غير معروف'}`)
      }
    } catch (error) {
      console.error('Error saving bank account:', error)
      alert('حدث خطأ أثناء حفظ الحساب البنكي')
    } finally {
      setSaving(false)
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.push('/banking/accounts')}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5" />
            العودة للحسابات البنكية
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إضافة حساب بنكي جديد</h1>
            <p className="mt-2 text-gray-600">إضافة حساب بنكي جديد إلى النظام</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Account Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                معلومات الحساب
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="accountName" className="block text-sm font-medium text-gray-700 mb-2">
                    اسم الحساب *
                  </label>
                  <input
                    type="text"
                    id="accountName"
                    name="accountName"
                    value={account.accountName}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثل: الحساب الجاري الرئيسي"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="accountNumber" className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الحساب *
                  </label>
                  <input
                    type="text"
                    id="accountNumber"
                    name="accountNumber"
                    value={account.accountNumber}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="رقم الحساب البنكي"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="bankName" className="block text-sm font-medium text-gray-700 mb-2">
                    اسم البنك *
                  </label>
                  <select
                    id="bankName"
                    name="bankName"
                    value={account.bankName}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  >
                    <option value="">اختر البنك</option>
                    {bankNames.map(bank => (
                      <option key={bank} value={bank}>{bank}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="branchName" className="block text-sm font-medium text-gray-700 mb-2">
                    اسم الفرع
                  </label>
                  <input
                    type="text"
                    id="branchName"
                    name="branchName"
                    value={account.branchName}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثل: فرع الرياض الرئيسي"
                  />
                </div>

                <div>
                  <label htmlFor="accountType" className="block text-sm font-medium text-gray-700 mb-2">
                    نوع الحساب *
                  </label>
                  <select
                    id="accountType"
                    name="accountType"
                    value={account.accountType}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  >
                    <option value="">اختر نوع الحساب</option>
                    {accountTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-2">
                    العملة
                  </label>
                  <select
                    id="currency"
                    name="currency"
                    value={account.currency}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {currencies.map(currency => (
                      <option key={currency.value} value={currency.value}>{currency.label}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="balance" className="block text-sm font-medium text-gray-700 mb-2">
                    الرصيد الابتدائي
                  </label>
                  <input
                    type="number"
                    id="balance"
                    name="balance"
                    value={account.balance}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0.00"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    name="isActive"
                    checked={account.isActive}
                    onChange={handleChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isActive" className="mr-2 block text-sm text-gray-900">
                    حساب نشط
                  </label>
                </div>
              </div>

              <div className="mt-6">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  الوصف
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={account.description}
                  onChange={handleChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="وصف إضافي للحساب البنكي..."
                />
              </div>
            </div>

            <div className="flex justify-end gap-4 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => router.push('/banking/accounts')}
                className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                إلغاء
              </button>
              <button
                type="submit"
                disabled={saving}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {saving ? 'جاري الحفظ...' : 'حفظ الحساب'}
              </button>
            </div>
          </form>
        </div>

        {/* Help Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">نصائح لإضافة الحسابات البنكية:</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• تأكد من صحة رقم الحساب البنكي</li>
            <li>• اختر نوع الحساب المناسب (جاري، توفير، ائتماني، قرض)</li>
            <li>• أدخل الرصيد الابتدائي الصحيح</li>
            <li>• يمكنك إلغاء تفعيل الحساب لاحقاً إذا لم تعد تستخدمه</li>
            <li>• استخدم أسماء واضحة ومفهومة للحسابات</li>
          </ul>
        </div>
      </div>
    </MainLayout>
  )
}
