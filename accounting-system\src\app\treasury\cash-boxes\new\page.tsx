'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import MainLayout from '@/components/Layout/MainLayout'
import { Save, ArrowLeft, Wallet } from 'lucide-react'

const currencies = [
  { value: 'SAR', label: 'ريال سعودي (SAR)' },
  { value: 'USD', label: 'دولار أمريكي (USD)' },
  { value: 'EUR', label: 'يورو (EUR)' },
  { value: 'GBP', label: 'جنيه إسترليني (GBP)' }
]

const employees = [
  'أحمد محمد',
  'فاطمة أحمد',
  'خالد علي',
  'سارة محمود',
  'محمد حسن',
  'نورا عبدالله'
]

const locations = [
  'مكتب المحاسبة',
  'مكتب الاستقبال',
  'مكتب الإدارة',
  'الخزنة الرئيسية',
  'قسم المبيعات',
  'قسم المشتريات'
]

export default function NewCashBoxPage() {
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [cashBox, setCashBox] = useState({
    name: '',
    description: '',
    currency: 'SAR',
    balance: '',
    location: '',
    responsible: '',
    isActive: true,
    dailyLimit: '',
    notes: ''
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setCashBox(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!cashBox.name) {
      alert('يرجى إدخال اسم الصندوق النقدي')
      return
    }

    if (parseFloat(cashBox.balance) < 0) {
      alert('لا يمكن أن يكون الرصيد سالباً')
      return
    }

    if (cashBox.dailyLimit && parseFloat(cashBox.dailyLimit) <= 0) {
      alert('يجب أن يكون الحد اليومي أكبر من صفر')
      return
    }

    setSaving(true)

    try {
      const cashBoxData = {
        name: cashBox.name,
        description: cashBox.description || null,
        currency: cashBox.currency,
        balance: parseFloat(cashBox.balance) || 0,
        location: cashBox.location || null,
        responsible: cashBox.responsible || null,
        isActive: cashBox.isActive,
        dailyLimit: cashBox.dailyLimit ? parseFloat(cashBox.dailyLimit) : null,
        notes: cashBox.notes || null
      }

      const response = await fetch('/api/treasury/cash-boxes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cashBoxData),
      })

      if (response.ok) {
        alert('تم إضافة الصندوق النقدي بنجاح!')
        router.push('/treasury/cash-boxes')
      } else {
        const error = await response.json()
        alert(`حدث خطأ: ${error.error || 'خطأ غير معروف'}`)
      }
    } catch (error) {
      console.error('Error saving cash box:', error)
      alert('حدث خطأ أثناء حفظ الصندوق النقدي')
    } finally {
      setSaving(false)
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.push('/treasury/cash-boxes')}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5" />
            العودة للصناديق النقدية
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إضافة صندوق نقدي جديد</h1>
            <p className="mt-2 text-gray-600">إضافة صندوق نقدي جديد إلى النظام</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                <Wallet className="h-5 w-5" />
                معلومات الصندوق
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    اسم الصندوق *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={cashBox.name}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثل: الصندوق الرئيسي"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-2">
                    العملة
                  </label>
                  <select
                    id="currency"
                    name="currency"
                    value={cashBox.currency}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {currencies.map(currency => (
                      <option key={currency.value} value={currency.value}>{currency.label}</option>
                    ))}
                  </select>
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                    الوصف
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={cashBox.description}
                    onChange={handleChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="وصف الصندوق النقدي..."
                  />
                </div>

                <div>
                  <label htmlFor="balance" className="block text-sm font-medium text-gray-700 mb-2">
                    الرصيد الابتدائي
                  </label>
                  <input
                    type="number"
                    id="balance"
                    name="balance"
                    value={cashBox.balance}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label htmlFor="dailyLimit" className="block text-sm font-medium text-gray-700 mb-2">
                    الحد اليومي للمعاملات
                  </label>
                  <input
                    type="number"
                    id="dailyLimit"
                    name="dailyLimit"
                    value={cashBox.dailyLimit}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثل: 50000"
                  />
                </div>

                <div>
                  <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                    الموقع
                  </label>
                  <select
                    id="location"
                    name="location"
                    value={cashBox.location}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">اختر الموقع</option>
                    {locations.map(location => (
                      <option key={location} value={location}>{location}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="responsible" className="block text-sm font-medium text-gray-700 mb-2">
                    الموظف المسؤول
                  </label>
                  <select
                    id="responsible"
                    name="responsible"
                    value={cashBox.responsible}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">اختر الموظف</option>
                    {employees.map(employee => (
                      <option key={employee} value={employee}>{employee}</option>
                    ))}
                  </select>
                </div>

                <div className="md:col-span-2 flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    name="isActive"
                    checked={cashBox.isActive}
                    onChange={handleChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isActive" className="mr-2 block text-sm text-gray-900">
                    صندوق نشط
                  </label>
                </div>
              </div>

              <div className="mt-6">
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                  ملاحظات إضافية
                </label>
                <textarea
                  id="notes"
                  name="notes"
                  value={cashBox.notes}
                  onChange={handleChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="أي ملاحظات إضافية حول الصندوق النقدي..."
                />
              </div>
            </div>

            <div className="flex justify-end gap-4 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => router.push('/treasury/cash-boxes')}
                className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                إلغاء
              </button>
              <button
                type="submit"
                disabled={saving}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {saving ? 'جاري الحفظ...' : 'حفظ الصندوق'}
              </button>
            </div>
          </form>
        </div>

        {/* Help Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">نصائح لإنشاء الصناديق النقدية:</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• استخدم أسماء واضحة ومفهومة للصناديق</li>
            <li>• حدد الحد اليومي للمعاملات لتجنب تجاوز الحدود المسموحة</li>
            <li>• اختر موظف مسؤول موثوق لإدارة الصندوق</li>
            <li>• حدد الموقع بوضوح لسهولة الوصول للصندوق</li>
            <li>• يمكنك إلغاء تفعيل الصندوق مؤقتاً دون حذفه</li>
          </ul>
        </div>
      </div>
    </MainLayout>
  )
}
