"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(app-pages-browser)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Building,Calendar,CreditCard,Database,DollarSign,Edit,Eye,Globe,Mail,MapPin,Palette,Phone,RefreshCw,Save,Settings,Shield,Trash2,Upload,User,UserCheck,UserPlus,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SettingsPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            name: 'أحمد محمد',\n            email: '<EMAIL>',\n            role: 'ADMIN',\n            isActive: true,\n            lastLogin: '2024-03-15T10:30:00Z',\n            createdAt: '2024-01-01T00:00:00Z'\n        },\n        {\n            id: '2',\n            name: 'فاطمة أحمد',\n            email: '<EMAIL>',\n            role: 'MANAGER',\n            isActive: true,\n            lastLogin: '2024-03-14T15:45:00Z',\n            createdAt: '2024-01-15T00:00:00Z'\n        },\n        {\n            id: '3',\n            name: 'خالد علي',\n            email: '<EMAIL>',\n            role: 'ACCOUNTANT',\n            isActive: true,\n            lastLogin: '2024-03-13T09:20:00Z',\n            createdAt: '2024-02-01T00:00:00Z'\n        },\n        {\n            id: '4',\n            name: 'سارة محمود',\n            email: '<EMAIL>',\n            role: 'ACCOUNTANT',\n            isActive: false,\n            lastLogin: '2024-03-01T11:30:00Z',\n            createdAt: '2024-03-01T00:00:00Z'\n        }\n    ]);\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // Company Information\n        companyName: 'شركة المحاسبة المتقدمة',\n        companyNameEn: 'Advanced Accounting Company',\n        companyEmail: '<EMAIL>',\n        companyPhone: '+************',\n        companyFax: '+************',\n        companyWebsite: 'www.company.com',\n        companyAddress: 'الرياض، المملكة العربية السعودية',\n        companyAddressEn: 'Riyadh, Saudi Arabia',\n        taxNumber: '*********012345',\n        commercialRegister: '**********',\n        establishmentDate: '2020-01-01',\n        legalForm: 'شركة ذات مسؤولية محدودة',\n        capital: '1000000',\n        bankName: 'البنك الأهلي السعودي',\n        bankAccount: '*********',\n        iban: '************************',\n        swiftCode: 'NCBKSARI',\n        companyLogo: '',\n        // General Settings\n        currency: 'SAR',\n        language: 'ar',\n        timezone: 'Asia/Riyadh',\n        dateFormat: 'dd/mm/yyyy',\n        numberFormat: 'arabic',\n        fiscalYearStart: '01/01',\n        // Notifications\n        emailNotifications: true,\n        smsNotifications: false,\n        invoiceReminders: true,\n        paymentNotifications: true,\n        // Appearance\n        theme: 'light',\n        primaryColor: 'blue',\n        fontSize: 'medium',\n        compactSidebar: false,\n        animations: true\n    });\n    const tabs = [\n        {\n            id: 'general',\n            name: 'عام',\n            icon: _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            id: 'company',\n            name: 'معلومات الشركة',\n            icon: _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            id: 'users',\n            name: 'إدارة المستخدمين',\n            icon: _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            id: 'notifications',\n            name: 'الإشعارات',\n            icon: _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: 'security',\n            name: 'الأمان',\n            icon: _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: 'appearance',\n            name: 'المظهر',\n            icon: _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: 'system',\n            name: 'النظام',\n            icon: _barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        }\n    ];\n    const handleSave = ()=>{\n        // Here you would save the settings to the database\n        alert('تم حفظ الإعدادات بنجاح!');\n    };\n    const handleReset = ()=>{\n        if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {\n            // Reset to default values\n            alert('تم إعادة تعيين الإعدادات!');\n        }\n    };\n    const handleLogoUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                setSettings((prev)=>{\n                    var _e_target;\n                    return {\n                        ...prev,\n                        companyLogo: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                    };\n                });\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    const handleToggleUserStatus = (userId)=>{\n        setUsers(users.map((user)=>user.id === userId ? {\n                ...user,\n                isActive: !user.isActive\n            } : user));\n        alert('تم تحديث حالة المستخدم بنجاح!');\n    };\n    const handleDeleteUser = (userId)=>{\n        if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {\n            setUsers(users.filter((user)=>user.id !== userId));\n            alert('تم حذف المستخدم بنجاح!');\n        }\n    };\n    const getRoleLabel = (role)=>{\n        switch(role){\n            case 'ADMIN':\n                return 'مدير النظام';\n            case 'MANAGER':\n                return 'مدير';\n            case 'ACCOUNTANT':\n                return 'محاسب';\n            default:\n                return role;\n        }\n    };\n    const getRoleColor = (role)=>{\n        switch(role){\n            case 'ADMIN':\n                return 'bg-red-100 text-red-800';\n            case 'MANAGER':\n                return 'bg-purple-100 text-purple-800';\n            case 'ACCOUNTANT':\n                return 'bg-blue-100 text-blue-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const renderGeneralSettings = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"الإعدادات العامة\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"العملة الافتراضية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: settings.currency,\n                                        onChange: (e)=>setSettings({\n                                                ...settings,\n                                                currency: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"SAR\",\n                                                children: \"ريال سعودي (SAR)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"USD\",\n                                                children: \"دولار أمريكي (USD)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"EUR\",\n                                                children: \"يورو (EUR)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"اللغة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: settings.language,\n                                        onChange: (e)=>setSettings({\n                                                ...settings,\n                                                language: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"ar\",\n                                                children: \"العربية\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"en\",\n                                                children: \"English\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"المنطقة الزمنية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: settings.timezone,\n                                        onChange: (e)=>setSettings({\n                                                ...settings,\n                                                timezone: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Asia/Riyadh\",\n                                                children: \"الرياض\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Asia/Dubai\",\n                                                children: \"دبي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Asia/Kuwait\",\n                                                children: \"الكويت\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"تنسيق التاريخ\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: settings.dateFormat,\n                                        onChange: (e)=>setSettings({\n                                                ...settings,\n                                                dateFormat: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"dd/mm/yyyy\",\n                                                children: \"يوم/شهر/سنة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"mm/dd/yyyy\",\n                                                children: \"شهر/يوم/سنة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"yyyy-mm-dd\",\n                                                children: \"سنة-شهر-يوم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 192,\n            columnNumber: 5\n        }, this);\n    const renderCompanySettings = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-4 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 11\n                                }, this),\n                                \"شعار الشركة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center\",\n                                    children: settings.companyLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: settings.companyLogo,\n                                        alt: \"Company Logo\",\n                                        className: \"w-full h-full object-contain rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"file\",\n                                                    accept: \"image/*\",\n                                                    onChange: handleLogoUpload,\n                                                    className: \"hidden\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        \"رفع شعار\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 13\n                                        }, this),\n                                        settings.companyLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSettings((prev)=>({\n                                                        ...prev,\n                                                        companyLogo: ''\n                                                    })),\n                                            className: \"flex items-center gap-2 px-4 py-2 text-red-600 hover:text-red-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"حذف الشعار\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-4 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 11\n                                }, this),\n                                \"المعلومات الأساسية\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"اسم الشركة (عربي) *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: settings.companyName,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    companyName: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"اسم الشركة (إنجليزي)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: settings.companyNameEn,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    companyNameEn: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"البريد الإلكتروني *\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            value: settings.companyEmail,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    companyEmail: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"رقم الهاتف *\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"tel\",\n                                            value: settings.companyPhone,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    companyPhone: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"رقم الفاكس\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"tel\",\n                                            value: settings.companyFax,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    companyFax: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"الموقع الإلكتروني\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"url\",\n                                            value: settings.companyWebsite,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    companyWebsite: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"العنوان (عربي) *\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: settings.companyAddress,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    companyAddress: e.target.value\n                                                }),\n                                            rows: 3,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"العنوان (إنجليزي)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: settings.companyAddressEn,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    companyAddressEn: e.target.value\n                                                }),\n                                            rows: 3,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-4 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 11\n                                }, this),\n                                \"المعلومات القانونية\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"الرقم الضريبي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: settings.taxNumber,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    taxNumber: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"السجل التجاري\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: settings.commercialRegister,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    commercialRegister: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"تاريخ التأسيس\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: settings.establishmentDate,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    establishmentDate: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"الشكل القانوني\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: settings.legalForm,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    legalForm: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"اختر الشكل القانوني\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"شركة ذات مسؤولية محدودة\",\n                                                    children: \"شركة ذات مسؤولية محدودة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"شركة مساهمة\",\n                                                    children: \"شركة مساهمة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"شركة تضامن\",\n                                                    children: \"شركة تضامن\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"مؤسسة فردية\",\n                                                    children: \"مؤسسة فردية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"رأس المال\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: settings.capital,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    capital: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"العملة الأساسية\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: settings.currency,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    currency: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SAR\",\n                                                    children: \"ريال سعودي (SAR)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"USD\",\n                                                    children: \"دولار أمريكي (USD)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"EUR\",\n                                                    children: \"يورو (EUR)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"GBP\",\n                                                    children: \"جنيه إسترليني (GBP)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-4 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 11\n                                }, this),\n                                \"المعلومات البنكية\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"اسم البنك\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: settings.bankName,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    bankName: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"رقم الحساب\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: settings.bankAccount,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    bankAccount: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"رقم الآيبان (IBAN)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: settings.iban,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    iban: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"رمز السويفت (SWIFT)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: settings.swiftCode,\n                                            onChange: (e)=>setSettings({\n                                                    ...settings,\n                                                    swiftCode: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 513,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-blue-900 mb-2\",\n                            children: \"نصائح لإعدادات الشركة:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-sm text-blue-800 space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• تأكد من صحة جميع البيانات القانونية والضريبية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• استخدم شعار عالي الجودة بصيغة PNG أو SVG\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• احرص على تحديث المعلومات البنكية عند تغييرها\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• راجع البيانات دورياً للتأكد من دقتها\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• احتفظ بنسخة من الوثائق القانونية في مكان آمن\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 260,\n            columnNumber: 5\n        }, this);\n    const renderUsersSettings = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"إجمالي المستخدمين\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: users.length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 587,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"المستخدمين النشطين\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: users.filter((u)=>u.isActive).length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 599,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"المديرين\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-red-600\",\n                                                children: users.filter((u)=>u.role === 'ADMIN' || u.role === 'MANAGER').length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 614,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 613,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"غير النشطين\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-600\",\n                                                children: users.filter((u)=>!u.isActive).length\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 628,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 627,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 586,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"قائمة المستخدمين\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 644,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = '/settings/users/new',\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 11\n                                }, this),\n                                \"إضافة مستخدم جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 645,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 643,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"المستخدم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"الدور\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 663,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"الحالة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"آخر تسجيل دخول\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"الإجراءات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white divide-y divide-gray-200\",\n                                    children: users.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-100 p-2 rounded-full\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mr-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: user.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 686,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: user.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 687,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 685,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(getRoleColor(user.role)),\n                                                        children: getRoleLabel(user.role)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 692,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                        children: user.isActive ? 'نشط' : 'غير نشط'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                    children: user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('ar-SA') : 'لم يسجل دخول'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"text-blue-600 hover:text-blue-900\",\n                                                                title: \"عرض التفاصيل\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 717,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 713,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"text-yellow-600 hover:text-yellow-900\",\n                                                                title: \"تعديل\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 723,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 719,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleToggleUserStatus(user.id),\n                                                                className: \"\".concat(user.isActive ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'),\n                                                                title: user.isActive ? 'إلغاء التفعيل' : 'تفعيل',\n                                                                children: user.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 42\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 74\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            user.role !== 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDeleteUser(user.id),\n                                                                className: \"text-red-600 hover:text-red-900\",\n                                                                title: \"حذف\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, user.id, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 656,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 655,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-medium text-blue-900 mb-3\",\n                            children: \"إجراءات إدارة المستخدمين:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 752,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.href = '/settings/users/new',\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 758,\n                                            columnNumber: 13\n                                        }, this),\n                                        \"إضافة مستخدم جديد\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.href = '/settings/permissions',\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 765,\n                                            columnNumber: 13\n                                        }, this),\n                                        \"إدارة الصلاحيات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 761,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 13\n                                        }, this),\n                                        \"تقرير المستخدمين\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 768,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 753,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 751,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-yellow-900 mb-2\",\n                            children: \"نصائح لإدارة المستخدمين:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 777,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-sm text-yellow-800 space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• امنح كل مستخدم الصلاحيات المناسبة لدوره فقط\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 779,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• راجع قائمة المستخدمين دورياً وأزل الحسابات غير المستخدمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 780,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• تأكد من استخدام كلمات مرور قوية لجميع الحسابات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• فعل المصادقة الثنائية للحسابات الإدارية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 782,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• راقب أنشطة المستخدمين من خلال سجل الأنشطة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 783,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 778,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 776,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 584,\n            columnNumber: 5\n        }, this);\n    const renderNotificationSettings = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"إعدادات الإشعارات\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 792,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"إشعارات البريد الإلكتروني\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"تلقي إشعارات عبر البريد الإلكتروني\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: settings.emailNotifications,\n                                                onChange: (e)=>setSettings({\n                                                        ...settings,\n                                                        emailNotifications: e.target.checked\n                                                    }),\n                                                className: \"sr-only peer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 806,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 794,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"إشعارات الرسائل النصية\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"تلقي إشعارات عبر الرسائل النصية\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 813,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: settings.smsNotifications,\n                                                onChange: (e)=>setSettings({\n                                                        ...settings,\n                                                        smsNotifications: e.target.checked\n                                                    }),\n                                                className: \"sr-only peer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 816,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 822,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 810,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"تذكيرات الفواتير\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 828,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"إرسال تذكيرات للفواتير المستحقة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 827,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: settings.invoiceReminders,\n                                                onChange: (e)=>setSettings({\n                                                        ...settings,\n                                                        invoiceReminders: e.target.checked\n                                                    }),\n                                                className: \"sr-only peer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 832,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 838,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 831,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 826,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"إشعارات الدفع\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 844,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"تلقي إشعارات عند استلام المدفوعات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 845,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: settings.paymentNotifications,\n                                                onChange: (e)=>setSettings({\n                                                        ...settings,\n                                                        paymentNotifications: e.target.checked\n                                                    }),\n                                                className: \"sr-only peer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 848,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 854,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 847,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 842,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 793,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 791,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 790,\n            columnNumber: 5\n        }, this);\n    const renderTabContent = ()=>{\n        switch(activeTab){\n            case 'general':\n                return renderGeneralSettings();\n            case 'company':\n                return renderCompanySettings();\n            case 'users':\n                return renderUsersSettings();\n            case 'notifications':\n                return renderNotificationSettings();\n            case 'security':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: \"إعدادات الأمان\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 876,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 880,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mr-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-yellow-800\",\n                                                            children: \"تنبيه أمني\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-yellow-700\",\n                                                            children: \"تأكد من استخدام كلمات مرور قوية وتفعيل المصادقة الثنائية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 883,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 881,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 879,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 878,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"مدة انتهاء الجلسة (بالدقائق)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 890,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        defaultValue: \"30\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 889,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"عدد محاولات تسجيل الدخول المسموحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 901,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        defaultValue: \"5\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 904,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 888,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: \"تفعيل المصادقة الثنائية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 915,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"طبقة حماية إضافية لحسابك\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 916,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 919,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 920,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 918,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 913,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: \"تسجيل أنشطة المستخدمين\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 926,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"حفظ سجل بجميع العمليات المهمة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 927,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 925,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                defaultChecked: true,\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 930,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 931,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 924,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: \"إشعارات تسجيل الدخول\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 937,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"تنبيه عند تسجيل دخول من جهاز جديد\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 938,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 936,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                defaultChecked: true,\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 941,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 942,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 940,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 935,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 912,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 877,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 875,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 874,\n                    columnNumber: 11\n                }, this);\n            case 'appearance':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: \"إعدادات المظهر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 954,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                                children: \"المظهر العام\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 957,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                id: \"light\",\n                                                                name: \"theme\",\n                                                                value: \"light\",\n                                                                defaultChecked: true,\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 962,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"light\",\n                                                                className: \"flex flex-col items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 peer-checked:border-blue-500 peer-checked:bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-12 bg-white border border-gray-300 rounded mb-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 974,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"فاتح\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 975,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 970,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 961,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                id: \"dark\",\n                                                                name: \"theme\",\n                                                                value: \"dark\",\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 980,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"dark\",\n                                                                className: \"flex flex-col items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 peer-checked:border-blue-500 peer-checked:bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-12 bg-gray-800 border border-gray-600 rounded mb-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 991,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"داكن\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 992,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 987,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 979,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                id: \"auto\",\n                                                                name: \"theme\",\n                                                                value: \"auto\",\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 997,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"auto\",\n                                                                className: \"flex flex-col items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 peer-checked:border-blue-500 peer-checked:bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-12 bg-gradient-to-r from-white to-gray-800 border border-gray-300 rounded mb-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1008,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"تلقائي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1009,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1004,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 996,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 960,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 956,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                                children: \"اللون الأساسي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1016,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-6 gap-3\",\n                                                children: [\n                                                    'blue',\n                                                    'green',\n                                                    'purple',\n                                                    'red',\n                                                    'orange',\n                                                    'pink'\n                                                ].map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                id: color,\n                                                                name: \"primaryColor\",\n                                                                value: color,\n                                                                defaultChecked: color === 'blue',\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1022,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: color,\n                                                                className: \"block w-12 h-12 rounded-lg cursor-pointer border-2 border-gray-200 peer-checked:border-gray-800 bg-\".concat(color, \"-500\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1030,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, color, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1021,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1019,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 1015,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"حجم الخط\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1040,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                className: \"w-full md:w-1/3 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"small\",\n                                                        children: \"صغير\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1044,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"medium\",\n                                                        selected: true,\n                                                        children: \"متوسط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"large\",\n                                                        children: \"كبير\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1043,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 1039,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: \"الشريط الجانبي المضغوط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1053,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"إخفاء نصوص القائمة الجانبية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1054,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1052,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1057,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1058,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1056,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1051,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: \"الرسوم المتحركة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1064,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"تفعيل التأثيرات المتحركة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1065,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1063,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                defaultChecked: true,\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1068,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1069,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1067,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1062,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 1050,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 955,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 953,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 952,\n                    columnNumber: 11\n                }, this);\n            case 'system':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: \"إعدادات النظام\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 1081,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 1085,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mr-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-blue-800\",\n                                                            children: \"معلومات النظام\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 1087,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-blue-700\",\n                                                            children: \"إصدار النظام: 1.0.0 | قاعدة البيانات: SQLite | الخادم: Next.js\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 1088,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 1086,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 1084,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 1083,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-3\",\n                                                children: \"النسخ الاحتياطي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1094,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"تكرار النسخ الاحتياطي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1097,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"daily\",\n                                                                        children: \"يومياً\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1101,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"weekly\",\n                                                                        selected: true,\n                                                                        children: \"أسبوعياً\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1102,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"monthly\",\n                                                                        children: \"شهرياً\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1103,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1100,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1096,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"وقت النسخ الاحتياطي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1108,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"time\",\n                                                                defaultValue: \"02:00\",\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1111,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1107,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1095,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 flex gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                                                        children: \"إنشاء نسخة احتياطية الآن\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                                        children: \"استعادة من نسخة احتياطية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1119,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 1093,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-3\",\n                                                children: \"صيانة النظام\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1130,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: \"تنظيف الملفات المؤقتة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1134,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"حذف الملفات المؤقتة وتحسين الأداء\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1135,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1133,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200\",\n                                                                children: \"تنظيف\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1137,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1132,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: \"تحسين قاعدة البيانات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1144,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"إعادة فهرسة وتحسين قاعدة البيانات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1145,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1143,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200\",\n                                                                children: \"تحسين\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1147,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1142,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: \"مسح سجل الأنشطة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1154,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"حذف سجلات الأنشطة القديمة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1155,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1153,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200\",\n                                                                children: \"مسح\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1157,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1152,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1131,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 1129,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-3\",\n                                                children: \"إعدادات متقدمة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1165,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: \"وضع التطوير\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1169,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"تفعيل أدوات التطوير والتشخيص\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1170,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1168,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"sr-only peer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1173,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1174,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1172,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1167,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: \"تسجيل مفصل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1180,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"حفظ سجلات مفصلة لجميع العمليات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1181,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1179,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        defaultChecked: true,\n                                                                        className: \"sr-only peer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1184,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1185,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1183,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1178,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: \"التحديثات التلقائية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1191,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"تحديث النظام تلقائياً عند توفر إصدار جديد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1192,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1190,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        defaultChecked: true,\n                                                                        className: \"sr-only peer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1195,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 1196,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 1194,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1189,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1166,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 1164,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-red-800 mb-2\",\n                                                children: \"منطقة الخطر\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1203,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-700 mb-3\",\n                                                children: \"العمليات التالية لا يمكن التراجع عنها\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1204,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\",\n                                                        children: \"إعادة تعيين النظام\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1206,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\",\n                                                        children: \"حذف جميع البيانات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 1209,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1205,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 1202,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 1082,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 1080,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 1079,\n                    columnNumber: 11\n                }, this);\n            default:\n                return renderGeneralSettings();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"الإعدادات\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 1227,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"إدارة إعدادات النظام والشركة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 1228,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 1226,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"-mb-px flex space-x-8 px-6\",\n                                \"aria-label\": \"Tabs\",\n                                children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.id),\n                                        className: \"\".concat(activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', \" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 1244,\n                                                columnNumber: 19\n                                            }, this),\n                                            tab.name\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 1235,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 1233,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 1232,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: renderTabContent()\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 1251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 px-6 py-4 flex justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleReset,\n                                    className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 1260,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعادة تعيين\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 1256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSave,\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Building_Calendar_CreditCard_Database_DollarSign_Edit_Eye_Globe_Mail_MapPin_Palette_Phone_RefreshCw_Save_Settings_Shield_Trash2_Upload_User_UserCheck_UserPlus_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 1268,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"حفظ التغييرات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 1264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 1255,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 1231,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 1225,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 1224,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"JyD0apXoxOWn2OgauHwDaywoR0o=\");\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});