// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// User model for authentication and authorization
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      UserRole @default(ACCOUNTANT)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  invoices  Invoice[]
  expenses  Expense[]

  @@map("users")
}

model Employee {
  id              String    @id @default(cuid())
  employeeId      String    @unique
  firstName       String
  lastName        String
  email           String    @unique
  phone           String?
  address         String?
  dateOfBirth     DateTime?
  hireDate        DateTime
  position        String
  department      String
  salary          Float
  status          String    @default("ACTIVE") // ACTIVE, INACTIVE, TERMINATED
  profileImage    String?
  nationalId      String?   @unique
  emergencyContact String?
  emergencyPhone  String?
  bankAccount     String?
  notes           String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  leaves          Leave[]
  salaries        Salary[]
}

model Leave {
  id          String    @id @default(cuid())
  employeeId  String
  type        String    // ANNUAL, SICK, EMERGENCY, MATERNITY, etc.
  startDate   DateTime
  endDate     DateTime
  days        Int
  reason      String?
  status      String    @default("PENDING") // PENDING, APPROVED, REJECTED
  approvedBy  String?
  approvedAt  DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  employee    Employee  @relation(fields: [employeeId], references: [id], onDelete: Cascade)
}

model Salary {
  id              String    @id @default(cuid())
  employeeId      String
  month           Int       // 1-12
  year            Int
  basicSalary     Float
  allowances      Float     @default(0)
  deductions      Float     @default(0)
  overtime        Float     @default(0)
  bonus           Float     @default(0)
  netSalary       Float
  status          String    @default("PENDING") // PENDING, PAID
  paidAt          DateTime?
  notes           String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  employee        Employee  @relation(fields: [employeeId], references: [id], onDelete: Cascade)

  @@unique([employeeId, month, year])
}

model BankAccount {
  id              String    @id @default(cuid())
  accountName     String
  accountNumber   String    @unique
  bankName        String
  branchName      String?
  accountType     String    // CHECKING, SAVINGS, CREDIT, LOAN
  currency        String    @default("SAR")
  balance         Float     @default(0)
  isActive        Boolean   @default(true)
  description     String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  transactions    BankTransaction[]
}

model BankTransaction {
  id              String    @id @default(cuid())
  accountId       String
  type            String    // DEPOSIT, WITHDRAWAL, TRANSFER_IN, TRANSFER_OUT
  amount          Float
  balance         Float     // Balance after transaction
  description     String
  reference       String?
  category        String?
  transactionDate DateTime
  status          String    @default("COMPLETED") // PENDING, COMPLETED, CANCELLED
  relatedId       String?   // Related invoice, expense, etc.
  relatedType     String?   // INVOICE, EXPENSE, SALARY, etc.
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  account         BankAccount @relation(fields: [accountId], references: [id], onDelete: Cascade)
}

model BankTransfer {
  id              String    @id @default(cuid())
  fromAccountId   String
  toAccountId     String
  amount          Float
  description     String
  reference       String?
  transferDate    DateTime
  status          String    @default("PENDING") // PENDING, COMPLETED, CANCELLED
  fees            Float     @default(0)
  exchangeRate    Float     @default(1)
  notes           String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

model Asset {
  id              String    @id @default(cuid())
  assetCode       String    @unique
  name            String
  description     String?
  category        String    // EQUIPMENT, FURNITURE, VEHICLE, BUILDING, COMPUTER, SOFTWARE
  subCategory     String?
  brand           String?
  model           String?
  serialNumber    String?
  purchaseDate    DateTime
  purchasePrice   Float
  currentValue    Float
  depreciationRate Float   @default(0)
  depreciationMethod String @default("STRAIGHT_LINE") // STRAIGHT_LINE, DECLINING_BALANCE
  usefulLife      Int       // in years
  location        String?
  department      String?
  assignedTo      String?   // Employee ID or department
  condition       String    @default("GOOD") // EXCELLENT, GOOD, FAIR, POOR, DAMAGED
  status          String    @default("ACTIVE") // ACTIVE, INACTIVE, DISPOSED, SOLD, LOST
  warrantyExpiry  DateTime?
  insuranceExpiry DateTime?
  lastMaintenance DateTime?
  nextMaintenance DateTime?
  supplier        String?
  invoiceNumber   String?
  notes           String?
  image           String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  maintenanceRecords AssetMaintenance[]
  depreciationRecords AssetDepreciation[]
  transfers       AssetTransfer[]
}

model AssetMaintenance {
  id              String    @id @default(cuid())
  assetId         String
  type            String    // PREVENTIVE, CORRECTIVE, EMERGENCY
  description     String
  cost            Float     @default(0)
  performedBy     String?   // Internal or external
  performedDate   DateTime
  nextDueDate     DateTime?
  status          String    @default("COMPLETED") // SCHEDULED, IN_PROGRESS, COMPLETED, CANCELLED
  notes           String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  asset           Asset     @relation(fields: [assetId], references: [id], onDelete: Cascade)
}

model AssetDepreciation {
  id              String    @id @default(cuid())
  assetId         String
  year            Int
  month           Int
  depreciationAmount Float
  accumulatedDepreciation Float
  bookValue       Float
  method          String    // STRAIGHT_LINE, DECLINING_BALANCE
  createdAt       DateTime  @default(now())

  // Relations
  asset           Asset     @relation(fields: [assetId], references: [id], onDelete: Cascade)

  @@unique([assetId, year, month])
}

model AssetTransfer {
  id              String    @id @default(cuid())
  assetId         String
  fromLocation    String?
  toLocation      String?
  fromDepartment  String?
  toDepartment    String?
  fromEmployee    String?
  toEmployee      String?
  transferDate    DateTime
  reason          String
  approvedBy      String?
  status          String    @default("PENDING") // PENDING, APPROVED, COMPLETED, CANCELLED
  notes           String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  asset           Asset     @relation(fields: [assetId], references: [id], onDelete: Cascade)
}

enum UserRole {
  ADMIN
  MANAGER
  ACCOUNTANT
}

// Customer model
model Customer {
  id          String   @id @default(cuid())
  name        String   @unique
  email       String?
  phone       String?
  address     String?
  taxNumber   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  invoices    Invoice[]

  @@map("customers")
}

// Product/Service model
model Product {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  price       Float
  unit        String?  // e.g., "piece", "hour", "kg"
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  invoiceItems InvoiceItem[]

  @@map("products")
}

// Invoice model
model Invoice {
  id          String        @id @default(cuid())
  number      String        @unique
  customerId  String
  userId      String
  issueDate   DateTime      @default(now())
  dueDate     DateTime
  status      InvoiceStatus @default(DRAFT)
  subtotal    Float
  taxAmount   Float         @default(0)
  total       Float
  notes       String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  customer     Customer      @relation(fields: [customerId], references: [id])
  user         User          @relation(fields: [userId], references: [id])
  items        InvoiceItem[]

  @@map("invoices")
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
}

// Invoice Item model
model InvoiceItem {
  id        String  @id @default(cuid())
  invoiceId String
  productId String
  quantity  Float
  price     Float
  total     Float

  // Relations
  invoice   Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  product   Product @relation(fields: [productId], references: [id])

  @@map("invoice_items")
}

// Expense Category model
model ExpenseCategory {
  id        String   @id @default(cuid())
  name      String   @unique
  color     String?  // For UI display
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  expenses  Expense[]

  @@map("expense_categories")
}

// Expense model
model Expense {
  id          String          @id @default(cuid())
  title       String
  description String?
  amount      Float
  date        DateTime        @default(now())
  categoryId  String
  userId      String
  receipt     String?         // File path for receipt image
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  // Relations
  category    ExpenseCategory @relation(fields: [categoryId], references: [id])
  user        User            @relation(fields: [userId], references: [id])

  @@map("expenses")
}
