'use client'

import { useState } from 'react'
import MainLayout from '@/components/Layout/MainLayout'
import {
  Settings,
  User,
  Bell,
  Shield,
  Database,
  Palette,
  Globe,
  Save,
  RefreshCw,
  Building,
  Upload,
  X,
  MapPin,
  Phone,
  Mail,
  CreditCard,
  Calendar,
  DollarSign,
  Users,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  UserCheck,
  UserX,
  UserPlus
} from 'lucide-react'

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('general')
  const [users, setUsers] = useState([
    {
      id: '1',
      name: 'أحمد محمد',
      email: '<EMAIL>',
      role: 'ADMIN',
      isActive: true,
      lastLogin: '2024-03-15T10:30:00Z',
      createdAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      name: 'فاطمة أحمد',
      email: '<EMAIL>',
      role: 'MANAGER',
      isActive: true,
      lastLogin: '2024-03-14T15:45:00Z',
      createdAt: '2024-01-15T00:00:00Z'
    },
    {
      id: '3',
      name: 'خالد علي',
      email: '<EMAIL>',
      role: 'ACCOUNTANT',
      isActive: true,
      lastLogin: '2024-03-13T09:20:00Z',
      createdAt: '2024-02-01T00:00:00Z'
    },
    {
      id: '4',
      name: 'سارة محمود',
      email: '<EMAIL>',
      role: 'ACCOUNTANT',
      isActive: false,
      lastLogin: '2024-03-01T11:30:00Z',
      createdAt: '2024-03-01T00:00:00Z'
    }
  ])
  const [settings, setSettings] = useState({
    // Company Information
    companyName: 'شركة المحاسبة المتقدمة',
    companyNameEn: 'Advanced Accounting Company',
    companyEmail: '<EMAIL>',
    companyPhone: '+************',
    companyFax: '+************',
    companyWebsite: 'www.company.com',
    companyAddress: 'الرياض، المملكة العربية السعودية',
    companyAddressEn: 'Riyadh, Saudi Arabia',
    taxNumber: '*********012345',
    commercialRegister: '**********',
    establishmentDate: '2020-01-01',
    legalForm: 'شركة ذات مسؤولية محدودة',
    capital: '1000000',
    bankName: 'البنك الأهلي السعودي',
    bankAccount: '*********',
    iban: 'SA*********0*********012',
    swiftCode: 'NCBKSARI',
    companyLogo: '',
    // General Settings
    currency: 'SAR',
    language: 'ar',
    timezone: 'Asia/Riyadh',
    dateFormat: 'dd/mm/yyyy',
    numberFormat: 'arabic',
    fiscalYearStart: '01/01',
    // Notifications
    emailNotifications: true,
    smsNotifications: false,
    invoiceReminders: true,
    paymentNotifications: true,
    // Appearance
    theme: 'light',
    primaryColor: 'blue',
    fontSize: 'medium',
    compactSidebar: false,
    animations: true
  })

  const tabs = [
    { id: 'general', name: 'عام', icon: Settings },
    { id: 'company', name: 'معلومات الشركة', icon: Building },
    { id: 'users', name: 'إدارة المستخدمين', icon: User },
    { id: 'notifications', name: 'الإشعارات', icon: Bell },
    { id: 'security', name: 'الأمان', icon: Shield },
    { id: 'appearance', name: 'المظهر', icon: Palette },
    { id: 'system', name: 'النظام', icon: Database }
  ]

  const handleSave = () => {
    // Here you would save the settings to the database
    alert('تم حفظ الإعدادات بنجاح!')
  }

  const handleReset = () => {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
      // Reset to default values
      alert('تم إعادة تعيين الإعدادات!')
    }
  }

  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setSettings(prev => ({
          ...prev,
          companyLogo: e.target?.result as string
        }))
      }
      reader.readAsDataURL(file)
    }
  }

  const handleToggleUserStatus = (userId: string) => {
    setUsers(users.map(user =>
      user.id === userId ? { ...user, isActive: !user.isActive } : user
    ))
    alert('تم تحديث حالة المستخدم بنجاح!')
  }

  const handleDeleteUser = (userId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      setUsers(users.filter(user => user.id !== userId))
      alert('تم حذف المستخدم بنجاح!')
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'مدير النظام'
      case 'MANAGER':
        return 'مدير'
      case 'ACCOUNTANT':
        return 'محاسب'
      default:
        return role
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'bg-red-100 text-red-800'
      case 'MANAGER':
        return 'bg-purple-100 text-purple-800'
      case 'ACCOUNTANT':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">الإعدادات العامة</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              العملة الافتراضية
            </label>
            <select
              value={settings.currency}
              onChange={(e) => setSettings({...settings, currency: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="SAR">ريال سعودي (SAR)</option>
              <option value="USD">دولار أمريكي (USD)</option>
              <option value="EUR">يورو (EUR)</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اللغة
            </label>
            <select
              value={settings.language}
              onChange={(e) => setSettings({...settings, language: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="ar">العربية</option>
              <option value="en">English</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              المنطقة الزمنية
            </label>
            <select
              value={settings.timezone}
              onChange={(e) => setSettings({...settings, timezone: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="Asia/Riyadh">الرياض</option>
              <option value="Asia/Dubai">دبي</option>
              <option value="Asia/Kuwait">الكويت</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              تنسيق التاريخ
            </label>
            <select
              value={settings.dateFormat}
              onChange={(e) => setSettings({...settings, dateFormat: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="dd/mm/yyyy">يوم/شهر/سنة</option>
              <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
              <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  )

  const renderCompanySettings = () => (
    <div className="space-y-8">
      {/* Company Logo */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
          <Building className="h-5 w-5" />
          شعار الشركة
        </h3>
        <div className="flex items-center gap-6">
          <div className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
            {settings.companyLogo ? (
              <img
                src={settings.companyLogo}
                alt="Company Logo"
                className="w-full h-full object-contain rounded-lg"
              />
            ) : (
              <Building className="h-12 w-12 text-gray-400" />
            )}
          </div>
          <div className="space-y-2">
            <label className="block">
              <input
                type="file"
                accept="image/*"
                onChange={handleLogoUpload}
                className="hidden"
              />
              <span className="inline-flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 cursor-pointer">
                <Upload className="h-4 w-4" />
                رفع شعار
              </span>
            </label>
            {settings.companyLogo && (
              <button
                onClick={() => setSettings(prev => ({ ...prev, companyLogo: '' }))}
                className="flex items-center gap-2 px-4 py-2 text-red-600 hover:text-red-800"
              >
                <X className="h-4 w-4" />
                حذف الشعار
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Basic Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
          <Building className="h-5 w-5" />
          المعلومات الأساسية
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اسم الشركة (عربي) *
            </label>
            <input
              type="text"
              value={settings.companyName}
              onChange={(e) => setSettings({...settings, companyName: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اسم الشركة (إنجليزي)
            </label>
            <input
              type="text"
              value={settings.companyNameEn}
              onChange={(e) => setSettings({...settings, companyNameEn: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
              <Mail className="h-4 w-4" />
              البريد الإلكتروني *
            </label>
            <input
              type="email"
              value={settings.companyEmail}
              onChange={(e) => setSettings({...settings, companyEmail: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
              <Phone className="h-4 w-4" />
              رقم الهاتف *
            </label>
            <input
              type="tel"
              value={settings.companyPhone}
              onChange={(e) => setSettings({...settings, companyPhone: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
              <Phone className="h-4 w-4" />
              رقم الفاكس
            </label>
            <input
              type="tel"
              value={settings.companyFax}
              onChange={(e) => setSettings({...settings, companyFax: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
              <Globe className="h-4 w-4" />
              الموقع الإلكتروني
            </label>
            <input
              type="url"
              value={settings.companyWebsite}
              onChange={(e) => setSettings({...settings, companyWebsite: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              العنوان (عربي) *
            </label>
            <textarea
              value={settings.companyAddress}
              onChange={(e) => setSettings({...settings, companyAddress: e.target.value})}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              العنوان (إنجليزي)
            </label>
            <textarea
              value={settings.companyAddressEn}
              onChange={(e) => setSettings({...settings, companyAddressEn: e.target.value})}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Legal Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          المعلومات القانونية
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الرقم الضريبي
            </label>
            <input
              type="text"
              value={settings.taxNumber}
              onChange={(e) => setSettings({...settings, taxNumber: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              السجل التجاري
            </label>
            <input
              type="text"
              value={settings.commercialRegister}
              onChange={(e) => setSettings({...settings, commercialRegister: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              تاريخ التأسيس
            </label>
            <input
              type="date"
              value={settings.establishmentDate}
              onChange={(e) => setSettings({...settings, establishmentDate: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الشكل القانوني
            </label>
            <select
              value={settings.legalForm}
              onChange={(e) => setSettings({...settings, legalForm: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">اختر الشكل القانوني</option>
              <option value="شركة ذات مسؤولية محدودة">شركة ذات مسؤولية محدودة</option>
              <option value="شركة مساهمة">شركة مساهمة</option>
              <option value="شركة تضامن">شركة تضامن</option>
              <option value="مؤسسة فردية">مؤسسة فردية</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              رأس المال
            </label>
            <input
              type="number"
              value={settings.capital}
              onChange={(e) => setSettings({...settings, capital: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              العملة الأساسية
            </label>
            <select
              value={settings.currency}
              onChange={(e) => setSettings({...settings, currency: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="SAR">ريال سعودي (SAR)</option>
              <option value="USD">دولار أمريكي (USD)</option>
              <option value="EUR">يورو (EUR)</option>
              <option value="GBP">جنيه إسترليني (GBP)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Banking Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          المعلومات البنكية
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اسم البنك
            </label>
            <input
              type="text"
              value={settings.bankName}
              onChange={(e) => setSettings({...settings, bankName: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              رقم الحساب
            </label>
            <input
              type="text"
              value={settings.bankAccount}
              onChange={(e) => setSettings({...settings, bankAccount: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              رقم الآيبان (IBAN)
            </label>
            <input
              type="text"
              value={settings.iban}
              onChange={(e) => setSettings({...settings, iban: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              رمز السويفت (SWIFT)
            </label>
            <input
              type="text"
              value={settings.swiftCode}
              onChange={(e) => setSettings({...settings, swiftCode: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Help Section */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-900 mb-2">نصائح لإعدادات الشركة:</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• تأكد من صحة جميع البيانات القانونية والضريبية</li>
          <li>• استخدم شعار عالي الجودة بصيغة PNG أو SVG</li>
          <li>• احرص على تحديث المعلومات البنكية عند تغييرها</li>
          <li>• راجع البيانات دورياً للتأكد من دقتها</li>
          <li>• احتفظ بنسخة من الوثائق القانونية في مكان آمن</li>
        </ul>
      </div>
    </div>
  )

  const renderUsersSettings = () => (
    <div className="space-y-6">
      {/* Users Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="bg-blue-500 p-3 rounded-lg">
              <Users className="h-6 w-6 text-white" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
              <p className="text-2xl font-bold text-blue-600">{users.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="bg-green-500 p-3 rounded-lg">
              <UserCheck className="h-6 w-6 text-white" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">المستخدمين النشطين</p>
              <p className="text-2xl font-bold text-green-600">
                {users.filter(u => u.isActive).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="bg-red-500 p-3 rounded-lg">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">المديرين</p>
              <p className="text-2xl font-bold text-red-600">
                {users.filter(u => u.role === 'ADMIN' || u.role === 'MANAGER').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="bg-gray-500 p-3 rounded-lg">
              <UserX className="h-6 w-6 text-white" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">غير النشطين</p>
              <p className="text-2xl font-bold text-gray-600">
                {users.filter(u => !u.isActive).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Add User Button */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">قائمة المستخدمين</h3>
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
          <UserPlus className="h-4 w-4" />
          إضافة مستخدم جديد
        </button>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المستخدم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الدور
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  آخر تسجيل دخول
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="bg-blue-100 p-2 rounded-full">
                        <User className="h-5 w-5 text-blue-600" />
                      </div>
                      <div className="mr-4">
                        <div className="text-sm font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(user.role)}`}>
                      {getRoleLabel(user.role)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {user.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {user.lastLogin
                      ? new Date(user.lastLogin).toLocaleDateString('ar-SA')
                      : 'لم يسجل دخول'
                    }
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button
                        className="text-blue-600 hover:text-blue-900"
                        title="عرض التفاصيل"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        className="text-yellow-600 hover:text-yellow-900"
                        title="تعديل"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleToggleUserStatus(user.id)}
                        className={`${user.isActive ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`}
                        title={user.isActive ? 'إلغاء التفعيل' : 'تفعيل'}
                      >
                        {user.isActive ? <UserX className="h-4 w-4" /> : <UserCheck className="h-4 w-4" />}
                      </button>
                      {user.role !== 'ADMIN' && (
                        <button
                          onClick={() => handleDeleteUser(user.id)}
                          className="text-red-600 hover:text-red-900"
                          title="حذف"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* User Management Actions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-3">إجراءات إدارة المستخدمين:</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
            <UserPlus className="h-4 w-4" />
            إضافة مستخدم جديد
          </button>
          <button
            onClick={() => window.location.href = '/settings/permissions'}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            <Users className="h-4 w-4" />
            إدارة الصلاحيات
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700">
            <Shield className="h-4 w-4" />
            تقرير المستخدمين
          </button>
        </div>
      </div>

      {/* Help Section */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-yellow-900 mb-2">نصائح لإدارة المستخدمين:</h3>
        <ul className="text-sm text-yellow-800 space-y-1">
          <li>• امنح كل مستخدم الصلاحيات المناسبة لدوره فقط</li>
          <li>• راجع قائمة المستخدمين دورياً وأزل الحسابات غير المستخدمة</li>
          <li>• تأكد من استخدام كلمات مرور قوية لجميع الحسابات</li>
          <li>• فعل المصادقة الثنائية للحسابات الإدارية</li>
          <li>• راقب أنشطة المستخدمين من خلال سجل الأنشطة</li>
        </ul>
      </div>
    </div>
  )

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">إعدادات الإشعارات</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">إشعارات البريد الإلكتروني</h4>
              <p className="text-sm text-gray-500">تلقي إشعارات عبر البريد الإلكتروني</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.emailNotifications}
                onChange={(e) => setSettings({...settings, emailNotifications: e.target.checked})}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">إشعارات الرسائل النصية</h4>
              <p className="text-sm text-gray-500">تلقي إشعارات عبر الرسائل النصية</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.smsNotifications}
                onChange={(e) => setSettings({...settings, smsNotifications: e.target.checked})}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">تذكيرات الفواتير</h4>
              <p className="text-sm text-gray-500">إرسال تذكيرات للفواتير المستحقة</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.invoiceReminders}
                onChange={(e) => setSettings({...settings, invoiceReminders: e.target.checked})}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">إشعارات الدفع</h4>
              <p className="text-sm text-gray-500">تلقي إشعارات عند استلام المدفوعات</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.paymentNotifications}
                onChange={(e) => setSettings({...settings, paymentNotifications: e.target.checked})}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  )

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return renderGeneralSettings()
      case 'company':
        return renderCompanySettings()
      case 'users':
        return renderUsersSettings()
      case 'notifications':
        return renderNotificationSettings()
      case 'security':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">إعدادات الأمان</h3>
              <div className="space-y-4">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex">
                    <Shield className="h-5 w-5 text-yellow-400" />
                    <div className="mr-3">
                      <h4 className="text-sm font-medium text-yellow-800">تنبيه أمني</h4>
                      <p className="text-sm text-yellow-700">تأكد من استخدام كلمات مرور قوية وتفعيل المصادقة الثنائية</p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      مدة انتهاء الجلسة (بالدقائق)
                    </label>
                    <input
                      type="number"
                      defaultValue="30"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      عدد محاولات تسجيل الدخول المسموحة
                    </label>
                    <input
                      type="number"
                      defaultValue="5"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">تفعيل المصادقة الثنائية</h4>
                      <p className="text-sm text-gray-500">طبقة حماية إضافية لحسابك</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">تسجيل أنشطة المستخدمين</h4>
                      <p className="text-sm text-gray-500">حفظ سجل بجميع العمليات المهمة</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" defaultChecked className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">إشعارات تسجيل الدخول</h4>
                      <p className="text-sm text-gray-500">تنبيه عند تسجيل دخول من جهاز جديد</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" defaultChecked className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      case 'appearance':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">إعدادات المظهر</h3>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    المظهر العام
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="relative">
                      <input
                        type="radio"
                        id="light"
                        name="theme"
                        value="light"
                        defaultChecked
                        className="sr-only peer"
                      />
                      <label
                        htmlFor="light"
                        className="flex flex-col items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 peer-checked:border-blue-500 peer-checked:bg-blue-50"
                      >
                        <div className="w-16 h-12 bg-white border border-gray-300 rounded mb-2"></div>
                        <span className="text-sm font-medium">فاتح</span>
                      </label>
                    </div>

                    <div className="relative">
                      <input
                        type="radio"
                        id="dark"
                        name="theme"
                        value="dark"
                        className="sr-only peer"
                      />
                      <label
                        htmlFor="dark"
                        className="flex flex-col items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 peer-checked:border-blue-500 peer-checked:bg-blue-50"
                      >
                        <div className="w-16 h-12 bg-gray-800 border border-gray-600 rounded mb-2"></div>
                        <span className="text-sm font-medium">داكن</span>
                      </label>
                    </div>

                    <div className="relative">
                      <input
                        type="radio"
                        id="auto"
                        name="theme"
                        value="auto"
                        className="sr-only peer"
                      />
                      <label
                        htmlFor="auto"
                        className="flex flex-col items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 peer-checked:border-blue-500 peer-checked:bg-blue-50"
                      >
                        <div className="w-16 h-12 bg-gradient-to-r from-white to-gray-800 border border-gray-300 rounded mb-2"></div>
                        <span className="text-sm font-medium">تلقائي</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    اللون الأساسي
                  </label>
                  <div className="grid grid-cols-6 gap-3">
                    {['blue', 'green', 'purple', 'red', 'orange', 'pink'].map((color) => (
                      <div key={color} className="relative">
                        <input
                          type="radio"
                          id={color}
                          name="primaryColor"
                          value={color}
                          defaultChecked={color === 'blue'}
                          className="sr-only peer"
                        />
                        <label
                          htmlFor={color}
                          className={`block w-12 h-12 rounded-lg cursor-pointer border-2 border-gray-200 peer-checked:border-gray-800 bg-${color}-500`}
                        ></label>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    حجم الخط
                  </label>
                  <select className="w-full md:w-1/3 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="small">صغير</option>
                    <option value="medium" selected>متوسط</option>
                    <option value="large">كبير</option>
                  </select>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">الشريط الجانبي المضغوط</h4>
                      <p className="text-sm text-gray-500">إخفاء نصوص القائمة الجانبية</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">الرسوم المتحركة</h4>
                      <p className="text-sm text-gray-500">تفعيل التأثيرات المتحركة</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" defaultChecked className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      case 'system':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">إعدادات النظام</h3>
              <div className="space-y-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex">
                    <Database className="h-5 w-5 text-blue-400" />
                    <div className="mr-3">
                      <h4 className="text-sm font-medium text-blue-800">معلومات النظام</h4>
                      <p className="text-sm text-blue-700">إصدار النظام: 1.0.0 | قاعدة البيانات: SQLite | الخادم: Next.js</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-3">النسخ الاحتياطي</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        تكرار النسخ الاحتياطي
                      </label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="daily">يومياً</option>
                        <option value="weekly" selected>أسبوعياً</option>
                        <option value="monthly">شهرياً</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        وقت النسخ الاحتياطي
                      </label>
                      <input
                        type="time"
                        defaultValue="02:00"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div className="mt-4 flex gap-3">
                    <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                      إنشاء نسخة احتياطية الآن
                    </button>
                    <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                      استعادة من نسخة احتياطية
                    </button>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-3">صيانة النظام</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">تنظيف الملفات المؤقتة</h5>
                        <p className="text-sm text-gray-500">حذف الملفات المؤقتة وتحسين الأداء</p>
                      </div>
                      <button className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                        تنظيف
                      </button>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">تحسين قاعدة البيانات</h5>
                        <p className="text-sm text-gray-500">إعادة فهرسة وتحسين قاعدة البيانات</p>
                      </div>
                      <button className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                        تحسين
                      </button>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">مسح سجل الأنشطة</h5>
                        <p className="text-sm text-gray-500">حذف سجلات الأنشطة القديمة</p>
                      </div>
                      <button className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200">
                        مسح
                      </button>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-3">إعدادات متقدمة</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">وضع التطوير</h5>
                        <p className="text-sm text-gray-500">تفعيل أدوات التطوير والتشخيص</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" className="sr-only peer" />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">تسجيل مفصل</h5>
                        <p className="text-sm text-gray-500">حفظ سجلات مفصلة لجميع العمليات</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" defaultChecked className="sr-only peer" />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">التحديثات التلقائية</h5>
                        <p className="text-sm text-gray-500">تحديث النظام تلقائياً عند توفر إصدار جديد</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" defaultChecked className="sr-only peer" />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-red-800 mb-2">منطقة الخطر</h4>
                  <p className="text-sm text-red-700 mb-3">العمليات التالية لا يمكن التراجع عنها</p>
                  <div className="flex gap-3">
                    <button className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
                      إعادة تعيين النظام
                    </button>
                    <button className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
                      حذف جميع البيانات
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      default:
        return renderGeneralSettings()
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">الإعدادات</h1>
          <p className="mt-2 text-gray-600">إدارة إعدادات النظام والشركة</p>
        </div>

        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
                >
                  <tab.icon className="h-4 w-4" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
          
          <div className="p-6">
            {renderTabContent()}
          </div>
          
          <div className="border-t border-gray-200 px-6 py-4 flex justify-between">
            <button
              onClick={handleReset}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              <RefreshCw className="h-4 w-4" />
              إعادة تعيين
            </button>
            
            <button
              onClick={handleSave}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Save className="h-4 w-4" />
              حفظ التغييرات
            </button>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
