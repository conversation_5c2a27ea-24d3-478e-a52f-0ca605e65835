/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/hr/employees/route";
exports.ids = ["app/api/hr/employees/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhr%2Femployees%2Froute&page=%2Fapi%2Fhr%2Femployees%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhr%2Femployees%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhr%2Femployees%2Froute&page=%2Fapi%2Fhr%2Femployees%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhr%2Femployees%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Accounting_accounting_system_src_app_api_hr_employees_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/hr/employees/route.ts */ \"(rsc)/./src/app/api/hr/employees/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/hr/employees/route\",\n        pathname: \"/api/hr/employees\",\n        filename: \"route\",\n        bundlePath: \"app/api/hr/employees/route\"\n    },\n    resolvedPagePath: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\api\\\\hr\\\\employees\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Accounting_accounting_system_src_app_api_hr_employees_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhr%2Femployees%2Froute&page=%2Fapi%2Fhr%2Femployees%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhr%2Femployees%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/hr/employees/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/hr/employees/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const employees = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.employee.findMany({\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(employees);\n    } catch (error) {\n        console.error('Error fetching employees:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const formData = await request.formData();\n        // Extract form data\n        const employeeId = formData.get('employeeId');\n        const firstName = formData.get('firstName');\n        const lastName = formData.get('lastName');\n        const email = formData.get('email');\n        const phone = formData.get('phone');\n        const address = formData.get('address');\n        const dateOfBirth = formData.get('dateOfBirth');\n        const hireDate = formData.get('hireDate');\n        const position = formData.get('position');\n        const department = formData.get('department');\n        const salary = formData.get('salary');\n        const nationalId = formData.get('nationalId');\n        const emergencyContact = formData.get('emergencyContact');\n        const emergencyPhone = formData.get('emergencyPhone');\n        const bankAccount = formData.get('bankAccount');\n        const notes = formData.get('notes');\n        const profileImage = formData.get('profileImage');\n        // Validate required fields\n        if (!firstName || !lastName || !email || !position || !department || !salary || !hireDate) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // Validate salary\n        if (parseFloat(salary) <= 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Salary must be greater than zero'\n            }, {\n                status: 400\n            });\n        }\n        let profileImagePath = null;\n        // Handle image upload\n        if (profileImage && profileImage.size > 0) {\n            try {\n                const bytes = await profileImage.arrayBuffer();\n                const buffer = Buffer.from(bytes);\n                // Create uploads directory if it doesn't exist\n                const uploadsDir = (0,path__WEBPACK_IMPORTED_MODULE_5__.join)(process.cwd(), 'public', 'uploads', 'employees');\n                await (0,fs_promises__WEBPACK_IMPORTED_MODULE_4__.mkdir)(uploadsDir, {\n                    recursive: true\n                });\n                // Generate unique filename\n                const timestamp = Date.now();\n                const extension = profileImage.name.split('.').pop();\n                const filename = `${employeeId || timestamp}.${extension}`;\n                const filepath = (0,path__WEBPACK_IMPORTED_MODULE_5__.join)(uploadsDir, filename);\n                // Write file\n                await (0,fs_promises__WEBPACK_IMPORTED_MODULE_4__.writeFile)(filepath, buffer);\n                profileImagePath = `/uploads/employees/${filename}`;\n            } catch (error) {\n                console.error('Error uploading image:', error);\n            // Continue without image if upload fails\n            }\n        }\n        // Generate employee ID if not provided\n        const finalEmployeeId = employeeId || `EMP${Date.now().toString().slice(-6)}`;\n        const employee = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.employee.create({\n            data: {\n                employeeId: finalEmployeeId,\n                firstName,\n                lastName,\n                email,\n                phone: phone || null,\n                address: address || null,\n                dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,\n                hireDate: new Date(hireDate),\n                position,\n                department,\n                salary: parseFloat(salary),\n                nationalId: nationalId || null,\n                emergencyContact: emergencyContact || null,\n                emergencyPhone: emergencyPhone || null,\n                bankAccount: bankAccount || null,\n                notes: notes || null,\n                profileImage: profileImagePath,\n                status: 'ACTIVE'\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(employee, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating employee:', error);\n        // Handle unique constraint violations\n        if (error instanceof Error && error.message.includes('Unique constraint')) {\n            if (error.message.includes('email')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Email already exists'\n                }, {\n                    status: 400\n                });\n            }\n            if (error.message.includes('employeeId')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Employee ID already exists'\n                }, {\n                    status: 400\n                });\n            }\n            if (error.message.includes('nationalId')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'National ID already exists'\n                }, {\n                    status: 400\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9oci9lbXBsb3llZXMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBdUQ7QUFDWDtBQUNKO0FBQ0g7QUFDUztBQUNuQjtBQUVwQixlQUFlTyxJQUFJQyxPQUFvQjtJQUM1QyxJQUFJO1FBQ0YsTUFBTUMsVUFBVSxNQUFNUiwyREFBZ0JBLENBQUNDLGtEQUFXQTtRQUVsRCxJQUFJLENBQUNPLFNBQVM7WUFDWixPQUFPVCxxREFBWUEsQ0FBQ1UsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQWUsR0FBRztnQkFBRUMsUUFBUTtZQUFJO1FBQ3BFO1FBRUEsTUFBTUMsWUFBWSxNQUFNViwrQ0FBTUEsQ0FBQ1csUUFBUSxDQUFDQyxRQUFRLENBQUM7WUFDL0NDLFNBQVM7Z0JBQ1BDLFdBQVc7WUFDYjtRQUNGO1FBRUEsT0FBT2pCLHFEQUFZQSxDQUFDVSxJQUFJLENBQUNHO0lBQzNCLEVBQUUsT0FBT0YsT0FBTztRQUNkTyxRQUFRUCxLQUFLLENBQUMsNkJBQTZCQTtRQUMzQyxPQUFPWCxxREFBWUEsQ0FBQ1UsSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQXdCLEdBQ2pDO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRU8sZUFBZU8sS0FBS1gsT0FBb0I7SUFDN0MsSUFBSTtRQUNGLE1BQU1DLFVBQVUsTUFBTVIsMkRBQWdCQSxDQUFDQyxrREFBV0E7UUFFbEQsSUFBSSxDQUFDTyxTQUFTO1lBQ1osT0FBT1QscURBQVlBLENBQUNVLElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUFlLEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUNwRTtRQUVBLE1BQU1RLFdBQVcsTUFBTVosUUFBUVksUUFBUTtRQUV2QyxvQkFBb0I7UUFDcEIsTUFBTUMsYUFBYUQsU0FBU0UsR0FBRyxDQUFDO1FBQ2hDLE1BQU1DLFlBQVlILFNBQVNFLEdBQUcsQ0FBQztRQUMvQixNQUFNRSxXQUFXSixTQUFTRSxHQUFHLENBQUM7UUFDOUIsTUFBTUcsUUFBUUwsU0FBU0UsR0FBRyxDQUFDO1FBQzNCLE1BQU1JLFFBQVFOLFNBQVNFLEdBQUcsQ0FBQztRQUMzQixNQUFNSyxVQUFVUCxTQUFTRSxHQUFHLENBQUM7UUFDN0IsTUFBTU0sY0FBY1IsU0FBU0UsR0FBRyxDQUFDO1FBQ2pDLE1BQU1PLFdBQVdULFNBQVNFLEdBQUcsQ0FBQztRQUM5QixNQUFNUSxXQUFXVixTQUFTRSxHQUFHLENBQUM7UUFDOUIsTUFBTVMsYUFBYVgsU0FBU0UsR0FBRyxDQUFDO1FBQ2hDLE1BQU1VLFNBQVNaLFNBQVNFLEdBQUcsQ0FBQztRQUM1QixNQUFNVyxhQUFhYixTQUFTRSxHQUFHLENBQUM7UUFDaEMsTUFBTVksbUJBQW1CZCxTQUFTRSxHQUFHLENBQUM7UUFDdEMsTUFBTWEsaUJBQWlCZixTQUFTRSxHQUFHLENBQUM7UUFDcEMsTUFBTWMsY0FBY2hCLFNBQVNFLEdBQUcsQ0FBQztRQUNqQyxNQUFNZSxRQUFRakIsU0FBU0UsR0FBRyxDQUFDO1FBQzNCLE1BQU1nQixlQUFlbEIsU0FBU0UsR0FBRyxDQUFDO1FBRWxDLDJCQUEyQjtRQUMzQixJQUFJLENBQUNDLGFBQWEsQ0FBQ0MsWUFBWSxDQUFDQyxTQUFTLENBQUNLLFlBQVksQ0FBQ0MsY0FBYyxDQUFDQyxVQUFVLENBQUNILFVBQVU7WUFDekYsT0FBTzdCLHFEQUFZQSxDQUFDVSxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQTBCLEdBQ25DO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxrQkFBa0I7UUFDbEIsSUFBSTJCLFdBQVdQLFdBQVcsR0FBRztZQUMzQixPQUFPaEMscURBQVlBLENBQUNVLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBbUMsR0FDNUM7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLElBQUk0QixtQkFBbUI7UUFFdkIsc0JBQXNCO1FBQ3RCLElBQUlGLGdCQUFnQkEsYUFBYUcsSUFBSSxHQUFHLEdBQUc7WUFDekMsSUFBSTtnQkFDRixNQUFNQyxRQUFRLE1BQU1KLGFBQWFLLFdBQVc7Z0JBQzVDLE1BQU1DLFNBQVNDLE9BQU9DLElBQUksQ0FBQ0o7Z0JBRTNCLCtDQUErQztnQkFDL0MsTUFBTUssYUFBYXpDLDBDQUFJQSxDQUFDMEMsUUFBUUMsR0FBRyxJQUFJLFVBQVUsV0FBVztnQkFDNUQsTUFBTTVDLGtEQUFLQSxDQUFDMEMsWUFBWTtvQkFBRUcsV0FBVztnQkFBSztnQkFFMUMsMkJBQTJCO2dCQUMzQixNQUFNQyxZQUFZQyxLQUFLQyxHQUFHO2dCQUMxQixNQUFNQyxZQUFZaEIsYUFBYWlCLElBQUksQ0FBQ0MsS0FBSyxDQUFDLEtBQUtDLEdBQUc7Z0JBQ2xELE1BQU1DLFdBQVcsR0FBR3JDLGNBQWM4QixVQUFVLENBQUMsRUFBRUcsV0FBVztnQkFDMUQsTUFBTUssV0FBV3JELDBDQUFJQSxDQUFDeUMsWUFBWVc7Z0JBRWxDLGFBQWE7Z0JBQ2IsTUFBTXRELHNEQUFTQSxDQUFDdUQsVUFBVWY7Z0JBQzFCSixtQkFBbUIsQ0FBQyxtQkFBbUIsRUFBRWtCLFVBQVU7WUFDckQsRUFBRSxPQUFPL0MsT0FBTztnQkFDZE8sUUFBUVAsS0FBSyxDQUFDLDBCQUEwQkE7WUFDeEMseUNBQXlDO1lBQzNDO1FBQ0Y7UUFFQSx1Q0FBdUM7UUFDdkMsTUFBTWlELGtCQUFrQnZDLGNBQWMsQ0FBQyxHQUFHLEVBQUUrQixLQUFLQyxHQUFHLEdBQUdRLFFBQVEsR0FBR0MsS0FBSyxDQUFDLENBQUMsSUFBSTtRQUU3RSxNQUFNaEQsV0FBVyxNQUFNWCwrQ0FBTUEsQ0FBQ1csUUFBUSxDQUFDaUQsTUFBTSxDQUFDO1lBQzVDQyxNQUFNO2dCQUNKM0MsWUFBWXVDO2dCQUNackM7Z0JBQ0FDO2dCQUNBQztnQkFDQUMsT0FBT0EsU0FBUztnQkFDaEJDLFNBQVNBLFdBQVc7Z0JBQ3BCQyxhQUFhQSxjQUFjLElBQUl3QixLQUFLeEIsZUFBZTtnQkFDbkRDLFVBQVUsSUFBSXVCLEtBQUt2QjtnQkFDbkJDO2dCQUNBQztnQkFDQUMsUUFBUU8sV0FBV1A7Z0JBQ25CQyxZQUFZQSxjQUFjO2dCQUMxQkMsa0JBQWtCQSxvQkFBb0I7Z0JBQ3RDQyxnQkFBZ0JBLGtCQUFrQjtnQkFDbENDLGFBQWFBLGVBQWU7Z0JBQzVCQyxPQUFPQSxTQUFTO2dCQUNoQkMsY0FBY0U7Z0JBQ2Q1QixRQUFRO1lBQ1Y7UUFDRjtRQUVBLE9BQU9aLHFEQUFZQSxDQUFDVSxJQUFJLENBQUNJLFVBQVU7WUFBRUYsUUFBUTtRQUFJO0lBQ25ELEVBQUUsT0FBT0QsT0FBTztRQUNkTyxRQUFRUCxLQUFLLENBQUMsNEJBQTRCQTtRQUUxQyxzQ0FBc0M7UUFDdEMsSUFBSUEsaUJBQWlCc0QsU0FBU3RELE1BQU11RCxPQUFPLENBQUNDLFFBQVEsQ0FBQyxzQkFBc0I7WUFDekUsSUFBSXhELE1BQU11RCxPQUFPLENBQUNDLFFBQVEsQ0FBQyxVQUFVO2dCQUNuQyxPQUFPbkUscURBQVlBLENBQUNVLElBQUksQ0FDdEI7b0JBQUVDLE9BQU87Z0JBQXVCLEdBQ2hDO29CQUFFQyxRQUFRO2dCQUFJO1lBRWxCO1lBQ0EsSUFBSUQsTUFBTXVELE9BQU8sQ0FBQ0MsUUFBUSxDQUFDLGVBQWU7Z0JBQ3hDLE9BQU9uRSxxREFBWUEsQ0FBQ1UsSUFBSSxDQUN0QjtvQkFBRUMsT0FBTztnQkFBNkIsR0FDdEM7b0JBQUVDLFFBQVE7Z0JBQUk7WUFFbEI7WUFDQSxJQUFJRCxNQUFNdUQsT0FBTyxDQUFDQyxRQUFRLENBQUMsZUFBZTtnQkFDeEMsT0FBT25FLHFEQUFZQSxDQUFDVSxJQUFJLENBQ3RCO29CQUFFQyxPQUFPO2dCQUE2QixHQUN0QztvQkFBRUMsUUFBUTtnQkFBSTtZQUVsQjtRQUNGO1FBRUEsT0FBT1oscURBQVlBLENBQUNVLElBQUksQ0FDdEI7WUFBRUMsT0FBTztRQUF3QixHQUNqQztZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRiIsInNvdXJjZXMiOlsiRDpcXEFjY291bnRpbmdcXGFjY291bnRpbmctc3lzdGVtXFxzcmNcXGFwcFxcYXBpXFxoclxcZW1wbG95ZWVzXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInXG5pbXBvcnQgeyBnZXRTZXJ2ZXJTZXNzaW9uIH0gZnJvbSAnbmV4dC1hdXRoJ1xuaW1wb3J0IHsgYXV0aE9wdGlvbnMgfSBmcm9tICdAL2xpYi9hdXRoJ1xuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSAnQC9saWIvcHJpc21hJ1xuaW1wb3J0IHsgd3JpdGVGaWxlLCBta2RpciB9IGZyb20gJ2ZzL3Byb21pc2VzJ1xuaW1wb3J0IHsgam9pbiB9IGZyb20gJ3BhdGgnXG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzZXNzaW9uID0gYXdhaXQgZ2V0U2VydmVyU2Vzc2lvbihhdXRoT3B0aW9ucylcbiAgICBcbiAgICBpZiAoIXNlc3Npb24pIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnVW5hdXRob3JpemVkJyB9LCB7IHN0YXR1czogNDAxIH0pXG4gICAgfVxuXG4gICAgY29uc3QgZW1wbG95ZWVzID0gYXdhaXQgcHJpc21hLmVtcGxveWVlLmZpbmRNYW55KHtcbiAgICAgIG9yZGVyQnk6IHtcbiAgICAgICAgY3JlYXRlZEF0OiAnZGVzYydcbiAgICAgIH1cbiAgICB9KVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKGVtcGxveWVlcylcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBlbXBsb3llZXM6JywgZXJyb3IpXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ0ludGVybmFsIHNlcnZlciBlcnJvcicgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgIClcbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCBnZXRTZXJ2ZXJTZXNzaW9uKGF1dGhPcHRpb25zKVxuICAgIFxuICAgIGlmICghc2Vzc2lvbikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH0sIHsgc3RhdHVzOiA0MDEgfSlcbiAgICB9XG5cbiAgICBjb25zdCBmb3JtRGF0YSA9IGF3YWl0IHJlcXVlc3QuZm9ybURhdGEoKVxuICAgIFxuICAgIC8vIEV4dHJhY3QgZm9ybSBkYXRhXG4gICAgY29uc3QgZW1wbG95ZWVJZCA9IGZvcm1EYXRhLmdldCgnZW1wbG95ZWVJZCcpIGFzIHN0cmluZ1xuICAgIGNvbnN0IGZpcnN0TmFtZSA9IGZvcm1EYXRhLmdldCgnZmlyc3ROYW1lJykgYXMgc3RyaW5nXG4gICAgY29uc3QgbGFzdE5hbWUgPSBmb3JtRGF0YS5nZXQoJ2xhc3ROYW1lJykgYXMgc3RyaW5nXG4gICAgY29uc3QgZW1haWwgPSBmb3JtRGF0YS5nZXQoJ2VtYWlsJykgYXMgc3RyaW5nXG4gICAgY29uc3QgcGhvbmUgPSBmb3JtRGF0YS5nZXQoJ3Bob25lJykgYXMgc3RyaW5nXG4gICAgY29uc3QgYWRkcmVzcyA9IGZvcm1EYXRhLmdldCgnYWRkcmVzcycpIGFzIHN0cmluZ1xuICAgIGNvbnN0IGRhdGVPZkJpcnRoID0gZm9ybURhdGEuZ2V0KCdkYXRlT2ZCaXJ0aCcpIGFzIHN0cmluZ1xuICAgIGNvbnN0IGhpcmVEYXRlID0gZm9ybURhdGEuZ2V0KCdoaXJlRGF0ZScpIGFzIHN0cmluZ1xuICAgIGNvbnN0IHBvc2l0aW9uID0gZm9ybURhdGEuZ2V0KCdwb3NpdGlvbicpIGFzIHN0cmluZ1xuICAgIGNvbnN0IGRlcGFydG1lbnQgPSBmb3JtRGF0YS5nZXQoJ2RlcGFydG1lbnQnKSBhcyBzdHJpbmdcbiAgICBjb25zdCBzYWxhcnkgPSBmb3JtRGF0YS5nZXQoJ3NhbGFyeScpIGFzIHN0cmluZ1xuICAgIGNvbnN0IG5hdGlvbmFsSWQgPSBmb3JtRGF0YS5nZXQoJ25hdGlvbmFsSWQnKSBhcyBzdHJpbmdcbiAgICBjb25zdCBlbWVyZ2VuY3lDb250YWN0ID0gZm9ybURhdGEuZ2V0KCdlbWVyZ2VuY3lDb250YWN0JykgYXMgc3RyaW5nXG4gICAgY29uc3QgZW1lcmdlbmN5UGhvbmUgPSBmb3JtRGF0YS5nZXQoJ2VtZXJnZW5jeVBob25lJykgYXMgc3RyaW5nXG4gICAgY29uc3QgYmFua0FjY291bnQgPSBmb3JtRGF0YS5nZXQoJ2JhbmtBY2NvdW50JykgYXMgc3RyaW5nXG4gICAgY29uc3Qgbm90ZXMgPSBmb3JtRGF0YS5nZXQoJ25vdGVzJykgYXMgc3RyaW5nXG4gICAgY29uc3QgcHJvZmlsZUltYWdlID0gZm9ybURhdGEuZ2V0KCdwcm9maWxlSW1hZ2UnKSBhcyBGaWxlXG5cbiAgICAvLyBWYWxpZGF0ZSByZXF1aXJlZCBmaWVsZHNcbiAgICBpZiAoIWZpcnN0TmFtZSB8fCAhbGFzdE5hbWUgfHwgIWVtYWlsIHx8ICFwb3NpdGlvbiB8fCAhZGVwYXJ0bWVudCB8fCAhc2FsYXJ5IHx8ICFoaXJlRGF0ZSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnTWlzc2luZyByZXF1aXJlZCBmaWVsZHMnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKVxuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIHNhbGFyeVxuICAgIGlmIChwYXJzZUZsb2F0KHNhbGFyeSkgPD0gMCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnU2FsYXJ5IG11c3QgYmUgZ3JlYXRlciB0aGFuIHplcm8nIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKVxuICAgIH1cblxuICAgIGxldCBwcm9maWxlSW1hZ2VQYXRoID0gbnVsbFxuXG4gICAgLy8gSGFuZGxlIGltYWdlIHVwbG9hZFxuICAgIGlmIChwcm9maWxlSW1hZ2UgJiYgcHJvZmlsZUltYWdlLnNpemUgPiAwKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBieXRlcyA9IGF3YWl0IHByb2ZpbGVJbWFnZS5hcnJheUJ1ZmZlcigpXG4gICAgICAgIGNvbnN0IGJ1ZmZlciA9IEJ1ZmZlci5mcm9tKGJ5dGVzKVxuXG4gICAgICAgIC8vIENyZWF0ZSB1cGxvYWRzIGRpcmVjdG9yeSBpZiBpdCBkb2Vzbid0IGV4aXN0XG4gICAgICAgIGNvbnN0IHVwbG9hZHNEaXIgPSBqb2luKHByb2Nlc3MuY3dkKCksICdwdWJsaWMnLCAndXBsb2FkcycsICdlbXBsb3llZXMnKVxuICAgICAgICBhd2FpdCBta2Rpcih1cGxvYWRzRGlyLCB7IHJlY3Vyc2l2ZTogdHJ1ZSB9KVxuXG4gICAgICAgIC8vIEdlbmVyYXRlIHVuaXF1ZSBmaWxlbmFtZVxuICAgICAgICBjb25zdCB0aW1lc3RhbXAgPSBEYXRlLm5vdygpXG4gICAgICAgIGNvbnN0IGV4dGVuc2lvbiA9IHByb2ZpbGVJbWFnZS5uYW1lLnNwbGl0KCcuJykucG9wKClcbiAgICAgICAgY29uc3QgZmlsZW5hbWUgPSBgJHtlbXBsb3llZUlkIHx8IHRpbWVzdGFtcH0uJHtleHRlbnNpb259YFxuICAgICAgICBjb25zdCBmaWxlcGF0aCA9IGpvaW4odXBsb2Fkc0RpciwgZmlsZW5hbWUpXG5cbiAgICAgICAgLy8gV3JpdGUgZmlsZVxuICAgICAgICBhd2FpdCB3cml0ZUZpbGUoZmlsZXBhdGgsIGJ1ZmZlcilcbiAgICAgICAgcHJvZmlsZUltYWdlUGF0aCA9IGAvdXBsb2Fkcy9lbXBsb3llZXMvJHtmaWxlbmFtZX1gXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGxvYWRpbmcgaW1hZ2U6JywgZXJyb3IpXG4gICAgICAgIC8vIENvbnRpbnVlIHdpdGhvdXQgaW1hZ2UgaWYgdXBsb2FkIGZhaWxzXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gR2VuZXJhdGUgZW1wbG95ZWUgSUQgaWYgbm90IHByb3ZpZGVkXG4gICAgY29uc3QgZmluYWxFbXBsb3llZUlkID0gZW1wbG95ZWVJZCB8fCBgRU1QJHtEYXRlLm5vdygpLnRvU3RyaW5nKCkuc2xpY2UoLTYpfWBcblxuICAgIGNvbnN0IGVtcGxveWVlID0gYXdhaXQgcHJpc21hLmVtcGxveWVlLmNyZWF0ZSh7XG4gICAgICBkYXRhOiB7XG4gICAgICAgIGVtcGxveWVlSWQ6IGZpbmFsRW1wbG95ZWVJZCxcbiAgICAgICAgZmlyc3ROYW1lLFxuICAgICAgICBsYXN0TmFtZSxcbiAgICAgICAgZW1haWwsXG4gICAgICAgIHBob25lOiBwaG9uZSB8fCBudWxsLFxuICAgICAgICBhZGRyZXNzOiBhZGRyZXNzIHx8IG51bGwsXG4gICAgICAgIGRhdGVPZkJpcnRoOiBkYXRlT2ZCaXJ0aCA/IG5ldyBEYXRlKGRhdGVPZkJpcnRoKSA6IG51bGwsXG4gICAgICAgIGhpcmVEYXRlOiBuZXcgRGF0ZShoaXJlRGF0ZSksXG4gICAgICAgIHBvc2l0aW9uLFxuICAgICAgICBkZXBhcnRtZW50LFxuICAgICAgICBzYWxhcnk6IHBhcnNlRmxvYXQoc2FsYXJ5KSxcbiAgICAgICAgbmF0aW9uYWxJZDogbmF0aW9uYWxJZCB8fCBudWxsLFxuICAgICAgICBlbWVyZ2VuY3lDb250YWN0OiBlbWVyZ2VuY3lDb250YWN0IHx8IG51bGwsXG4gICAgICAgIGVtZXJnZW5jeVBob25lOiBlbWVyZ2VuY3lQaG9uZSB8fCBudWxsLFxuICAgICAgICBiYW5rQWNjb3VudDogYmFua0FjY291bnQgfHwgbnVsbCxcbiAgICAgICAgbm90ZXM6IG5vdGVzIHx8IG51bGwsXG4gICAgICAgIHByb2ZpbGVJbWFnZTogcHJvZmlsZUltYWdlUGF0aCxcbiAgICAgICAgc3RhdHVzOiAnQUNUSVZFJ1xuICAgICAgfVxuICAgIH0pXG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oZW1wbG95ZWUsIHsgc3RhdHVzOiAyMDEgfSlcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyBlbXBsb3llZTonLCBlcnJvcilcbiAgICBcbiAgICAvLyBIYW5kbGUgdW5pcXVlIGNvbnN0cmFpbnQgdmlvbGF0aW9uc1xuICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yICYmIGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ1VuaXF1ZSBjb25zdHJhaW50JykpIHtcbiAgICAgIGlmIChlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdlbWFpbCcpKSB7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICB7IGVycm9yOiAnRW1haWwgYWxyZWFkeSBleGlzdHMnIH0sXG4gICAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICAgIClcbiAgICAgIH1cbiAgICAgIGlmIChlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdlbXBsb3llZUlkJykpIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgIHsgZXJyb3I6ICdFbXBsb3llZSBJRCBhbHJlYWR5IGV4aXN0cycgfSxcbiAgICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICAgKVxuICAgICAgfVxuICAgICAgaWYgKGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ25hdGlvbmFsSWQnKSkge1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAgeyBlcnJvcjogJ05hdGlvbmFsIElEIGFscmVhZHkgZXhpc3RzJyB9LFxuICAgICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgICApXG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJnZXRTZXJ2ZXJTZXNzaW9uIiwiYXV0aE9wdGlvbnMiLCJwcmlzbWEiLCJ3cml0ZUZpbGUiLCJta2RpciIsImpvaW4iLCJHRVQiLCJyZXF1ZXN0Iiwic2Vzc2lvbiIsImpzb24iLCJlcnJvciIsInN0YXR1cyIsImVtcGxveWVlcyIsImVtcGxveWVlIiwiZmluZE1hbnkiLCJvcmRlckJ5IiwiY3JlYXRlZEF0IiwiY29uc29sZSIsIlBPU1QiLCJmb3JtRGF0YSIsImVtcGxveWVlSWQiLCJnZXQiLCJmaXJzdE5hbWUiLCJsYXN0TmFtZSIsImVtYWlsIiwicGhvbmUiLCJhZGRyZXNzIiwiZGF0ZU9mQmlydGgiLCJoaXJlRGF0ZSIsInBvc2l0aW9uIiwiZGVwYXJ0bWVudCIsInNhbGFyeSIsIm5hdGlvbmFsSWQiLCJlbWVyZ2VuY3lDb250YWN0IiwiZW1lcmdlbmN5UGhvbmUiLCJiYW5rQWNjb3VudCIsIm5vdGVzIiwicHJvZmlsZUltYWdlIiwicGFyc2VGbG9hdCIsInByb2ZpbGVJbWFnZVBhdGgiLCJzaXplIiwiYnl0ZXMiLCJhcnJheUJ1ZmZlciIsImJ1ZmZlciIsIkJ1ZmZlciIsImZyb20iLCJ1cGxvYWRzRGlyIiwicHJvY2VzcyIsImN3ZCIsInJlY3Vyc2l2ZSIsInRpbWVzdGFtcCIsIkRhdGUiLCJub3ciLCJleHRlbnNpb24iLCJuYW1lIiwic3BsaXQiLCJwb3AiLCJmaWxlbmFtZSIsImZpbGVwYXRoIiwiZmluYWxFbXBsb3llZUlkIiwidG9TdHJpbmciLCJzbGljZSIsImNyZWF0ZSIsImRhdGEiLCJFcnJvciIsIm1lc3NhZ2UiLCJpbmNsdWRlcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/hr/employees/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user) {\n                    return null;\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"].compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        signUp: '/auth/signup'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkQ6XFxBY2NvdW50aW5nXFxhY2NvdW50aW5nLXN5c3RlbVxcc3JjXFxsaWJcXHByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/@next-auth","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhr%2Femployees%2Froute&page=%2Fapi%2Fhr%2Femployees%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhr%2Femployees%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();