'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import MainLayout from '@/components/Layout/MainLayout'
import { Plus, Search, Edit, Trash2, Eye, Calendar, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react'

interface Leave {
  id: string
  employeeId: string
  employeeName: string
  type: string
  startDate: string
  endDate: string
  days: number
  reason?: string
  status: string
  approvedBy?: string
  approvedAt?: string
  createdAt: string
}

export default function LeavesPage() {
  const [leaves, setLeaves] = useState<Leave[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('ALL')
  const [typeFilter, setTypeFilter] = useState<string>('ALL')

  useEffect(() => {
    const fetchLeaves = async () => {
      try {
        const response = await fetch('/api/hr/leaves')
        if (response.ok) {
          const data = await response.json()
          setLeaves(data)
        } else {
          // Fallback to demo data if API fails
          setLeaves([
            {
              id: '1',
              employeeId: 'EMP001',
              employeeName: 'أحمد محمد',
              type: 'ANNUAL',
              startDate: '2024-03-15',
              endDate: '2024-03-20',
              days: 6,
              reason: 'إجازة سنوية',
              status: 'APPROVED',
              approvedBy: 'مدير الموارد البشرية',
              approvedAt: '2024-03-10',
              createdAt: '2024-03-08'
            },
            {
              id: '2',
              employeeId: 'EMP002',
              employeeName: 'فاطمة أحمد',
              type: 'SICK',
              startDate: '2024-03-12',
              endDate: '2024-03-14',
              days: 3,
              reason: 'إجازة مرضية',
              status: 'PENDING',
              createdAt: '2024-03-11'
            },
            {
              id: '3',
              employeeId: 'EMP003',
              employeeName: 'خالد علي',
              type: 'EMERGENCY',
              startDate: '2024-03-18',
              endDate: '2024-03-18',
              days: 1,
              reason: 'ظروف طارئة',
              status: 'REJECTED',
              createdAt: '2024-03-17'
            }
          ])
        }
      } catch (error) {
        console.error('Error fetching leaves:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchLeaves()
  }, [])

  const leaveTypes = [...new Set(leaves.map(leave => leave.type))]

  const filteredLeaves = leaves.filter(leave => {
    const matchesSearch = 
      leave.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      leave.employeeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (leave.reason && leave.reason.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesStatus = statusFilter === 'ALL' || leave.status === statusFilter
    const matchesType = typeFilter === 'ALL' || leave.type === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })

  const handleApproveLeave = async (id: string) => {
    try {
      const response = await fetch(`/api/hr/leaves/${id}/approve`, {
        method: 'POST',
      })

      if (response.ok) {
        setLeaves(leaves.map(leave => 
          leave.id === id 
            ? { ...leave, status: 'APPROVED', approvedAt: new Date().toISOString() }
            : leave
        ))
        alert('تم الموافقة على الإجازة!')
      } else {
        alert('حدث خطأ أثناء الموافقة على الإجازة')
      }
    } catch (error) {
      console.error('Error approving leave:', error)
      alert('حدث خطأ أثناء الموافقة على الإجازة')
    }
  }

  const handleRejectLeave = async (id: string) => {
    if (confirm('هل أنت متأكد من رفض هذه الإجازة؟')) {
      try {
        const response = await fetch(`/api/hr/leaves/${id}/reject`, {
          method: 'POST',
        })

        if (response.ok) {
          setLeaves(leaves.map(leave => 
            leave.id === id 
              ? { ...leave, status: 'REJECTED' }
              : leave
          ))
          alert('تم رفض الإجازة!')
        } else {
          alert('حدث خطأ أثناء رفض الإجازة')
        }
      } catch (error) {
        console.error('Error rejecting leave:', error)
        alert('حدث خطأ أثناء رفض الإجازة')
      }
    }
  }

  const handleDeleteLeave = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذه الإجازة؟')) {
      try {
        const response = await fetch(`/api/hr/leaves/${id}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          setLeaves(leaves.filter(leave => leave.id !== id))
          alert('تم حذف الإجازة بنجاح!')
        } else {
          alert('حدث خطأ أثناء حذف الإجازة')
        }
      } catch (error) {
        console.error('Error deleting leave:', error)
        alert('حدث خطأ أثناء حذف الإجازة')
      }
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800'
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800'
      case 'REJECTED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'موافق عليها'
      case 'PENDING':
        return 'في الانتظار'
      case 'REJECTED':
        return 'مرفوضة'
      default:
        return status
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'PENDING':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />
      case 'REJECTED':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return null
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'ANNUAL':
        return 'إجازة سنوية'
      case 'SICK':
        return 'إجازة مرضية'
      case 'EMERGENCY':
        return 'إجازة طارئة'
      case 'MATERNITY':
        return 'إجازة أمومة'
      case 'PATERNITY':
        return 'إجازة أبوة'
      case 'UNPAID':
        return 'إجازة بدون راتب'
      default:
        return type
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'ANNUAL':
        return 'bg-blue-100 text-blue-800'
      case 'SICK':
        return 'bg-red-100 text-red-800'
      case 'EMERGENCY':
        return 'bg-orange-100 text-orange-800'
      case 'MATERNITY':
        return 'bg-pink-100 text-pink-800'
      case 'PATERNITY':
        return 'bg-purple-100 text-purple-800'
      case 'UNPAID':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة الإجازات</h1>
            <p className="mt-2 text-gray-600">إدارة طلبات الإجازات والموافقة عليها</p>
          </div>
          <Link
            href="/hr/leaves/new"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
          >
            <Plus className="h-5 w-5" />
            طلب إجازة جديدة
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-500 p-3 rounded-lg">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                <p className="text-2xl font-bold text-blue-600">{leaves.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-yellow-500 p-3 rounded-lg">
                <Clock className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">في الانتظار</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {leaves.filter(leave => leave.status === 'PENDING').length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-500 p-3 rounded-lg">
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">موافق عليها</p>
                <p className="text-2xl font-bold text-green-600">
                  {leaves.filter(leave => leave.status === 'APPROVED').length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-red-500 p-3 rounded-lg">
                <XCircle className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">مرفوضة</p>
                <p className="text-2xl font-bold text-red-600">
                  {leaves.filter(leave => leave.status === 'REJECTED').length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="البحث في الإجازات..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="ALL">جميع الحالات</option>
              <option value="PENDING">في الانتظار</option>
              <option value="APPROVED">موافق عليها</option>
              <option value="REJECTED">مرفوضة</option>
            </select>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
            >
              <option value="ALL">جميع الأنواع</option>
              <option value="ANNUAL">إجازة سنوية</option>
              <option value="SICK">إجازة مرضية</option>
              <option value="EMERGENCY">إجازة طارئة</option>
              <option value="MATERNITY">إجازة أمومة</option>
              <option value="PATERNITY">إجازة أبوة</option>
              <option value="UNPAID">إجازة بدون راتب</option>
            </select>
          </div>
        </div>

        {/* Leaves Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الموظف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    نوع الإجازة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التواريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    عدد الأيام
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    السبب
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredLeaves.map((leave) => (
                  <tr key={leave.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{leave.employeeName}</div>
                      <div className="text-sm text-gray-500">{leave.employeeId}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(leave.type)}`}>
                        {getTypeLabel(leave.type)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {new Date(leave.startDate).toLocaleDateString('ar-SA')} - {new Date(leave.endDate).toLocaleDateString('ar-SA')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{leave.days} يوم</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 max-w-xs truncate">
                        {leave.reason || '-'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(leave.status)}
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(leave.status)}`}>
                          {getStatusLabel(leave.status)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        {leave.status === 'PENDING' && (
                          <>
                            <button
                              onClick={() => handleApproveLeave(leave.id)}
                              className="text-green-600 hover:text-green-900"
                              title="موافقة"
                            >
                              <CheckCircle className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleRejectLeave(leave.id)}
                              className="text-red-600 hover:text-red-900"
                              title="رفض"
                            >
                              <XCircle className="h-4 w-4" />
                            </button>
                          </>
                        )}
                        <Link
                          href={`/hr/leaves/${leave.id}`}
                          className="text-blue-600 hover:text-blue-900"
                          title="عرض"
                        >
                          <Eye className="h-4 w-4" />
                        </Link>
                        <Link
                          href={`/hr/leaves/${leave.id}/edit`}
                          className="text-yellow-600 hover:text-yellow-900"
                          title="تعديل"
                        >
                          <Edit className="h-4 w-4" />
                        </Link>
                        <button 
                          onClick={() => handleDeleteLeave(leave.id)}
                          className="text-red-600 hover:text-red-900"
                          title="حذف"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredLeaves.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد إجازات مطابقة لبحثك</p>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  )
}
