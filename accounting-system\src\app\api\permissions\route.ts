import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to view permissions
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get all permissions, roles, and users
    const [permissions, roles, users] = await Promise.all([
      // For now, return static permissions since we don't have a permissions table
      Promise.resolve([
        // Dashboard Permissions
        { id: 'dashboard_view', name: 'عرض لوحة التحكم', description: 'عرض لوحة التحكم الرئيسية', module: 'dashboard', action: 'view' },
        
        // Accounting Permissions
        { id: 'invoices_view', name: 'عرض الفواتير', description: 'عرض قائمة الفواتير', module: 'accounting', action: 'view' },
        { id: 'invoices_create', name: 'إنشاء الفواتير', description: 'إنشاء فواتير جديدة', module: 'accounting', action: 'create' },
        { id: 'invoices_edit', name: 'تعديل الفواتير', description: 'تعديل الفواتير الموجودة', module: 'accounting', action: 'edit' },
        { id: 'invoices_delete', name: 'حذف الفواتير', description: 'حذف الفواتير', module: 'accounting', action: 'delete' },
        
        { id: 'expenses_view', name: 'عرض المصروفات', description: 'عرض قائمة المصروفات', module: 'accounting', action: 'view' },
        { id: 'expenses_create', name: 'إنشاء المصروفات', description: 'إنشاء مصروفات جديدة', module: 'accounting', action: 'create' },
        { id: 'expenses_edit', name: 'تعديل المصروفات', description: 'تعديل المصروفات الموجودة', module: 'accounting', action: 'edit' },
        { id: 'expenses_delete', name: 'حذف المصروفات', description: 'حذف المصروفات', module: 'accounting', action: 'delete' },
        
        // HR Permissions
        { id: 'hr_view', name: 'عرض الموارد البشرية', description: 'عرض بيانات الموظفين', module: 'hr', action: 'view' },
        { id: 'hr_create', name: 'إضافة موظفين', description: 'إضافة موظفين جدد', module: 'hr', action: 'create' },
        { id: 'hr_edit', name: 'تعديل بيانات الموظفين', description: 'تعديل بيانات الموظفين', module: 'hr', action: 'edit' },
        { id: 'hr_delete', name: 'حذف الموظفين', description: 'حذف الموظفين', module: 'hr', action: 'delete' },
        
        { id: 'payroll_view', name: 'عرض الرواتب', description: 'عرض بيانات الرواتب', module: 'hr', action: 'view' },
        { id: 'payroll_process', name: 'معالجة الرواتب', description: 'معالجة وصرف الرواتب', module: 'hr', action: 'process' },
        
        // Banking Permissions
        { id: 'banking_view', name: 'عرض البنوك', description: 'عرض الحسابات البنكية', module: 'banking', action: 'view' },
        { id: 'banking_create', name: 'إضافة حسابات بنكية', description: 'إضافة حسابات بنكية جديدة', module: 'banking', action: 'create' },
        { id: 'banking_edit', name: 'تعديل الحسابات البنكية', description: 'تعديل الحسابات البنكية', module: 'banking', action: 'edit' },
        { id: 'banking_delete', name: 'حذف الحسابات البنكية', description: 'حذف الحسابات البنكية', module: 'banking', action: 'delete' },
        
        // Treasury Permissions
        { id: 'treasury_view', name: 'عرض الخزينة', description: 'عرض الصناديق النقدية', module: 'treasury', action: 'view' },
        { id: 'treasury_create', name: 'إضافة صناديق نقدية', description: 'إضافة صناديق نقدية جديدة', module: 'treasury', action: 'create' },
        { id: 'treasury_edit', name: 'تعديل الصناديق النقدية', description: 'تعديل الصناديق النقدية', module: 'treasury', action: 'edit' },
        { id: 'treasury_delete', name: 'حذف الصناديق النقدية', description: 'حذف الصناديق النقدية', module: 'treasury', action: 'delete' },
        
        // Assets Permissions
        { id: 'assets_view', name: 'عرض الأصول', description: 'عرض قائمة الأصول', module: 'assets', action: 'view' },
        { id: 'assets_create', name: 'إضافة أصول', description: 'إضافة أصول جديدة', module: 'assets', action: 'create' },
        { id: 'assets_edit', name: 'تعديل الأصول', description: 'تعديل الأصول الموجودة', module: 'assets', action: 'edit' },
        { id: 'assets_delete', name: 'حذف الأصول', description: 'حذف الأصول', module: 'assets', action: 'delete' },
        
        // Settings Permissions
        { id: 'settings_view', name: 'عرض الإعدادات', description: 'عرض إعدادات النظام', module: 'settings', action: 'view' },
        { id: 'settings_edit', name: 'تعديل الإعدادات', description: 'تعديل إعدادات النظام', module: 'settings', action: 'edit' },
        { id: 'users_manage', name: 'إدارة المستخدمين', description: 'إدارة المستخدمين والصلاحيات', module: 'settings', action: 'manage' },
        
        // Reports Permissions
        { id: 'reports_view', name: 'عرض التقارير', description: 'عرض جميع التقارير', module: 'reports', action: 'view' },
        { id: 'reports_export', name: 'تصدير التقارير', description: 'تصدير التقارير', module: 'reports', action: 'export' }
      ]),
      
      // Get roles from database or return static roles
      Promise.resolve([
        {
          id: 'ADMIN',
          name: 'مدير النظام',
          description: 'صلاحيات كاملة لجميع أجزاء النظام',
          permissions: ['dashboard_view', 'invoices_view', 'invoices_create', 'invoices_edit', 'invoices_delete', 'expenses_view', 'expenses_create', 'expenses_edit', 'expenses_delete', 'hr_view', 'hr_create', 'hr_edit', 'hr_delete', 'payroll_view', 'payroll_process', 'banking_view', 'banking_create', 'banking_edit', 'banking_delete', 'treasury_view', 'treasury_create', 'treasury_edit', 'treasury_delete', 'assets_view', 'assets_create', 'assets_edit', 'assets_delete', 'settings_view', 'settings_edit', 'users_manage', 'reports_view', 'reports_export'],
          userCount: 1,
          isSystem: true
        },
        {
          id: 'MANAGER',
          name: 'مدير',
          description: 'صلاحيات إدارية للمحاسبة والموارد البشرية',
          permissions: ['dashboard_view', 'invoices_view', 'invoices_create', 'invoices_edit', 'expenses_view', 'expenses_create', 'expenses_edit', 'hr_view', 'hr_create', 'hr_edit', 'payroll_view', 'payroll_process', 'banking_view', 'banking_create', 'banking_edit', 'treasury_view', 'treasury_create', 'treasury_edit', 'assets_view', 'assets_create', 'assets_edit', 'reports_view', 'reports_export'],
          userCount: 1,
          isSystem: true
        },
        {
          id: 'ACCOUNTANT',
          name: 'محاسب',
          description: 'صلاحيات المحاسبة والتقارير المالية',
          permissions: ['dashboard_view', 'invoices_view', 'invoices_create', 'invoices_edit', 'expenses_view', 'expenses_create', 'expenses_edit', 'banking_view', 'treasury_view', 'assets_view', 'reports_view'],
          userCount: 1,
          isSystem: true
        }
      ]),
      
      // Get users from database
      prisma.user.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true
        }
      })
    ])

    return NextResponse.json({
      permissions,
      roles,
      users: users.map(user => ({
        ...user,
        permissions: [], // Additional user-specific permissions
        isActive: true
      }))
    })
  } catch (error) {
    console.error('Error fetching permissions data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const { type, data } = body

    switch (type) {
      case 'UPDATE_ROLE_PERMISSIONS':
        // Update role permissions
        // For now, just return success since we don't have a roles table
        await prisma.activityLog.create({
          data: {
            userId: session.user.id,
            action: 'UPDATE_ROLE_PERMISSIONS',
            description: `تم تحديث صلاحيات الدور: ${data.roleName}`,
            metadata: {
              roleId: data.roleId,
              permissions: data.permissions
            }
          }
        })
        break

      case 'UPDATE_USER_PERMISSIONS':
        // Update user-specific permissions
        // For now, just log the activity
        await prisma.activityLog.create({
          data: {
            userId: session.user.id,
            action: 'UPDATE_USER_PERMISSIONS',
            description: `تم تحديث صلاحيات المستخدم: ${data.userName}`,
            metadata: {
              targetUserId: data.userId,
              permissions: data.permissions
            }
          }
        })
        break

      case 'CREATE_ROLE':
        // Create new role
        await prisma.activityLog.create({
          data: {
            userId: session.user.id,
            action: 'CREATE_ROLE',
            description: `تم إنشاء دور جديد: ${data.name}`,
            metadata: {
              roleName: data.name,
              permissions: data.permissions
            }
          }
        })
        break

      default:
        return NextResponse.json(
          { error: 'Invalid operation type' },
          { status: 400 }
        )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error updating permissions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
