'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import MainLayout from '@/components/Layout/MainLayout'
import { Save, ArrowLeft, Upload, User } from 'lucide-react'

const departments = [
  'تقنية المعلومات',
  'المالية',
  'الموارد البشرية',
  'المبيعات',
  'التسويق',
  'العمليات',
  'خدمة العملاء',
  'الإدارة العامة',
  'الأمن والسلامة',
  'الصيانة'
]

const positions = [
  'مدير عام',
  'مدير قسم',
  'مشرف',
  'موظف أول',
  'موظف',
  'متدرب',
  'مطور برمجيات',
  'محاسب',
  'مندوب مبيعات',
  'أخصائي تسويق',
  'أخصائي موارد بشرية',
  'سكرتير',
  'حارس أمن',
  'فني صيانة'
]

export default function NewEmployeePage() {
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [employee, setEmployee] = useState({
    employeeId: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    dateOfBirth: '',
    hireDate: new Date().toISOString().split('T')[0],
    position: '',
    department: '',
    salary: '',
    nationalId: '',
    emergencyContact: '',
    emergencyPhone: '',
    bankAccount: '',
    notes: '',
    profileImage: null as File | null
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setEmployee(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setEmployee(prev => ({ ...prev, profileImage: file }))
      
      // Create preview
      const reader = new FileReader()
      reader.onloadend = () => {
        setImagePreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const generateEmployeeId = () => {
    const prefix = 'EMP'
    const timestamp = Date.now().toString().slice(-6)
    return `${prefix}${timestamp}`
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!employee.firstName || !employee.lastName || !employee.email || !employee.position || !employee.department || !employee.salary) {
      alert('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    if (parseFloat(employee.salary) <= 0) {
      alert('يجب أن يكون الراتب أكبر من صفر')
      return
    }

    setSaving(true)

    try {
      const formData = new FormData()
      
      // Add all employee data
      formData.append('employeeId', employee.employeeId || generateEmployeeId())
      formData.append('firstName', employee.firstName)
      formData.append('lastName', employee.lastName)
      formData.append('email', employee.email)
      formData.append('phone', employee.phone)
      formData.append('address', employee.address)
      formData.append('dateOfBirth', employee.dateOfBirth)
      formData.append('hireDate', employee.hireDate)
      formData.append('position', employee.position)
      formData.append('department', employee.department)
      formData.append('salary', employee.salary)
      formData.append('nationalId', employee.nationalId)
      formData.append('emergencyContact', employee.emergencyContact)
      formData.append('emergencyPhone', employee.emergencyPhone)
      formData.append('bankAccount', employee.bankAccount)
      formData.append('notes', employee.notes)
      
      // Add image if selected
      if (employee.profileImage) {
        formData.append('profileImage', employee.profileImage)
      }

      const response = await fetch('/api/hr/employees', {
        method: 'POST',
        body: formData,
      })

      if (response.ok) {
        alert('تم إضافة الموظف بنجاح!')
        router.push('/hr/employees')
      } else {
        const error = await response.json()
        alert(`حدث خطأ: ${error.error || 'خطأ غير معروف'}`)
      }
    } catch (error) {
      console.error('Error saving employee:', error)
      alert('حدث خطأ أثناء حفظ بيانات الموظف')
    } finally {
      setSaving(false)
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.push('/hr/employees')}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5" />
            العودة للموظفين
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إضافة موظف جديد</h1>
            <p className="mt-2 text-gray-600">إضافة موظف جديد إلى النظام</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Profile Image */}
            <div className="flex flex-col items-center">
              <div className="relative">
                {imagePreview ? (
                  <img
                    src={imagePreview}
                    alt="معاينة الصورة"
                    className="w-32 h-32 rounded-full object-cover border-4 border-gray-200"
                  />
                ) : (
                  <div className="w-32 h-32 rounded-full bg-gray-200 flex items-center justify-center border-4 border-gray-200">
                    <User className="w-16 h-16 text-gray-400" />
                  </div>
                )}
                <label className="absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full cursor-pointer hover:bg-blue-700">
                  <Upload className="w-4 h-4" />
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="hidden"
                  />
                </label>
              </div>
              <p className="mt-2 text-sm text-gray-500">اضغط لرفع صورة الموظف</p>
            </div>

            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">المعلومات الأساسية</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="employeeId" className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الموظف
                  </label>
                  <input
                    type="text"
                    id="employeeId"
                    name="employeeId"
                    value={employee.employeeId}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="سيتم إنشاؤه تلقائياً إذا ترك فارغاً"
                  />
                </div>

                <div>
                  <label htmlFor="nationalId" className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الهوية الوطنية
                  </label>
                  <input
                    type="text"
                    id="nationalId"
                    name="nationalId"
                    value={employee.nationalId}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="رقم الهوية الوطنية"
                  />
                </div>

                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                    الاسم الأول *
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    value={employee.firstName}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="الاسم الأول"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                    اسم العائلة *
                  </label>
                  <input
                    type="text"
                    id="lastName"
                    name="lastName"
                    value={employee.lastName}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="اسم العائلة"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    البريد الإلكتروني *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={employee.email}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="البريد الإلكتروني"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الهاتف
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={employee.phone}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="+************"
                  />
                </div>

                <div>
                  <label htmlFor="dateOfBirth" className="block text-sm font-medium text-gray-700 mb-2">
                    تاريخ الميلاد
                  </label>
                  <input
                    type="date"
                    id="dateOfBirth"
                    name="dateOfBirth"
                    value={employee.dateOfBirth}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label htmlFor="hireDate" className="block text-sm font-medium text-gray-700 mb-2">
                    تاريخ التوظيف *
                  </label>
                  <input
                    type="date"
                    id="hireDate"
                    name="hireDate"
                    value={employee.hireDate}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
              </div>

              <div className="mt-6">
                <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                  العنوان
                </label>
                <textarea
                  id="address"
                  name="address"
                  value={employee.address}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="العنوان الكامل"
                />
              </div>
            </div>

            {/* Job Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">معلومات الوظيفة</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-2">
                    القسم *
                  </label>
                  <select
                    id="department"
                    name="department"
                    value={employee.department}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  >
                    <option value="">اختر القسم</option>
                    {departments.map(dept => (
                      <option key={dept} value={dept}>{dept}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-2">
                    المنصب *
                  </label>
                  <select
                    id="position"
                    name="position"
                    value={employee.position}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  >
                    <option value="">اختر المنصب</option>
                    {positions.map(pos => (
                      <option key={pos} value={pos}>{pos}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="salary" className="block text-sm font-medium text-gray-700 mb-2">
                    الراتب الأساسي (ر.س) *
                  </label>
                  <input
                    type="number"
                    id="salary"
                    name="salary"
                    value={employee.salary}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0.00"
                    required
                  />
                </div>
              </div>
            </div>

            {/* Emergency Contact & Banking */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">معلومات إضافية</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="emergencyContact" className="block text-sm font-medium text-gray-700 mb-2">
                    جهة الاتصال في الطوارئ
                  </label>
                  <input
                    type="text"
                    id="emergencyContact"
                    name="emergencyContact"
                    value={employee.emergencyContact}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="اسم جهة الاتصال"
                  />
                </div>

                <div>
                  <label htmlFor="emergencyPhone" className="block text-sm font-medium text-gray-700 mb-2">
                    رقم هاتف الطوارئ
                  </label>
                  <input
                    type="tel"
                    id="emergencyPhone"
                    name="emergencyPhone"
                    value={employee.emergencyPhone}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="+************"
                  />
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="bankAccount" className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الحساب البنكي
                  </label>
                  <input
                    type="text"
                    id="bankAccount"
                    name="bankAccount"
                    value={employee.bankAccount}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="رقم الحساب البنكي"
                  />
                </div>
              </div>

              <div className="mt-6">
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                  ملاحظات
                </label>
                <textarea
                  id="notes"
                  name="notes"
                  value={employee.notes}
                  onChange={handleChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="ملاحظات إضافية حول الموظف..."
                />
              </div>
            </div>

            <div className="flex justify-end gap-4 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => router.push('/hr/employees')}
                className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                إلغاء
              </button>
              <button
                type="submit"
                disabled={saving}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {saving ? 'جاري الحفظ...' : 'حفظ الموظف'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </MainLayout>
  )
}
