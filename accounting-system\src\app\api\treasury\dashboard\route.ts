import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get cash box statistics
    const totalCashBoxes = await prisma.cashBox.count()
    const activeCashBoxes = await prisma.cashBox.count({
      where: { isActive: true }
    })

    // Get total balance (sum of all active cash boxes)
    const activeCashBoxesData = await prisma.cashBox.findMany({
      where: { isActive: true }
    })
    
    const totalBalance = activeCashBoxesData.reduce((sum, cashBox) => {
      // Convert all balances to SAR for total calculation
      if (cashBox.currency === 'SAR') {
        return sum + cashBox.balance
      } else if (cashBox.currency === 'USD') {
        return sum + (cashBox.balance * 3.75) // USD to SAR
      } else if (cashBox.currency === 'EUR') {
        return sum + (cashBox.balance * 4.1) // EUR to SAR
      } else if (cashBox.currency === 'GBP') {
        return sum + (cashBox.balance * 4.7) // GBP to SAR
      }
      return sum + cashBox.balance
    }, 0)

    // Get today's transactions
    const today = new Date()
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)

    const todayTransactions = await prisma.cashTransaction.findMany({
      where: {
        transactionDate: {
          gte: startOfDay,
          lt: endOfDay
        },
        status: 'COMPLETED'
      }
    })

    const todayTransactionsCount = todayTransactions.length

    const todayCashIn = todayTransactions
      .filter(t => t.type === 'CASH_IN' || t.type === 'TRANSFER_IN')
      .reduce((sum, t) => sum + t.amount, 0)

    const todayCashOut = todayTransactions
      .filter(t => t.type === 'CASH_OUT' || t.type === 'TRANSFER_OUT')
      .reduce((sum, t) => sum + t.amount, 0)

    // Get pending petty cash requests
    const pendingPettyCash = await prisma.pettyCash.count({
      where: { 
        status: {
          in: ['PENDING', 'APPROVED']
        }
      }
    })

    // Get pending daily reports (reports that are not closed)
    const pendingReports = await prisma.cashDailyReport.count({
      where: {
        status: {
          in: ['DRAFT', 'SUBMITTED']
        }
      }
    })

    const stats = {
      totalCashBoxes,
      activeCashBoxes,
      totalBalance: Math.round(totalBalance),
      todayTransactions: todayTransactionsCount,
      todayCashIn,
      todayCashOut,
      pendingPettyCash,
      pendingReports
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching treasury dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
