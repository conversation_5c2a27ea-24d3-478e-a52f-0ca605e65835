'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import MainLayout from '@/components/Layout/MainLayout'
import { 
  ArrowLeft, 
  Shield, 
  Users, 
  Save, 
  Eye, 
  Edit, 
  Trash2, 
  Plus,
  Check,
  X,
  Settings,
  FileText,
  CreditCard,
  Building,
  Wallet,
  Briefcase,
  UserCheck
} from 'lucide-react'

interface Permission {
  id: string
  name: string
  description: string
  module: string
  action: string
}

interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  userCount: number
  isSystem: boolean
}

interface User {
  id: string
  name: string
  email: string
  role: string
  permissions: string[]
  isActive: boolean
}

export default function PermissionsPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('roles')
  const [loading, setLoading] = useState(true)
  
  const [permissions] = useState<Permission[]>([
    // Dashboard Permissions
    { id: 'dashboard_view', name: 'عرض لوحة التحكم', description: 'عرض لوحة التحكم الرئيسية', module: 'dashboard', action: 'view' },
    
    // Accounting Permissions
    { id: 'invoices_view', name: 'عرض الفواتير', description: 'عرض قائمة الفواتير', module: 'accounting', action: 'view' },
    { id: 'invoices_create', name: 'إنشاء الفواتير', description: 'إنشاء فواتير جديدة', module: 'accounting', action: 'create' },
    { id: 'invoices_edit', name: 'تعديل الفواتير', description: 'تعديل الفواتير الموجودة', module: 'accounting', action: 'edit' },
    { id: 'invoices_delete', name: 'حذف الفواتير', description: 'حذف الفواتير', module: 'accounting', action: 'delete' },
    
    { id: 'expenses_view', name: 'عرض المصروفات', description: 'عرض قائمة المصروفات', module: 'accounting', action: 'view' },
    { id: 'expenses_create', name: 'إنشاء المصروفات', description: 'إنشاء مصروفات جديدة', module: 'accounting', action: 'create' },
    { id: 'expenses_edit', name: 'تعديل المصروفات', description: 'تعديل المصروفات الموجودة', module: 'accounting', action: 'edit' },
    { id: 'expenses_delete', name: 'حذف المصروفات', description: 'حذف المصروفات', module: 'accounting', action: 'delete' },
    
    // HR Permissions
    { id: 'hr_view', name: 'عرض الموارد البشرية', description: 'عرض بيانات الموظفين', module: 'hr', action: 'view' },
    { id: 'hr_create', name: 'إضافة موظفين', description: 'إضافة موظفين جدد', module: 'hr', action: 'create' },
    { id: 'hr_edit', name: 'تعديل بيانات الموظفين', description: 'تعديل بيانات الموظفين', module: 'hr', action: 'edit' },
    { id: 'hr_delete', name: 'حذف الموظفين', description: 'حذف الموظفين', module: 'hr', action: 'delete' },
    
    { id: 'payroll_view', name: 'عرض الرواتب', description: 'عرض بيانات الرواتب', module: 'hr', action: 'view' },
    { id: 'payroll_process', name: 'معالجة الرواتب', description: 'معالجة وصرف الرواتب', module: 'hr', action: 'process' },
    
    // Banking Permissions
    { id: 'banking_view', name: 'عرض البنوك', description: 'عرض الحسابات البنكية', module: 'banking', action: 'view' },
    { id: 'banking_create', name: 'إضافة حسابات بنكية', description: 'إضافة حسابات بنكية جديدة', module: 'banking', action: 'create' },
    { id: 'banking_edit', name: 'تعديل الحسابات البنكية', description: 'تعديل الحسابات البنكية', module: 'banking', action: 'edit' },
    { id: 'banking_delete', name: 'حذف الحسابات البنكية', description: 'حذف الحسابات البنكية', module: 'banking', action: 'delete' },
    
    // Treasury Permissions
    { id: 'treasury_view', name: 'عرض الخزينة', description: 'عرض الصناديق النقدية', module: 'treasury', action: 'view' },
    { id: 'treasury_create', name: 'إضافة صناديق نقدية', description: 'إضافة صناديق نقدية جديدة', module: 'treasury', action: 'create' },
    { id: 'treasury_edit', name: 'تعديل الصناديق النقدية', description: 'تعديل الصناديق النقدية', module: 'treasury', action: 'edit' },
    { id: 'treasury_delete', name: 'حذف الصناديق النقدية', description: 'حذف الصناديق النقدية', module: 'treasury', action: 'delete' },
    
    // Assets Permissions
    { id: 'assets_view', name: 'عرض الأصول', description: 'عرض قائمة الأصول', module: 'assets', action: 'view' },
    { id: 'assets_create', name: 'إضافة أصول', description: 'إضافة أصول جديدة', module: 'assets', action: 'create' },
    { id: 'assets_edit', name: 'تعديل الأصول', description: 'تعديل الأصول الموجودة', module: 'assets', action: 'edit' },
    { id: 'assets_delete', name: 'حذف الأصول', description: 'حذف الأصول', module: 'assets', action: 'delete' },
    
    // Settings Permissions
    { id: 'settings_view', name: 'عرض الإعدادات', description: 'عرض إعدادات النظام', module: 'settings', action: 'view' },
    { id: 'settings_edit', name: 'تعديل الإعدادات', description: 'تعديل إعدادات النظام', module: 'settings', action: 'edit' },
    { id: 'users_manage', name: 'إدارة المستخدمين', description: 'إدارة المستخدمين والصلاحيات', module: 'settings', action: 'manage' },
    
    // Reports Permissions
    { id: 'reports_view', name: 'عرض التقارير', description: 'عرض جميع التقارير', module: 'reports', action: 'view' },
    { id: 'reports_export', name: 'تصدير التقارير', description: 'تصدير التقارير', module: 'reports', action: 'export' }
  ])

  const [roles, setRoles] = useState<Role[]>([
    {
      id: 'admin',
      name: 'مدير النظام',
      description: 'صلاحيات كاملة لجميع أجزاء النظام',
      permissions: permissions.map(p => p.id),
      userCount: 1,
      isSystem: true
    },
    {
      id: 'manager',
      name: 'مدير',
      description: 'صلاحيات إدارية للمحاسبة والموارد البشرية',
      permissions: [
        'dashboard_view',
        'invoices_view', 'invoices_create', 'invoices_edit',
        'expenses_view', 'expenses_create', 'expenses_edit',
        'hr_view', 'hr_create', 'hr_edit',
        'payroll_view', 'payroll_process',
        'banking_view', 'banking_create', 'banking_edit',
        'treasury_view', 'treasury_create', 'treasury_edit',
        'assets_view', 'assets_create', 'assets_edit',
        'reports_view', 'reports_export'
      ],
      userCount: 1,
      isSystem: true
    },
    {
      id: 'accountant',
      name: 'محاسب',
      description: 'صلاحيات المحاسبة والتقارير المالية',
      permissions: [
        'dashboard_view',
        'invoices_view', 'invoices_create', 'invoices_edit',
        'expenses_view', 'expenses_create', 'expenses_edit',
        'banking_view',
        'treasury_view',
        'assets_view',
        'reports_view'
      ],
      userCount: 1,
      isSystem: true
    },
    {
      id: 'hr_specialist',
      name: 'أخصائي موارد بشرية',
      description: 'صلاحيات الموارد البشرية والرواتب',
      permissions: [
        'dashboard_view',
        'hr_view', 'hr_create', 'hr_edit',
        'payroll_view', 'payroll_process',
        'reports_view'
      ],
      userCount: 1,
      isSystem: false
    }
  ])

  const [users, setUsers] = useState<User[]>([
    {
      id: '1',
      name: 'أحمد محمد',
      email: '<EMAIL>',
      role: 'admin',
      permissions: [],
      isActive: true
    },
    {
      id: '2',
      name: 'فاطمة أحمد',
      email: '<EMAIL>',
      role: 'manager',
      permissions: [],
      isActive: true
    },
    {
      id: '3',
      name: 'خالد علي',
      email: '<EMAIL>',
      role: 'accountant',
      permissions: [],
      isActive: true
    },
    {
      id: '4',
      name: 'سارة محمود',
      email: '<EMAIL>',
      role: 'hr_specialist',
      permissions: ['reports_export'],
      isActive: true
    }
  ])

  useEffect(() => {
    setLoading(false)
  }, [])

  const getModuleIcon = (module: string) => {
    switch (module) {
      case 'dashboard':
        return <Settings className="h-4 w-4" />
      case 'accounting':
        return <FileText className="h-4 w-4" />
      case 'hr':
        return <UserCheck className="h-4 w-4" />
      case 'banking':
        return <Building className="h-4 w-4" />
      case 'treasury':
        return <Wallet className="h-4 w-4" />
      case 'assets':
        return <Briefcase className="h-4 w-4" />
      case 'settings':
        return <Settings className="h-4 w-4" />
      case 'reports':
        return <FileText className="h-4 w-4" />
      default:
        return <Shield className="h-4 w-4" />
    }
  }

  const getModuleColor = (module: string) => {
    switch (module) {
      case 'dashboard':
        return 'bg-blue-100 text-blue-800'
      case 'accounting':
        return 'bg-green-100 text-green-800'
      case 'hr':
        return 'bg-purple-100 text-purple-800'
      case 'banking':
        return 'bg-indigo-100 text-indigo-800'
      case 'treasury':
        return 'bg-yellow-100 text-yellow-800'
      case 'assets':
        return 'bg-orange-100 text-orange-800'
      case 'settings':
        return 'bg-gray-100 text-gray-800'
      case 'reports':
        return 'bg-pink-100 text-pink-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleRolePermissionToggle = (roleId: string, permissionId: string) => {
    setRoles(roles.map(role => {
      if (role.id === roleId && !role.isSystem) {
        const hasPermission = role.permissions.includes(permissionId)
        return {
          ...role,
          permissions: hasPermission
            ? role.permissions.filter(p => p !== permissionId)
            : [...role.permissions, permissionId]
        }
      }
      return role
    }))
  }

  const handleUserPermissionToggle = (userId: string, permissionId: string) => {
    setUsers(users.map(user => {
      if (user.id === userId) {
        const hasPermission = user.permissions.includes(permissionId)
        return {
          ...user,
          permissions: hasPermission
            ? user.permissions.filter(p => p !== permissionId)
            : [...user.permissions, permissionId]
        }
      }
      return user
    }))
  }

  const getUserEffectivePermissions = (user: User) => {
    const role = roles.find(r => r.id === user.role)
    const rolePermissions = role ? role.permissions : []
    return [...new Set([...rolePermissions, ...user.permissions])]
  }

  const hasPermission = (user: User, permissionId: string) => {
    const effectivePermissions = getUserEffectivePermissions(user)
    return effectivePermissions.includes(permissionId)
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.push('/settings')}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5" />
            العودة للإعدادات
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة الصلاحيات</h1>
            <p className="mt-2 text-gray-600">إدارة الأدوار والصلاحيات للمستخدمين</p>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('roles')}
              className={`${
                activeTab === 'roles'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
            >
              <Shield className="h-4 w-4" />
              الأدوار
            </button>
            <button
              onClick={() => setActiveTab('users')}
              className={`${
                activeTab === 'users'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
            >
              <Users className="h-4 w-4" />
              المستخدمين
            </button>
            <button
              onClick={() => setActiveTab('permissions')}
              className={`${
                activeTab === 'permissions'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
            >
              <Settings className="h-4 w-4" />
              الصلاحيات
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'roles' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">إدارة الأدوار</h2>
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                <Plus className="h-4 w-4" />
                إضافة دور جديد
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {roles.map((role) => (
                <div key={role.id} className="bg-white rounded-lg shadow border">
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                          <Shield className="h-5 w-5 text-blue-600" />
                          {role.name}
                          {role.isSystem && (
                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                              نظام
                            </span>
                          )}
                        </h3>
                        <p className="text-sm text-gray-600 mt-1">{role.description}</p>
                        <p className="text-xs text-gray-500 mt-2">
                          {role.userCount} مستخدم • {role.permissions.length} صلاحية
                        </p>
                      </div>
                      {!role.isSystem && (
                        <div className="flex gap-2">
                          <button className="text-blue-600 hover:text-blue-900">
                            <Edit className="h-4 w-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-900">
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      )}
                    </div>

                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-gray-900">الصلاحيات:</h4>
                      <div className="max-h-48 overflow-y-auto space-y-2">
                        {permissions.map((permission) => (
                          <div key={permission.id} className="flex items-center justify-between py-1">
                            <div className="flex items-center gap-2">
                              <span className={`inline-flex px-2 py-1 text-xs rounded-full ${getModuleColor(permission.module)}`}>
                                {getModuleIcon(permission.module)}
                                <span className="mr-1">{permission.module}</span>
                              </span>
                              <span className="text-sm text-gray-700">{permission.name}</span>
                            </div>
                            <div className="flex items-center">
                              {role.isSystem ? (
                                role.permissions.includes(permission.id) ? (
                                  <Check className="h-4 w-4 text-green-600" />
                                ) : (
                                  <X className="h-4 w-4 text-gray-400" />
                                )
                              ) : (
                                <input
                                  type="checkbox"
                                  checked={role.permissions.includes(permission.id)}
                                  onChange={() => handleRolePermissionToggle(role.id, permission.id)}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {!role.isSystem && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <button className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2">
                          <Save className="h-4 w-4" />
                          حفظ التغييرات
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'users' && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-900">صلاحيات المستخدمين</h2>

            <div className="space-y-4">
              {users.map((user) => {
                const userRole = roles.find(r => r.id === user.role)
                const effectivePermissions = getUserEffectivePermissions(user)

                return (
                  <div key={user.id} className="bg-white rounded-lg shadow border">
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                            <Users className="h-5 w-5 text-blue-600" />
                            {user.name}
                          </h3>
                          <p className="text-sm text-gray-600">{user.email}</p>
                          <div className="flex items-center gap-2 mt-2">
                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                              {userRole?.name}
                            </span>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {user.isActive ? 'نشط' : 'غير نشط'}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <h4 className="text-sm font-medium text-gray-900">
                          الصلاحيات الفعالة ({effectivePermissions.length}):
                        </h4>
                        <div className="max-h-48 overflow-y-auto space-y-2">
                          {permissions.map((permission) => {
                            const hasFromRole = userRole?.permissions.includes(permission.id) || false
                            const hasFromUser = user.permissions.includes(permission.id)
                            const hasEffective = hasPermission(user, permission.id)

                            return (
                              <div key={permission.id} className="flex items-center justify-between py-1">
                                <div className="flex items-center gap-2">
                                  <span className={`inline-flex px-2 py-1 text-xs rounded-full ${getModuleColor(permission.module)}`}>
                                    {getModuleIcon(permission.module)}
                                    <span className="mr-1">{permission.module}</span>
                                  </span>
                                  <span className="text-sm text-gray-700">{permission.name}</span>
                                  {hasFromRole && (
                                    <span className="text-xs text-blue-600">(من الدور)</span>
                                  )}
                                </div>
                                <div className="flex items-center gap-2">
                                  {hasEffective && (
                                    <Check className="h-4 w-4 text-green-600" />
                                  )}
                                  <input
                                    type="checkbox"
                                    checked={hasFromUser}
                                    onChange={() => handleUserPermissionToggle(user.id, permission.id)}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    title="صلاحية إضافية للمستخدم"
                                  />
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      </div>

                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <button className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2">
                          <Save className="h-4 w-4" />
                          حفظ صلاحيات المستخدم
                        </button>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {activeTab === 'permissions' && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-900">جميع الصلاحيات</h2>

            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الصلاحية
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الوحدة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الوصف
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الأدوار المخولة
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {permissions.map((permission) => {
                      const authorizedRoles = roles.filter(role =>
                        role.permissions.includes(permission.id)
                      )

                      return (
                        <tr key={permission.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{permission.name}</div>
                            <div className="text-sm text-gray-500">{permission.id}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs rounded-full ${getModuleColor(permission.module)}`}>
                              {getModuleIcon(permission.module)}
                              <span className="mr-1">{permission.module}</span>
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            <div className="text-sm text-gray-900">{permission.description}</div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex flex-wrap gap-1">
                              {authorizedRoles.map((role) => (
                                <span
                                  key={role.id}
                                  className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800"
                                >
                                  {role.name}
                                </span>
                              ))}
                            </div>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Help Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">نصائح لإدارة الصلاحيات:</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• امنح كل مستخدم الصلاحيات المناسبة لدوره فقط</li>
            <li>• استخدم الأدوار لتجميع الصلاحيات المتشابهة</li>
            <li>• يمكن إضافة صلاحيات إضافية للمستخدمين عند الحاجة</li>
            <li>• راجع الصلاحيات دورياً وأزل غير المستخدمة</li>
            <li>• الأدوار النظام محمية ولا يمكن تعديلها</li>
          </ul>
        </div>
      </div>
    </MainLayout>
  )
}
