import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const transactions = await prisma.cashTransaction.findMany({
      include: {
        cashBox: true
      },
      orderBy: {
        transactionDate: 'desc'
      }
    })

    // Map to include cash box name
    const transactionsWithCashBoxName = transactions.map(transaction => ({
      ...transaction,
      cashBoxName: transaction.cashBox.name
    }))

    return NextResponse.json(transactionsWithCashBoxName)
  } catch (error) {
    console.error('Error fetching cash transactions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      cashBoxId,
      type,
      amount,
      description,
      reference,
      category,
      transactionDate,
      relatedId,
      relatedType,
      receiptNumber,
      notes
    } = body

    // Validate required fields
    if (!cashBoxId || !type || !amount || !description) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate amount
    if (amount <= 0) {
      return NextResponse.json(
        { error: 'Amount must be greater than zero' },
        { status: 400 }
      )
    }

    // Get current cash box balance
    const cashBox = await prisma.cashBox.findUnique({
      where: { id: cashBoxId }
    })

    if (!cashBox) {
      return NextResponse.json(
        { error: 'Cash box not found' },
        { status: 404 }
      )
    }

    if (!cashBox.isActive) {
      return NextResponse.json(
        { error: 'Cash box is not active' },
        { status: 400 }
      )
    }

    // Calculate new balance
    let newBalance = cashBox.balance
    if (type === 'CASH_IN' || type === 'TRANSFER_IN') {
      newBalance += parseFloat(amount)
    } else if (type === 'CASH_OUT' || type === 'TRANSFER_OUT') {
      newBalance -= parseFloat(amount)
      
      // Check for sufficient funds
      if (newBalance < 0) {
        return NextResponse.json(
          { error: 'Insufficient funds in cash box' },
          { status: 400 }
        )
      }
    }

    // Check daily limit
    if (cashBox.dailyLimit) {
      const today = new Date()
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
      const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)

      const todayTransactions = await prisma.cashTransaction.findMany({
        where: {
          cashBoxId,
          transactionDate: {
            gte: startOfDay,
            lt: endOfDay
          },
          status: 'COMPLETED'
        }
      })

      const todayTotal = todayTransactions.reduce((sum, t) => sum + t.amount, 0)
      
      if (todayTotal + parseFloat(amount) > cashBox.dailyLimit) {
        return NextResponse.json(
          { error: 'Transaction exceeds daily limit' },
          { status: 400 }
        )
      }
    }

    // Create transaction
    const transaction = await prisma.cashTransaction.create({
      data: {
        cashBoxId,
        type,
        amount: parseFloat(amount),
        balance: newBalance,
        description,
        reference: reference || null,
        category: category || null,
        transactionDate: transactionDate ? new Date(transactionDate) : new Date(),
        performedBy: session.user.name || 'مستخدم',
        relatedId: relatedId || null,
        relatedType: relatedType || null,
        receiptNumber: receiptNumber || null,
        notes: notes || null,
        status: 'COMPLETED'
      },
      include: {
        cashBox: true
      }
    })

    // Update cash box balance
    await prisma.cashBox.update({
      where: { id: cashBoxId },
      data: { balance: newBalance }
    })

    return NextResponse.json({
      ...transaction,
      cashBoxName: transaction.cashBox.name
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating cash transaction:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
