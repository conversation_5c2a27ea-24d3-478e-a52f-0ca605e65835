"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/banking/accounts/page",{

/***/ "(app-pages-browser)/./src/components/Layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/Layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst navigation = [\n    {\n        name: 'لوحة التحكم',\n        href: '/',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: 'العملاء',\n        href: '/customers',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'الفواتير',\n        href: '/invoices',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'المنتجات',\n        href: '/products',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'المصروفات',\n        href: '/expenses',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'البنوك',\n        href: '/banking',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'الأصول',\n        href: '/assets',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: 'الموارد البشرية',\n        href: '/hr',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: 'التقارير',\n        href: '/reports',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: 'المستخدمين',\n        href: '/users',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        adminOnly: true\n    },\n    {\n        name: 'الإعدادات',\n        href: '/settings',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    var _session_user, _session_user1, _session_user2, _session_user3;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const handleSignOut = ()=>{\n        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)({\n            callbackUrl: '/auth/signin'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full w-64 flex-col bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center justify-center bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-bold text-white\",\n                    children: \"نظام المحاسبة\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 space-y-1 px-2 py-4\",\n                children: navigation.map((item)=>{\n                    var _session_user;\n                    // Hide admin-only items for non-admin users\n                    if (item.adminOnly && (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) !== 'ADMIN') {\n                        return null;\n                    }\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: item.href,\n                        className: \"\\n                group flex items-center px-2 py-2 text-sm font-medium rounded-md\\n                \".concat(isActive ? 'bg-gray-800 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white', \"\\n              \"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"\\n                  mr-3 h-5 w-5 flex-shrink-0\\n                  \".concat(isActive ? 'text-white' : 'text-gray-400 group-hover:text-white', \"\\n                \")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this),\n                            item.name\n                        ]\n                    }, item.name, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-700 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-full bg-gray-600 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-white\",\n                                        children: (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name) || (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.email)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.role) === 'ADMIN' ? 'مشرف' : (session === null || session === void 0 ? void 0 : (_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : _session_user3.role) === 'MANAGER' ? 'مدير' : 'محاسب'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSignOut,\n                        className: \"mt-3 w-full flex items-center px-2 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-gray-700 hover:text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-3 h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            \"تسجيل الخروج\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"vU8JzSd4L4Kl4BSMCCU4jwFXDNQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Layout/Sidebar.tsx\n"));

/***/ })

});