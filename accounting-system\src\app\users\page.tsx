'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import MainLayout from '@/components/Layout/MainLayout'
import { Plus, Search, Edit, Trash2, User, Shield, UserCheck } from 'lucide-react'

interface User {
  id: string
  name: string
  email: string
  role: 'ADMIN' | 'MANAGER' | 'ACCOUNTANT'
  createdAt: string
  lastLogin?: string
}

const roleLabels = {
  ADMIN: 'مشرف',
  MANAGER: 'مدير',
  ACCOUNTANT: 'محاسب'
}

const roleColors = {
  ADMIN: 'bg-red-100 text-red-800',
  MANAGER: 'bg-blue-100 text-blue-800',
  ACCOUNTANT: 'bg-green-100 text-green-800'
}

export default function UsersPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('ALL')

  // Check if user is admin
  useEffect(() => {
    if (session && session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }
  }, [session, router])

  useEffect(() => {
    // Simulate API call - replace with actual API call
    setTimeout(() => {
      setUsers([
        {
          id: '1',
          name: 'أحمد محمد',
          email: '<EMAIL>',
          role: 'ADMIN',
          createdAt: '2024-01-01',
          lastLogin: '2024-02-15'
        },
        {
          id: '2',
          name: 'فاطمة علي',
          email: '<EMAIL>',
          role: 'MANAGER',
          createdAt: '2024-01-10',
          lastLogin: '2024-02-14'
        },
        {
          id: '3',
          name: 'محمد سالم',
          email: '<EMAIL>',
          role: 'ACCOUNTANT',
          createdAt: '2024-01-15',
          lastLogin: '2024-02-13'
        },
        {
          id: '4',
          name: 'نورا أحمد',
          email: '<EMAIL>',
          role: 'ACCOUNTANT',
          createdAt: '2024-02-01',
          lastLogin: '2024-02-12'
        }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRole = roleFilter === 'ALL' || user.role === roleFilter
    return matchesSearch && matchesRole
  })

  const handleDeleteUser = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      setUsers(users.filter(user => user.id !== id))
    }
  }

  const getUsersByRole = (role: string) => {
    return users.filter(user => user.role === role).length
  }

  // Don't render if not admin
  if (session && session.user.role !== 'ADMIN') {
    return null
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة المستخدمين</h1>
            <p className="mt-2 text-gray-600">إدارة حسابات المستخدمين وصلاحياتهم</p>
          </div>
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
            <Plus className="h-5 w-5" />
            إضافة مستخدم جديد
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-500 p-3 rounded-lg">
                <User className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
                <p className="text-2xl font-bold text-blue-600">{users.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-red-500 p-3 rounded-lg">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">المشرفين</p>
                <p className="text-2xl font-bold text-red-600">{getUsersByRole('ADMIN')}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-500 p-3 rounded-lg">
                <UserCheck className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">المديرين</p>
                <p className="text-2xl font-bold text-blue-600">{getUsersByRole('MANAGER')}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-500 p-3 rounded-lg">
                <User className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">المحاسبين</p>
                <p className="text-2xl font-bold text-green-600">{getUsersByRole('ACCOUNTANT')}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="البحث عن المستخدمين..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
            >
              <option value="ALL">جميع الأدوار</option>
              <option value="ADMIN">مشرف</option>
              <option value="MANAGER">مدير</option>
              <option value="ACCOUNTANT">محاسب</option>
            </select>
          </div>
        </div>

        {/* Users Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المستخدم
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    البريد الإلكتروني
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الدور
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    تاريخ الإنشاء
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    آخر دخول
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                          <User className="h-5 w-5 text-gray-600" />
                        </div>
                        <div className="mr-4">
                          <div className="text-sm font-medium text-gray-900">
                            {user.name}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{user.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${roleColors[user.role]}`}>
                        {roleLabels[user.role]}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {new Date(user.createdAt).toLocaleDateString('ar-SA')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {user.lastLogin 
                          ? new Date(user.lastLogin).toLocaleDateString('ar-SA')
                          : 'لم يسجل دخول بعد'
                        }
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <button className="text-blue-600 hover:text-blue-900">
                          <Edit className="h-4 w-4" />
                        </button>
                        {user.id !== session?.user?.id && (
                          <button 
                            onClick={() => handleDeleteUser(user.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredUsers.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد مستخدمين مطابقين لبحثك</p>
            </div>
          )}
        </div>

        {/* Permissions Info */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">صلاحيات الأدوار</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="border border-red-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Shield className="h-5 w-5 text-red-600 ml-2" />
                <h4 className="font-medium text-red-800">مشرف</h4>
              </div>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• إدارة جميع المستخدمين</li>
                <li>• الوصول لجميع التقارير</li>
                <li>• إدارة الإعدادات العامة</li>
                <li>• جميع صلاحيات المدير</li>
              </ul>
            </div>
            
            <div className="border border-blue-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <UserCheck className="h-5 w-5 text-blue-600 ml-2" />
                <h4 className="font-medium text-blue-800">مدير</h4>
              </div>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• إدارة العملاء والفواتير</li>
                <li>• عرض التقارير المالية</li>
                <li>• إدارة المنتجات والخدمات</li>
                <li>• جميع صلاحيات المحاسب</li>
              </ul>
            </div>
            
            <div className="border border-green-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <User className="h-5 w-5 text-green-600 ml-2" />
                <h4 className="font-medium text-green-800">محاسب</h4>
              </div>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• إنشاء وتعديل الفواتير</li>
                <li>• إدارة المصروفات</li>
                <li>• عرض التقارير الأساسية</li>
                <li>• إدارة بيانات العملاء</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
