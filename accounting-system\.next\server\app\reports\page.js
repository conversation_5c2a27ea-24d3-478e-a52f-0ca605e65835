/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/reports/page";
exports.ids = ["app/reports/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Freports%2Fpage&page=%2Freports%2Fpage&appPaths=%2Freports%2Fpage&pagePath=private-next-app-dir%2Freports%2Fpage.tsx&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Freports%2Fpage&page=%2Freports%2Fpage&appPaths=%2Freports%2Fpage&pagePath=private-next-app-dir%2Freports%2Fpage.tsx&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/reports/page.tsx */ \"(rsc)/./src/app/reports/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'reports',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/reports/page\",\n        pathname: \"/reports\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Freports%2Fpage&page=%2Freports%2Fpage&appPaths=%2Freports%2Fpage&pagePath=private-next-app-dir%2Freports%2Fpage.tsx&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers/SessionProvider.tsx */ \"(rsc)/./src/components/Providers/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0FjY291bnRpbmclNUMlNUNhY2NvdW50aW5nLXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDUHJvdmlkZXJzJTVDJTVDU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdNQUFvSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXEFjY291bnRpbmdcXFxcYWNjb3VudGluZy1zeXN0ZW1cXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcUHJvdmlkZXJzXFxcXFNlc3Npb25Qcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Creports%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Creports%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/reports/page.tsx */ \"(rsc)/./src/app/reports/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNyZXBvcnRzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdLQUFtRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQWNjb3VudGluZ1xcXFxhY2NvdW50aW5nLXN5c3RlbVxcXFxzcmNcXFxcYXBwXFxcXHJlcG9ydHNcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Creports%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxBY2NvdW50aW5nXFxhY2NvdW50aW5nLXN5c3RlbVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcQWNjb3VudGluZ1xcYWNjb3VudGluZy1zeXN0ZW1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Providers_SessionProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Providers/SessionProvider */ \"(rsc)/./src/components/Providers/SessionProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"نظام المحاسبة الاحترافي\",\n    description: \"نظام محاسبة شامل للشركات الصغيرة والمتوسطة\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers_SessionProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGlCO0FBQzhDO0FBSTlELE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsS0FBSTtrQkFDbEIsNEVBQUNDO1lBQUtDLFdBQVdYLCtKQUFlO3NCQUM5Qiw0RUFBQ0MsNkVBQWVBOzBCQUNiSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJEOlxcQWNjb3VudGluZ1xcYWNjb3VudGluZy1zeXN0ZW1cXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuaW1wb3J0IFNlc3Npb25Qcm92aWRlciBmcm9tIFwiQC9jb21wb25lbnRzL1Byb3ZpZGVycy9TZXNzaW9uUHJvdmlkZXJcIjtcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFtcImxhdGluXCJdIH0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCLZhti42KfZhSDYp9mE2YXYrdin2LPYqNipINin2YTYp9it2KrYsdin2YHZilwiLFxuICBkZXNjcmlwdGlvbjogXCLZhti42KfZhSDZhdit2KfYs9io2Kkg2LTYp9mF2YQg2YTZhNi02LHZg9in2Kog2KfZhNi12LrZitix2Kkg2YjYp9mE2YXYqtmI2LPYt9ipXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJhclwiIGRpcj1cInJ0bFwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8U2Vzc2lvblByb3ZpZGVyPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9TZXNzaW9uUHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwiU2Vzc2lvblByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJkaXIiLCJib2R5IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/reports/page.tsx":
/*!**********************************!*\
  !*** ./src/app/reports/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Accounting\\accounting-system\\src\\app\\reports\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/Providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Providers\\\\SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Accounting\\accounting-system\\src\\components\\Providers\\SessionProvider.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDQWNjb3VudGluZyU1QyU1Q2FjY291bnRpbmctc3lzdGVtJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0FjY291bnRpbmclNUMlNUNhY2NvdW50aW5nLXN5c3RlbSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBb0k7QUFDcEk7QUFDQSwwT0FBdUk7QUFDdkk7QUFDQSwwT0FBdUk7QUFDdkk7QUFDQSxvUkFBNko7QUFDN0o7QUFDQSx3T0FBc0k7QUFDdEk7QUFDQSw0UEFBaUo7QUFDako7QUFDQSxrUUFBb0o7QUFDcEo7QUFDQSxzUUFBcUoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEFjY291bnRpbmdcXFxcYWNjb3VudGluZy1zeXN0ZW1cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQWNjb3VudGluZ1xcXFxhY2NvdW50aW5nLXN5c3RlbVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxBY2NvdW50aW5nXFxcXGFjY291bnRpbmctc3lzdGVtXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEFjY291bnRpbmdcXFxcYWNjb3VudGluZy1zeXN0ZW1cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxodHRwLWFjY2Vzcy1mYWxsYmFja1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQWNjb3VudGluZ1xcXFxhY2NvdW50aW5nLXN5c3RlbVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEFjY291bnRpbmdcXFxcYWNjb3VudGluZy1zeXN0ZW1cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQWNjb3VudGluZ1xcXFxhY2NvdW50aW5nLXN5c3RlbVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG1ldGFkYXRhXFxcXG1ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxBY2NvdW50aW5nXFxcXGFjY291bnRpbmctc3lzdGVtXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers/SessionProvider.tsx */ \"(ssr)/./src/components/Providers/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0FjY291bnRpbmclNUMlNUNhY2NvdW50aW5nLXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDUHJvdmlkZXJzJTVDJTVDU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdNQUFvSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXEFjY291bnRpbmdcXFxcYWNjb3VudGluZy1zeXN0ZW1cXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcUHJvdmlkZXJzXFxcXFNlc3Npb25Qcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Creports%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Creports%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/reports/page.tsx */ \"(ssr)/./src/app/reports/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNyZXBvcnRzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdLQUFtRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQWNjb3VudGluZ1xcXFxhY2NvdW50aW5nLXN5c3RlbVxcXFxzcmNcXFxcYXBwXFxcXHJlcG9ydHNcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Creports%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/reports/page.tsx":
/*!**********************************!*\
  !*** ./src/app/reports/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReportsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(ssr)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_DollarSign_Download_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,DollarSign,Download,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_DollarSign_Download_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,DollarSign,Download,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_DollarSign_Download_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,DollarSign,Download,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_DollarSign_Download_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,DollarSign,Download,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_DollarSign_Download_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,DollarSign,Download,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ReportsPage() {\n    const [reportData, setReportData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        revenue: [],\n        expenses: [],\n        profit: [],\n        months: []\n    });\n    const [summaryStats, setSummaryStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalRevenue: 0,\n        totalExpenses: 0,\n        netProfit: 0,\n        profitMargin: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('THIS_YEAR');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReportsPage.useEffect\": ()=>{\n            // Simulate API call - replace with actual API call\n            setTimeout({\n                \"ReportsPage.useEffect\": ()=>{\n                    const months = [\n                        'يناير',\n                        'فبراير',\n                        'مارس',\n                        'أبريل',\n                        'مايو',\n                        'يونيو'\n                    ];\n                    const revenue = [\n                        45000,\n                        52000,\n                        48000,\n                        61000,\n                        55000,\n                        67000\n                    ];\n                    const expenses = [\n                        25000,\n                        28000,\n                        22000,\n                        35000,\n                        30000,\n                        38000\n                    ];\n                    const profit = revenue.map({\n                        \"ReportsPage.useEffect.profit\": (rev, index)=>rev - expenses[index]\n                    }[\"ReportsPage.useEffect.profit\"]);\n                    setReportData({\n                        revenue,\n                        expenses,\n                        profit,\n                        months\n                    });\n                    const totalRevenue = revenue.reduce({\n                        \"ReportsPage.useEffect.totalRevenue\": (sum, val)=>sum + val\n                    }[\"ReportsPage.useEffect.totalRevenue\"], 0);\n                    const totalExpenses = expenses.reduce({\n                        \"ReportsPage.useEffect.totalExpenses\": (sum, val)=>sum + val\n                    }[\"ReportsPage.useEffect.totalExpenses\"], 0);\n                    const netProfit = totalRevenue - totalExpenses;\n                    const profitMargin = totalRevenue > 0 ? netProfit / totalRevenue * 100 : 0;\n                    setSummaryStats({\n                        totalRevenue,\n                        totalExpenses,\n                        netProfit,\n                        profitMargin\n                    });\n                    setLoading(false);\n                }\n            }[\"ReportsPage.useEffect\"], 1000);\n        }\n    }[\"ReportsPage.useEffect\"], [\n        selectedPeriod\n    ]);\n    const generateReport = (type)=>{\n        alert(`سيتم تحميل تقرير ${type} قريباً`);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"التقارير المالية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-gray-600\",\n                                    children: \"تحليل الأداء المالي والتقارير الدورية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                value: selectedPeriod,\n                                onChange: (e)=>setSelectedPeriod(e.target.value),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"THIS_MONTH\",\n                                        children: \"هذا الشهر\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"LAST_MONTH\",\n                                        children: \"الشهر الماضي\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"THIS_QUARTER\",\n                                        children: \"هذا الربع\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"THIS_YEAR\",\n                                        children: \"هذا العام\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"LAST_YEAR\",\n                                        children: \"العام الماضي\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_DollarSign_Download_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"إجمالي الإيرادات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: [\n                                                    summaryStats.totalRevenue.toLocaleString(),\n                                                    \" ر.س\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_DollarSign_Download_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"إجمالي المصروفات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-red-600\",\n                                                children: [\n                                                    summaryStats.totalExpenses.toLocaleString(),\n                                                    \" ر.س\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `${summaryStats.netProfit >= 0 ? 'bg-blue-500' : 'bg-red-500'} p-3 rounded-lg`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_DollarSign_Download_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"صافي الربح\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: `text-2xl font-bold ${summaryStats.netProfit >= 0 ? 'text-blue-600' : 'text-red-600'}`,\n                                                children: [\n                                                    summaryStats.netProfit.toLocaleString(),\n                                                    \" ر.س\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_DollarSign_Download_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"هامش الربح\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-purple-600\",\n                                                children: [\n                                                    summaryStats.profitMargin.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                    children: \"الإيرادات مقابل المصروفات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-64 flex items-end justify-between space-x-2\",\n                                    children: reportData.months.map((month, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center space-y-2 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-1 w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-500 w-full rounded-t\",\n                                                            style: {\n                                                                height: `${reportData.revenue[index] / Math.max(...reportData.revenue) * 200}px`,\n                                                                minHeight: '10px'\n                                                            },\n                                                            title: `إيرادات: ${reportData.revenue[index].toLocaleString()} ر.س`\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-red-500 w-full rounded-b\",\n                                                            style: {\n                                                                height: `${reportData.expenses[index] / Math.max(...reportData.revenue) * 200}px`,\n                                                                minHeight: '10px'\n                                                            },\n                                                            title: `مصروفات: ${reportData.expenses[index].toLocaleString()} ر.س`\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-600 transform -rotate-45\",\n                                                    children: month\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, month, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mt-4 space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 bg-green-500 rounded mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"الإيرادات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 bg-red-500 rounded mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"المصروفات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                    children: \"اتجاه الأرباح\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-64 flex items-end justify-between space-x-2\",\n                                    children: reportData.months.map((month, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center space-y-2 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-full rounded ${reportData.profit[index] >= 0 ? 'bg-blue-500' : 'bg-red-500'}`,\n                                                    style: {\n                                                        height: `${Math.abs(reportData.profit[index]) / Math.max(...reportData.profit.map(Math.abs)) * 200}px`,\n                                                        minHeight: '10px'\n                                                    },\n                                                    title: `ربح: ${reportData.profit[index].toLocaleString()} ر.س`\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-600 transform -rotate-45\",\n                                                    children: month\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, month, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900\",\n                                children: \"التفصيل الشهري\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"الشهر\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"الإيرادات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"المصروفات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"صافي الربح\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"هامش الربح\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: reportData.months.map((month, index)=>{\n                                            const profit = reportData.profit[index];\n                                            const margin = reportData.revenue[index] > 0 ? profit / reportData.revenue[index] * 100 : 0;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                        children: month\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium\",\n                                                        children: [\n                                                            reportData.revenue[index].toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium\",\n                                                        children: [\n                                                            reportData.expenses[index].toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: `px-6 py-4 whitespace-nowrap text-sm font-medium ${profit >= 0 ? 'text-blue-600' : 'text-red-600'}`,\n                                                        children: [\n                                                            profit.toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: `px-6 py-4 whitespace-nowrap text-sm font-medium ${margin >= 0 ? 'text-blue-600' : 'text-red-600'}`,\n                                                        children: [\n                                                            margin.toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, month, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                            children: \"تحميل التقارير\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>generateReport('الأرباح والخسائر'),\n                                    className: \"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_DollarSign_Download_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5 ml-2 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"تقرير الأرباح والخسائر\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>generateReport('التدفق النقدي'),\n                                    className: \"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_DollarSign_Download_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5 ml-2 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"تقرير التدفق النقدي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>generateReport('الميزانية العمومية'),\n                                    className: \"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_DollarSign_Download_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5 ml-2 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"الميزانية العمومية\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\reports\\\\page.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/reports/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/MainLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/Layout/MainLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MainLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/Layout/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction MainLayout({ children }) {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"MainLayout.useEffect\": ()=>{\n            if (status === 'loading') return; // Still loading\n            if (!session) {\n                router.push('/auth/signin');\n                return;\n            }\n        }\n    }[\"MainLayout.useEffect\"], [\n        session,\n        status,\n        router\n    ]);\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-100\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/MainLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/Layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst navigation = [\n    {\n        name: 'لوحة التحكم',\n        href: '/',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: 'العملاء',\n        href: '/customers',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'الفواتير',\n        href: '/invoices',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'المنتجات',\n        href: '/products',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'المصروفات',\n        href: '/expenses',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'البنوك',\n        href: '/banking',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'الخزينة',\n        href: '/treasury',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: 'الأصول',\n        href: '/assets',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: 'الموارد البشرية',\n        href: '/hr',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: 'التقارير',\n        href: '/reports',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        name: 'المستخدمين',\n        href: '/users',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        adminOnly: true\n    },\n    {\n        name: 'الإعدادات',\n        href: '/settings',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const handleSignOut = ()=>{\n        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)({\n            callbackUrl: '/auth/signin'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full w-64 flex-col bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center justify-center bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-bold text-white\",\n                    children: \"نظام المحاسبة\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 space-y-1 px-2 py-4\",\n                children: navigation.map((item)=>{\n                    // Hide admin-only items for non-admin users\n                    if (item.adminOnly && session?.user?.role !== 'ADMIN') {\n                        return null;\n                    }\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: item.href,\n                        className: `\n                group flex items-center px-2 py-2 text-sm font-medium rounded-md\n                ${isActive ? 'bg-gray-800 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'}\n              `,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: `\n                  mr-3 h-5 w-5 flex-shrink-0\n                  ${isActive ? 'text-white' : 'text-gray-400 group-hover:text-white'}\n                `\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 15\n                            }, this),\n                            item.name\n                        ]\n                    }, item.name, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-700 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-full bg-gray-600 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-white\",\n                                        children: session?.user?.name || session?.user?.email\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: session?.user?.role === 'ADMIN' ? 'مشرف' : session?.user?.role === 'MANAGER' ? 'مدير' : 'محاسب'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSignOut,\n                        className: \"mt-3 w-full flex items-center px-2 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-gray-700 hover:text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"mr-3 h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            \"تسجيل الخروج\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/Providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Providers\\\\SessionProvider.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Qcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNEU7QUFNN0QsU0FBU0EsZ0JBQWdCLEVBQUVFLFFBQVEsRUFBd0I7SUFDeEUscUJBQ0UsOERBQUNELDREQUF1QkE7a0JBQ3JCQzs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkQ6XFxBY2NvdW50aW5nXFxhY2NvdW50aW5nLXN5c3RlbVxcc3JjXFxjb21wb25lbnRzXFxQcm92aWRlcnNcXFNlc3Npb25Qcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciBhcyBOZXh0QXV0aFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcblxuaW50ZXJmYWNlIFNlc3Npb25Qcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTZXNzaW9uUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiBTZXNzaW9uUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxOZXh0QXV0aFNlc3Npb25Qcm92aWRlcj5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L05leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Providers/SessionProvider.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Freports%2Fpage&page=%2Freports%2Fpage&appPaths=%2Freports%2Fpage&pagePath=private-next-app-dir%2Freports%2Fpage.tsx&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();