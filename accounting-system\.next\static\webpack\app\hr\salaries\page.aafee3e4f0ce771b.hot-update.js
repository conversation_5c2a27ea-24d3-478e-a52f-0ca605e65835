"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/hr/salaries/page",{

/***/ "(app-pages-browser)/./src/components/Layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/Layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst navigation = [\n    {\n        name: 'لوحة التحكم',\n        href: '/',\n        icon: _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: 'العملاء',\n        href: '/customers',\n        icon: _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'الفواتير',\n        href: '/invoices',\n        icon: _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'المنتجات',\n        href: '/products',\n        icon: _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'المصروفات',\n        href: '/expenses',\n        icon: _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'الموارد البشرية',\n        href: '/hr',\n        icon: _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'التقارير',\n        href: '/reports',\n        icon: _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: 'المستخدمين',\n        href: '/users',\n        icon: _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        adminOnly: true\n    },\n    {\n        name: 'الإعدادات',\n        href: '/settings',\n        icon: _barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    var _session_user, _session_user1, _session_user2, _session_user3;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const handleSignOut = ()=>{\n        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)({\n            callbackUrl: '/auth/signin'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full w-64 flex-col bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center justify-center bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-bold text-white\",\n                    children: \"نظام المحاسبة\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 space-y-1 px-2 py-4\",\n                children: navigation.map((item)=>{\n                    var _session_user;\n                    // Hide admin-only items for non-admin users\n                    if (item.adminOnly && (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) !== 'ADMIN') {\n                        return null;\n                    }\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: item.href,\n                        className: \"\\n                group flex items-center px-2 py-2 text-sm font-medium rounded-md\\n                \".concat(isActive ? 'bg-gray-800 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white', \"\\n              \"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"\\n                  mr-3 h-5 w-5 flex-shrink-0\\n                  \".concat(isActive ? 'text-white' : 'text-gray-400 group-hover:text-white', \"\\n                \")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 15\n                            }, this),\n                            item.name\n                        ]\n                    }, item.name, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-700 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-full bg-gray-600 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-white\",\n                                        children: (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name) || (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.email)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.role) === 'ADMIN' ? 'مشرف' : (session === null || session === void 0 ? void 0 : (_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : _session_user3.role) === 'MANAGER' ? 'مدير' : 'محاسب'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSignOut,\n                        className: \"mt-3 w-full flex items-center px-2 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-gray-700 hover:text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"mr-3 h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            \"تسجيل الخروج\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"vU8JzSd4L4Kl4BSMCCU4jwFXDNQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Layout/Sidebar.tsx\n"));

/***/ })

});