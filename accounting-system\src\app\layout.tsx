import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import SessionProvider from "@/components/Providers/SessionProvider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "نظام المحاسبة الاحترافي",
  description: "نظام محاسبة شامل للشركات الصغيرة والمتوسطة",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body className={inter.className}>
        <SessionProvider>
          {children}
        </SessionProvider>
      </body>
    </html>
  );
}
