// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication and authorization
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      UserRole @default(ACCOUNTANT)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  invoices Invoice[]
  expenses Expense[]

  @@map("users")
}

enum UserRole {
  ADMIN
  MANAGER
  ACCOUNTANT
}

// Customer model
model Customer {
  id        String   @id @default(cuid())
  name      String
  email     String?
  phone     String?
  address   String?
  taxNumber String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  invoices Invoice[]

  @@map("customers")
}

// Product/Service model
model Product {
  id          String   @id @default(cuid())
  name        String
  description String?
  price       Decimal  @db.Decimal(10, 2)
  unit        String? // e.g., "piece", "hour", "kg"
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  invoiceItems InvoiceItem[]

  @@map("products")
}

// Invoice model
model Invoice {
  id         String        @id @default(cuid())
  number     String        @unique
  customerId String
  userId     String
  issueDate  DateTime      @default(now())
  dueDate    DateTime
  status     InvoiceStatus @default(DRAFT)
  subtotal   Decimal       @db.Decimal(10, 2)
  taxAmount  Decimal       @default(0) @db.Decimal(10, 2)
  total      Decimal       @db.Decimal(10, 2)
  notes      String?
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt

  // Relations
  customer Customer      @relation(fields: [customerId], references: [id])
  user     User          @relation(fields: [userId], references: [id])
  items    InvoiceItem[]

  @@map("invoices")
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
}

// Invoice Item model
model InvoiceItem {
  id        String  @id @default(cuid())
  invoiceId String
  productId String
  quantity  Decimal @db.Decimal(10, 2)
  price     Decimal @db.Decimal(10, 2)
  total     Decimal @db.Decimal(10, 2)

  // Relations
  invoice Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("invoice_items")
}

// Expense Category model
model ExpenseCategory {
  id        String   @id @default(cuid())
  name      String   @unique
  color     String? // For UI display
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  expenses Expense[]

  @@map("expense_categories")
}

// Expense model
model Expense {
  id          String   @id @default(cuid())
  title       String
  description String?
  amount      Decimal  @db.Decimal(10, 2)
  date        DateTime @default(now())
  categoryId  String
  userId      String
  receipt     String? // File path for receipt image
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  category ExpenseCategory @relation(fields: [categoryId], references: [id])
  user     User            @relation(fields: [userId], references: [id])

  @@map("expenses")
}
