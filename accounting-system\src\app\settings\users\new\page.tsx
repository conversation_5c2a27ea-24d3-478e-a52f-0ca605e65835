'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import MainLayout from '@/components/Layout/MainLayout'
import { 
  ArrowLeft, 
  Save, 
  User, 
  Mail, 
  Lock, 
  Shield, 
  Eye, 
  EyeOff,
  UserPlus,
  AlertCircle,
  Check
} from 'lucide-react'

interface FormData {
  name: string
  email: string
  password: string
  confirmPassword: string
  role: string
  isActive: boolean
  permissions: string[]
}

export default function NewUserPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [success, setSuccess] = useState(false)

  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'ACCOUNTANT',
    isActive: true,
    permissions: []
  })

  const roles = [
    { id: 'ADMIN', name: 'مدير النظام', description: 'صلاحيات كاملة لجميع أجزاء النظام' },
    { id: 'MANAGER', name: 'مدير', description: 'صلاحيات إدارية للمحاسبة والموارد البشرية' },
    { id: 'ACCOUNTANT', name: 'محاسب', description: 'صلاحيات المحاسبة والتقارير المالية' },
    { id: 'HR_SPECIALIST', name: 'أخصائي موارد بشرية', description: 'صلاحيات الموارد البشرية والرواتب' }
  ]

  const permissions = [
    // Dashboard
    { id: 'dashboard_view', name: 'عرض لوحة التحكم', module: 'dashboard' },
    
    // Accounting
    { id: 'invoices_view', name: 'عرض الفواتير', module: 'accounting' },
    { id: 'invoices_create', name: 'إنشاء الفواتير', module: 'accounting' },
    { id: 'invoices_edit', name: 'تعديل الفواتير', module: 'accounting' },
    { id: 'invoices_delete', name: 'حذف الفواتير', module: 'accounting' },
    { id: 'expenses_view', name: 'عرض المصروفات', module: 'accounting' },
    { id: 'expenses_create', name: 'إنشاء المصروفات', module: 'accounting' },
    { id: 'expenses_edit', name: 'تعديل المصروفات', module: 'accounting' },
    { id: 'expenses_delete', name: 'حذف المصروفات', module: 'accounting' },
    
    // HR
    { id: 'hr_view', name: 'عرض الموارد البشرية', module: 'hr' },
    { id: 'hr_create', name: 'إضافة موظفين', module: 'hr' },
    { id: 'hr_edit', name: 'تعديل بيانات الموظفين', module: 'hr' },
    { id: 'hr_delete', name: 'حذف الموظفين', module: 'hr' },
    { id: 'payroll_view', name: 'عرض الرواتب', module: 'hr' },
    { id: 'payroll_process', name: 'معالجة الرواتب', module: 'hr' },
    
    // Banking
    { id: 'banking_view', name: 'عرض البنوك', module: 'banking' },
    { id: 'banking_create', name: 'إضافة حسابات بنكية', module: 'banking' },
    { id: 'banking_edit', name: 'تعديل الحسابات البنكية', module: 'banking' },
    { id: 'banking_delete', name: 'حذف الحسابات البنكية', module: 'banking' },
    
    // Treasury
    { id: 'treasury_view', name: 'عرض الخزينة', module: 'treasury' },
    { id: 'treasury_create', name: 'إضافة صناديق نقدية', module: 'treasury' },
    { id: 'treasury_edit', name: 'تعديل الصناديق النقدية', module: 'treasury' },
    { id: 'treasury_delete', name: 'حذف الصناديق النقدية', module: 'treasury' },
    
    // Assets
    { id: 'assets_view', name: 'عرض الأصول', module: 'assets' },
    { id: 'assets_create', name: 'إضافة أصول', module: 'assets' },
    { id: 'assets_edit', name: 'تعديل الأصول', module: 'assets' },
    { id: 'assets_delete', name: 'حذف الأصول', module: 'assets' },
    
    // Settings
    { id: 'settings_view', name: 'عرض الإعدادات', module: 'settings' },
    { id: 'settings_edit', name: 'تعديل الإعدادات', module: 'settings' },
    { id: 'users_manage', name: 'إدارة المستخدمين', module: 'settings' },
    
    // Reports
    { id: 'reports_view', name: 'عرض التقارير', module: 'reports' },
    { id: 'reports_export', name: 'تصدير التقارير', module: 'reports' }
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const handlePermissionToggle = (permissionId: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permissionId)
        ? prev.permissions.filter(p => p !== permissionId)
        : [...prev.permissions, permissionId]
    }))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'الاسم مطلوب'
    }

    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح'
    }

    if (!formData.password) {
      newErrors.password = 'كلمة المرور مطلوبة'
    } else if (formData.password.length < 6) {
      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'تأكيد كلمة المرور مطلوب'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'كلمة المرور غير متطابقة'
    }

    if (!formData.role) {
      newErrors.role = 'الدور مطلوب'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    setErrors({})

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          email: formData.email.trim().toLowerCase(),
          password: formData.password,
          role: formData.role,
          isActive: formData.isActive,
          permissions: formData.permissions
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        if (data.error === 'Email already exists') {
          setErrors({ email: 'البريد الإلكتروني مستخدم بالفعل' })
        } else {
          setErrors({ general: data.error || 'حدث خطأ أثناء إنشاء المستخدم' })
        }
        return
      }

      setSuccess(true)
      setTimeout(() => {
        router.push('/settings?tab=users')
      }, 2000)

    } catch (error) {
      console.error('Error creating user:', error)
      setErrors({ general: 'حدث خطأ في الاتصال' })
    } finally {
      setLoading(false)
    }
  }

  const getModuleColor = (module: string) => {
    switch (module) {
      case 'dashboard': return 'bg-blue-100 text-blue-800'
      case 'accounting': return 'bg-green-100 text-green-800'
      case 'hr': return 'bg-purple-100 text-purple-800'
      case 'banking': return 'bg-indigo-100 text-indigo-800'
      case 'treasury': return 'bg-yellow-100 text-yellow-800'
      case 'assets': return 'bg-orange-100 text-orange-800'
      case 'settings': return 'bg-gray-100 text-gray-800'
      case 'reports': return 'bg-pink-100 text-pink-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (success) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
              <Check className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">تم إنشاء المستخدم بنجاح!</h3>
            <p className="text-sm text-gray-500 mb-4">سيتم توجيهك إلى صفحة الإعدادات...</p>
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.push('/settings?tab=users')}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5" />
            العودة للإعدادات
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إضافة مستخدم جديد</h1>
            <p className="mt-2 text-gray-600">إنشاء حساب مستخدم جديد في النظام</p>
          </div>
        </div>

        {errors.general && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="mr-3">
                <p className="text-sm text-red-800">{errors.general}</p>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-6 flex items-center gap-2">
              <UserPlus className="h-5 w-5 text-blue-600" />
              معلومات المستخدم الأساسية
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <User className="h-4 w-4 inline ml-1" />
                  الاسم الكامل *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="أدخل الاسم الكامل"
                />
                {errors.name && <p className="text-red-600 text-sm mt-1">{errors.name}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Mail className="h-4 w-4 inline ml-1" />
                  البريد الإلكتروني *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.email ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="<EMAIL>"
                />
                {errors.email && <p className="text-red-600 text-sm mt-1">{errors.email}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Lock className="h-4 w-4 inline ml-1" />
                  كلمة المرور *
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.password ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="أدخل كلمة المرور"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                {errors.password && <p className="text-red-600 text-sm mt-1">{errors.password}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Lock className="h-4 w-4 inline ml-1" />
                  تأكيد كلمة المرور *
                </label>
                <div className="relative">
                  <input
                    type={showConfirmPassword ? 'text' : 'password'}
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.confirmPassword ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="أعد إدخال كلمة المرور"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                {errors.confirmPassword && <p className="text-red-600 text-sm mt-1">{errors.confirmPassword}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Shield className="h-4 w-4 inline ml-1" />
                  الدور *
                </label>
                <select
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.role ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  {roles.map((role) => (
                    <option key={role.id} value={role.id}>
                      {role.name}
                    </option>
                  ))}
                </select>
                {errors.role && <p className="text-red-600 text-sm mt-1">{errors.role}</p>}
                <p className="text-sm text-gray-500 mt-1">
                  {roles.find(r => r.id === formData.role)?.description}
                </p>
              </div>

              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="mr-2 text-sm text-gray-700">تفعيل المستخدم</span>
                </label>
                <p className="text-sm text-gray-500 mt-1">
                  المستخدمون المفعلون يمكنهم تسجيل الدخول واستخدام النظام
                </p>
              </div>
            </div>
          </div>

          {/* Additional Permissions */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-600" />
              صلاحيات إضافية (اختيارية)
            </h2>
            <p className="text-sm text-gray-600 mb-6">
              يمكنك إضافة صلاحيات إضافية للمستخدم بجانب الصلاحيات الأساسية للدور المحدد
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {permissions.map((permission) => (
                <div key={permission.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                  <div className="flex items-center gap-2">
                    <span className={`inline-flex px-2 py-1 text-xs rounded-full ${getModuleColor(permission.module)}`}>
                      {permission.module}
                    </span>
                    <span className="text-sm text-gray-700">{permission.name}</span>
                  </div>
                  <input
                    type="checkbox"
                    checked={formData.permissions.includes(permission.id)}
                    onChange={() => handlePermissionToggle(permission.id)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>
              ))}
            </div>

            {formData.permissions.length > 0 && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-800">
                  تم اختيار {formData.permissions.length} صلاحية إضافية
                </p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center">
            <button
              type="button"
              onClick={() => router.push('/settings?tab=users')}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              إلغاء
            </button>

            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  جاري الإنشاء...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  إنشاء المستخدم
                </>
              )}
            </button>
          </div>
        </form>

        {/* Help Section */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-yellow-900 mb-2">نصائح لإنشاء مستخدم جديد:</h3>
          <ul className="text-sm text-yellow-800 space-y-1">
            <li>• اختر دوراً مناسباً للمستخدم حسب مسؤولياته</li>
            <li>• استخدم كلمة مرور قوية تحتوي على أرقام وحروف</li>
            <li>• أضف صلاحيات إضافية فقط عند الحاجة</li>
            <li>• تأكد من صحة البريد الإلكتروني قبل الحفظ</li>
            <li>• يمكن تعديل الصلاحيات لاحقاً من إدارة الصلاحيات</li>
          </ul>
        </div>
      </div>
    </MainLayout>
  )
}
