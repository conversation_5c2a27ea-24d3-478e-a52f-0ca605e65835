'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import MainLayout from '@/components/Layout/MainLayout'
import { 
  Users, 
  Calendar, 
  DollarSign, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  TrendingUp,
  UserPlus,
  CalendarPlus,
  FileText,
  Award,
  Building
} from 'lucide-react'

interface HRStats {
  totalEmployees: number
  activeEmployees: number
  pendingLeaves: number
  pendingSalaries: number
  totalSalariesAmount: number
  departmentsCount: number
}

export default function HRDashboard() {
  const [stats, setStats] = useState<HRStats>({
    totalEmployees: 0,
    activeEmployees: 0,
    pendingLeaves: 0,
    pendingSalaries: 0,
    totalSalariesAmount: 0,
    departmentsCount: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/hr/dashboard')
        if (response.ok) {
          const data = await response.json()
          setStats(data)
        } else {
          // Fallback to demo data
          setStats({
            totalEmployees: 25,
            activeEmployees: 23,
            pendingLeaves: 5,
            pendingSalaries: 8,
            totalSalariesAmount: 185000,
            departmentsCount: 6
          })
        }
      } catch (error) {
        console.error('Error fetching HR stats:', error)
        // Use demo data as fallback
        setStats({
          totalEmployees: 25,
          activeEmployees: 23,
          pendingLeaves: 5,
          pendingSalaries: 8,
          totalSalariesAmount: 185000,
          departmentsCount: 6
        })
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  const quickActions = [
    {
      title: 'إضافة موظف جديد',
      description: 'تسجيل موظف جديد في النظام',
      icon: UserPlus,
      href: '/hr/employees/new',
      color: 'bg-blue-500'
    },
    {
      title: 'طلب إجازة',
      description: 'تقديم طلب إجازة جديد',
      icon: CalendarPlus,
      href: '/hr/leaves/new',
      color: 'bg-green-500'
    },
    {
      title: 'إنشاء رواتب الشهر',
      description: 'إنشاء رواتب جميع الموظفين للشهر الحالي',
      icon: DollarSign,
      href: '/hr/salaries/generate',
      color: 'bg-purple-500'
    },
    {
      title: 'تقارير الموارد البشرية',
      description: 'عرض تقارير شاملة عن الموظفين',
      icon: FileText,
      href: '/hr/reports',
      color: 'bg-orange-500'
    }
  ]

  const hrModules = [
    {
      title: 'إدارة الموظفين',
      description: 'إدارة بيانات الموظفين ومعلوماتهم الشخصية',
      icon: Users,
      href: '/hr/employees',
      stats: `${stats.totalEmployees} موظف`,
      color: 'border-blue-500'
    },
    {
      title: 'إدارة الإجازات',
      description: 'إدارة طلبات الإجازات والموافقة عليها',
      icon: Calendar,
      href: '/hr/leaves',
      stats: `${stats.pendingLeaves} طلب في الانتظار`,
      color: 'border-green-500'
    },
    {
      title: 'إدارة الرواتب',
      description: 'إدارة رواتب الموظفين وكشوف المرتبات',
      icon: DollarSign,
      href: '/hr/salaries',
      stats: `${stats.pendingSalaries} راتب في الانتظار`,
      color: 'border-purple-500'
    },
    {
      title: 'تقييم الأداء',
      description: 'تقييم أداء الموظفين والمراجعات الدورية',
      icon: Award,
      href: '/hr/performance',
      stats: 'قريباً',
      color: 'border-yellow-500'
    }
  ]

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">لوحة تحكم الموارد البشرية</h1>
          <p className="mt-2 text-gray-600">إدارة شاملة للموظفين والإجازات والرواتب</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-500 p-3 rounded-lg">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الموظفين</p>
                <p className="text-2xl font-bold text-blue-600">{stats.totalEmployees}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-500 p-3 rounded-lg">
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الموظفين النشطين</p>
                <p className="text-2xl font-bold text-green-600">{stats.activeEmployees}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-yellow-500 p-3 rounded-lg">
                <Clock className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجازات معلقة</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pendingLeaves}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-purple-500 p-3 rounded-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">رواتب معلقة</p>
                <p className="text-2xl font-bold text-purple-600">{stats.pendingSalaries}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-indigo-500 p-3 rounded-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الرواتب</p>
                <p className="text-xl font-bold text-indigo-600">
                  {stats.totalSalariesAmount.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-orange-500 p-3 rounded-lg">
                <Building className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الأقسام</p>
                <p className="text-2xl font-bold text-orange-600">{stats.departmentsCount}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">الإجراءات السريعة</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action, index) => (
              <Link
                key={index}
                href={action.href}
                className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow duration-200"
              >
                <div className="flex items-center mb-4">
                  <div className={`${action.color} p-3 rounded-lg`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{action.title}</h3>
                <p className="text-sm text-gray-600">{action.description}</p>
              </Link>
            ))}
          </div>
        </div>

        {/* HR Modules */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">وحدات الموارد البشرية</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {hrModules.map((module, index) => (
              <Link
                key={index}
                href={module.href}
                className={`bg-white rounded-lg shadow p-6 border-r-4 ${module.color} hover:shadow-lg transition-shadow duration-200`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    <module.icon className="h-8 w-8 text-gray-600 ml-4" />
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{module.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{module.description}</p>
                      <p className="text-sm font-medium text-blue-600 mt-2">{module.stats}</p>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Recent Activities */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">الأنشطة الأخيرة</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center">
                <div className="bg-green-100 p-2 rounded-full">
                  <UserPlus className="h-4 w-4 text-green-600" />
                </div>
                <div className="mr-3">
                  <p className="text-sm font-medium text-gray-900">تم إضافة موظف جديد</p>
                  <p className="text-sm text-gray-500">أحمد محمد - قسم تقنية المعلومات</p>
                </div>
                <div className="mr-auto text-sm text-gray-500">منذ ساعتين</div>
              </div>
              
              <div className="flex items-center">
                <div className="bg-blue-100 p-2 rounded-full">
                  <CheckCircle className="h-4 w-4 text-blue-600" />
                </div>
                <div className="mr-3">
                  <p className="text-sm font-medium text-gray-900">تم الموافقة على إجازة</p>
                  <p className="text-sm text-gray-500">فاطمة أحمد - إجازة سنوية</p>
                </div>
                <div className="mr-auto text-sm text-gray-500">منذ 4 ساعات</div>
              </div>
              
              <div className="flex items-center">
                <div className="bg-purple-100 p-2 rounded-full">
                  <DollarSign className="h-4 w-4 text-purple-600" />
                </div>
                <div className="mr-3">
                  <p className="text-sm font-medium text-gray-900">تم دفع راتب</p>
                  <p className="text-sm text-gray-500">خالد علي - راتب شهر مارس</p>
                </div>
                <div className="mr-auto text-sm text-gray-500">أمس</div>
              </div>
              
              <div className="flex items-center">
                <div className="bg-yellow-100 p-2 rounded-full">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                </div>
                <div className="mr-3">
                  <p className="text-sm font-medium text-gray-900">طلب إجازة جديد</p>
                  <p className="text-sm text-gray-500">سارة محمود - إجازة مرضية</p>
                </div>
                <div className="mr-auto text-sm text-gray-500">منذ يومين</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
