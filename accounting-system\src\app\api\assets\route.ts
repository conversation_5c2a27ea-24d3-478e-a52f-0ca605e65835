import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const assets = await prisma.asset.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(assets)
  } catch (error) {
    console.error('Error fetching assets:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await request.formData()
    
    // Extract form data
    const assetCode = formData.get('assetCode') as string
    const name = formData.get('name') as string
    const description = formData.get('description') as string
    const category = formData.get('category') as string
    const subCategory = formData.get('subCategory') as string
    const brand = formData.get('brand') as string
    const model = formData.get('model') as string
    const serialNumber = formData.get('serialNumber') as string
    const purchaseDate = formData.get('purchaseDate') as string
    const purchasePrice = formData.get('purchasePrice') as string
    const depreciationRate = formData.get('depreciationRate') as string
    const depreciationMethod = formData.get('depreciationMethod') as string
    const usefulLife = formData.get('usefulLife') as string
    const location = formData.get('location') as string
    const department = formData.get('department') as string
    const assignedTo = formData.get('assignedTo') as string
    const condition = formData.get('condition') as string
    const warrantyExpiry = formData.get('warrantyExpiry') as string
    const insuranceExpiry = formData.get('insuranceExpiry') as string
    const supplier = formData.get('supplier') as string
    const invoiceNumber = formData.get('invoiceNumber') as string
    const notes = formData.get('notes') as string
    const imageFile = formData.get('image') as File

    // Validate required fields
    if (!assetCode || !name || !category || !purchaseDate || !purchasePrice) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate purchase price
    const price = parseFloat(purchasePrice)
    if (price <= 0) {
      return NextResponse.json(
        { error: 'Purchase price must be greater than zero' },
        { status: 400 }
      )
    }

    // Handle image upload (simplified - in production, use cloud storage)
    let imagePath = null
    if (imageFile && imageFile.size > 0) {
      // For demo purposes, we'll just store the filename
      // In production, upload to cloud storage and store the URL
      imagePath = `assets/${Date.now()}-${imageFile.name}`
    }

    // Calculate current value based on depreciation
    const purchaseDateObj = new Date(purchaseDate)
    const now = new Date()
    const yearsElapsed = (now.getTime() - purchaseDateObj.getTime()) / (1000 * 60 * 60 * 24 * 365)
    
    let currentValue = price
    if (depreciationRate && parseFloat(depreciationRate) > 0) {
      const rate = parseFloat(depreciationRate) / 100
      if (depreciationMethod === 'STRAIGHT_LINE') {
        currentValue = Math.max(0, price - (price * rate * yearsElapsed))
      } else if (depreciationMethod === 'DECLINING_BALANCE') {
        currentValue = price * Math.pow(1 - rate, yearsElapsed)
      }
    }

    const asset = await prisma.asset.create({
      data: {
        assetCode,
        name,
        description: description || null,
        category,
        subCategory: subCategory || null,
        brand: brand || null,
        model: model || null,
        serialNumber: serialNumber || null,
        purchaseDate: new Date(purchaseDate),
        purchasePrice: price,
        currentValue: Math.round(currentValue * 100) / 100,
        depreciationRate: depreciationRate ? parseFloat(depreciationRate) : 0,
        depreciationMethod: depreciationMethod || 'STRAIGHT_LINE',
        usefulLife: usefulLife ? parseInt(usefulLife) : 5,
        location: location || null,
        department: department || null,
        assignedTo: assignedTo || null,
        condition: condition || 'GOOD',
        status: 'ACTIVE',
        warrantyExpiry: warrantyExpiry ? new Date(warrantyExpiry) : null,
        insuranceExpiry: insuranceExpiry ? new Date(insuranceExpiry) : null,
        supplier: supplier || null,
        invoiceNumber: invoiceNumber || null,
        notes: notes || null,
        image: imagePath
      }
    })

    return NextResponse.json(asset, { status: 201 })
  } catch (error) {
    console.error('Error creating asset:', error)
    
    // Handle unique constraint violations
    if (error instanceof Error && error.message.includes('Unique constraint')) {
      return NextResponse.json(
        { error: 'Asset code already exists' },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
