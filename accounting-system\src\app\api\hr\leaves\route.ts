import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const leaves = await prisma.leave.findMany({
      include: {
        employee: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Map to include employee name
    const leavesWithEmployeeName = leaves.map(leave => ({
      ...leave,
      employeeName: `${leave.employee.firstName} ${leave.employee.lastName}`
    }))

    return NextResponse.json(leavesWithEmployeeName)
  } catch (error) {
    console.error('Error fetching leaves:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      employeeId,
      type,
      startDate,
      endDate,
      reason
    } = body

    // Validate required fields
    if (!employeeId || !type || !startDate || !endDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Calculate days
    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    const days = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1

    const leave = await prisma.leave.create({
      data: {
        employeeId,
        type,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        days,
        reason: reason || null,
        status: 'PENDING'
      },
      include: {
        employee: true
      }
    })

    return NextResponse.json({
      ...leave,
      employeeName: `${leave.employee.firstName} ${leave.employee.lastName}`
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating leave:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
