import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const transfers = await prisma.bankTransfer.findMany({
      orderBy: {
        transferDate: 'desc'
      }
    })

    // Get account names for each transfer
    const transfersWithAccountNames = await Promise.all(
      transfers.map(async (transfer) => {
        const fromAccount = await prisma.bankAccount.findUnique({
          where: { id: transfer.fromAccountId }
        })
        const toAccount = await prisma.bankAccount.findUnique({
          where: { id: transfer.toAccountId }
        })

        return {
          ...transfer,
          fromAccountName: fromAccount?.accountName || 'حساب محذوف',
          toAccountName: toAccount?.accountName || 'حساب محذوف'
        }
      })
    )

    return NextResponse.json(transfersWithAccountNames)
  } catch (error) {
    console.error('Error fetching bank transfers:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      fromAccountId,
      toAccountId,
      amount,
      description,
      reference,
      transferDate,
      fees = 0,
      exchangeRate = 1,
      notes
    } = body

    // Validate required fields
    if (!fromAccountId || !toAccountId || !amount || !description) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate amount
    if (amount <= 0) {
      return NextResponse.json(
        { error: 'Amount must be greater than zero' },
        { status: 400 }
      )
    }

    // Check if accounts exist
    const fromAccount = await prisma.bankAccount.findUnique({
      where: { id: fromAccountId }
    })
    const toAccount = await prisma.bankAccount.findUnique({
      where: { id: toAccountId }
    })

    if (!fromAccount || !toAccount) {
      return NextResponse.json(
        { error: 'One or both accounts not found' },
        { status: 404 }
      )
    }

    // Check if accounts are different
    if (fromAccountId === toAccountId) {
      return NextResponse.json(
        { error: 'Cannot transfer to the same account' },
        { status: 400 }
      )
    }

    // Check sufficient funds (including fees)
    const totalAmount = parseFloat(amount) + parseFloat(fees)
    if (fromAccount.balance < totalAmount && fromAccount.accountType !== 'CREDIT') {
      return NextResponse.json(
        { error: 'Insufficient funds' },
        { status: 400 }
      )
    }

    // Create transfer record
    const transfer = await prisma.bankTransfer.create({
      data: {
        fromAccountId,
        toAccountId,
        amount: parseFloat(amount),
        description,
        reference: reference || null,
        transferDate: transferDate ? new Date(transferDate) : new Date(),
        status: 'PENDING',
        fees: parseFloat(fees),
        exchangeRate: parseFloat(exchangeRate),
        notes: notes || null
      }
    })

    return NextResponse.json({
      ...transfer,
      fromAccountName: fromAccount.accountName,
      toAccountName: toAccount.accountName
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating bank transfer:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
