'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import MainLayout from '@/components/Layout/MainLayout'
import { Save, ArrowLeft, Upload } from 'lucide-react'

const expenseCategories = [
  'مصاريف إدارية',
  'مصاريف تشغيلية',
  'مصاريف تسويق',
  'مصاريف سفر',
  'مصاريف مكتبية',
  'مصاريف صيانة',
  'مصاريف اتصالات',
  'مصاريف مرافق',
  'مصاريف إيجار',
  'مصاريف رواتب',
  'مصاريف تدريب',
  'مصاريف قانونية',
  'مصاريف محاسبية',
  'مصاريف أخرى'
]

const paymentMethods = [
  'نقدي',
  'شيك',
  'تحويل بنكي',
  'بطاقة ائتمان',
  'بطاقة مدين'
]

export default function NewExpensePage() {
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [expense, setExpense] = useState({
    description: '',
    amount: '',
    category: '',
    date: new Date().toISOString().split('T')[0],
    paymentMethod: '',
    vendor: '',
    reference: '',
    notes: '',
    isRecurring: false,
    recurringPeriod: 'monthly'
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setExpense(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!expense.description || !expense.amount || !expense.category) {
      alert('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    if (parseFloat(expense.amount) <= 0) {
      alert('يجب أن يكون المبلغ أكبر من صفر')
      return
    }

    setSaving(true)

    try {
      const expenseData = {
        description: expense.description,
        amount: parseFloat(expense.amount),
        category: expense.category,
        date: expense.date,
        paymentMethod: expense.paymentMethod || null,
        vendor: expense.vendor || null,
        reference: expense.reference || null,
        notes: expense.notes || null,
        isRecurring: expense.isRecurring,
        recurringPeriod: expense.isRecurring ? expense.recurringPeriod : null
      }

      const response = await fetch('/api/expenses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(expenseData),
      })

      if (response.ok) {
        alert('تم إضافة المصروف بنجاح!')
        router.push('/expenses')
      } else {
        const error = await response.json()
        alert(`حدث خطأ: ${error.error || 'خطأ غير معروف'}`)
      }
    } catch (error) {
      console.error('Error saving expense:', error)
      alert('حدث خطأ أثناء حفظ المصروف')
    } finally {
      setSaving(false)
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.push('/expenses')}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5" />
            العودة للمصروفات
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إضافة مصروف جديد</h1>
            <p className="mt-2 text-gray-600">تسجيل مصروف جديد في النظام</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  وصف المصروف *
                </label>
                <input
                  type="text"
                  id="description"
                  name="description"
                  value={expense.description}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="أدخل وصف المصروف"
                  required
                />
              </div>

              <div>
                <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                  المبلغ (ر.س) *
                </label>
                <input
                  type="number"
                  id="amount"
                  name="amount"
                  value={expense.amount}
                  onChange={handleChange}
                  min="0"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="0.00"
                  required
                />
              </div>

              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                  التصنيف *
                </label>
                <select
                  id="category"
                  name="category"
                  value={expense.category}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="">اختر التصنيف</option>
                  {expenseCategories.map(category => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-2">
                  تاريخ المصروف *
                </label>
                <input
                  type="date"
                  id="date"
                  name="date"
                  value={expense.date}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700 mb-2">
                  طريقة الدفع
                </label>
                <select
                  id="paymentMethod"
                  name="paymentMethod"
                  value={expense.paymentMethod}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">اختر طريقة الدفع</option>
                  {paymentMethods.map(method => (
                    <option key={method} value={method}>
                      {method}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="vendor" className="block text-sm font-medium text-gray-700 mb-2">
                  المورد/الجهة
                </label>
                <input
                  type="text"
                  id="vendor"
                  name="vendor"
                  value={expense.vendor}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="اسم المورد أو الجهة"
                />
              </div>

              <div>
                <label htmlFor="reference" className="block text-sm font-medium text-gray-700 mb-2">
                  رقم المرجع/الفاتورة
                </label>
                <input
                  type="text"
                  id="reference"
                  name="reference"
                  value={expense.reference}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="رقم الفاتورة أو المرجع"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isRecurring"
                  name="isRecurring"
                  checked={expense.isRecurring}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="isRecurring" className="mr-2 block text-sm text-gray-900">
                  مصروف متكرر
                </label>
              </div>

              {expense.isRecurring && (
                <div>
                  <label htmlFor="recurringPeriod" className="block text-sm font-medium text-gray-700 mb-2">
                    فترة التكرار
                  </label>
                  <select
                    id="recurringPeriod"
                    name="recurringPeriod"
                    value={expense.recurringPeriod}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="weekly">أسبوعي</option>
                    <option value="monthly">شهري</option>
                    <option value="quarterly">ربع سنوي</option>
                    <option value="yearly">سنوي</option>
                  </select>
                </div>
              )}
            </div>

            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                ملاحظات
              </label>
              <textarea
                id="notes"
                name="notes"
                value={expense.notes}
                onChange={handleChange}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="ملاحظات إضافية حول المصروف..."
              />
            </div>

            <div className="flex justify-end gap-4 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => router.push('/expenses')}
                className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                إلغاء
              </button>
              <button
                type="submit"
                disabled={saving}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {saving ? 'جاري الحفظ...' : 'حفظ المصروف'}
              </button>
            </div>
          </form>
        </div>

        {/* Help Section */}
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-orange-900 mb-2">نصائح لتسجيل المصروفات:</h3>
          <ul className="text-sm text-orange-800 space-y-1">
            <li>• استخدم وصفاً واضحاً ومفصلاً للمصروف</li>
            <li>• اختر التصنيف المناسب لتسهيل التقارير</li>
            <li>• احتفظ بالإيصالات والفواتير كمرجع</li>
            <li>• سجل المصروفات فور حدوثها لتجنب النسيان</li>
            <li>• استخدم المصروفات المتكررة للمصاريف الثابتة</li>
            <li>• أضف رقم المرجع أو الفاتورة للمتابعة</li>
          </ul>
        </div>
      </div>
    </MainLayout>
  )
}
