/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/hr/page";
exports.ids = ["app/hr/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fhr%2Fpage&page=%2Fhr%2Fpage&appPaths=%2Fhr%2Fpage&pagePath=private-next-app-dir%2Fhr%2Fpage.tsx&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fhr%2Fpage&page=%2Fhr%2Fpage&appPaths=%2Fhr%2Fpage&pagePath=private-next-app-dir%2Fhr%2Fpage.tsx&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/hr/page.tsx */ \"(rsc)/./src/app/hr/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'hr',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/hr/page\",\n        pathname: \"/hr\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZociUyRnBhZ2UmcGFnZT0lMkZociUyRnBhZ2UmYXBwUGF0aHM9JTJGaHIlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGaHIlMkZwYWdlLnRzeCZhcHBEaXI9RCUzQSU1Q0FjY291bnRpbmclNUNhY2NvdW50aW5nLXN5c3RlbSU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9RCUzQSU1Q0FjY291bnRpbmclNUNhY2NvdW50aW5nLXN5c3RlbSZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsc0JBQXNCLG9KQUE0RjtBQUNsSCxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLGdPQUFtRjtBQUN6RyxvQkFBb0Isc0pBQThGO0FBR2hIO0FBR0E7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLHNmQUFvUDtBQUN4UjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0Msc2ZBQW9QO0FBQ3hSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBR3JCO0FBQ0YsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBR0U7QUFDRjtBQUNPLHdCQUF3Qix1R0FBa0I7QUFDakQ7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbW9kdWxlMCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQWNjb3VudGluZ1xcXFxhY2NvdW50aW5nLXN5c3RlbVxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIik7XG5jb25zdCBtb2R1bGUxID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMiA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2ZvcmJpZGRlbi1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTMgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIik7XG5jb25zdCBwYWdlNCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQWNjb3VudGluZ1xcXFxhY2NvdW50aW5nLXN5c3RlbVxcXFxzcmNcXFxcYXBwXFxcXGhyXFxcXHBhZ2UudHN4XCIpO1xuaW1wb3J0IHsgQXBwUGFnZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zc3InXG59O1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICdocicsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFtwYWdlNCwgXCJEOlxcXFxBY2NvdW50aW5nXFxcXGFjY291bnRpbmctc3lzdGVtXFxcXHNyY1xcXFxhcHBcXFxcaHJcXFxccGFnZS50c3hcIl0sXG4gICAgICAgICAgXG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBtZXRhZGF0YToge1xuICAgIGljb246IFsoYXN5bmMgKHByb3BzKSA9PiAoYXdhaXQgaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlcj90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhRDpcXFxcQWNjb3VudGluZ1xcXFxhY2NvdW50aW5nLXN5c3RlbVxcXFxzcmNcXFxcYXBwXFxcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogW21vZHVsZTAsIFwiRDpcXFxcQWNjb3VudGluZ1xcXFxhY2NvdW50aW5nLXN5c3RlbVxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogW21vZHVsZTEsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbidmb3JiaWRkZW4nOiBbbW9kdWxlMiwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCJdLFxuJ3VuYXV0aG9yaXplZCc6IFttb2R1bGUzLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIl0sXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFEOlxcXFxBY2NvdW50aW5nXFxcXGFjY291bnRpbmctc3lzdGVtXFxcXHNyY1xcXFxhcHBcXFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX19cIikpLmRlZmF1bHQocHJvcHMpKV0sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IHVuZGVmaW5lZFxuICB9XG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJEOlxcXFxBY2NvdW50aW5nXFxcXGFjY291bnRpbmctc3lzdGVtXFxcXHNyY1xcXFxhcHBcXFxcaHJcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL2hyL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2hyXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogJycsXG4gICAgICAgIGZpbGVuYW1lOiAnJyxcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fhr%2Fpage&page=%2Fhr%2Fpage&appPaths=%2Fhr%2Fpage&pagePath=private-next-app-dir%2Fhr%2Fpage.tsx&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers/SessionProvider.tsx */ \"(rsc)/./src/components/Providers/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0FjY291bnRpbmclNUMlNUNhY2NvdW50aW5nLXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDUHJvdmlkZXJzJTVDJTVDU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdNQUFvSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXEFjY291bnRpbmdcXFxcYWNjb3VudGluZy1zeXN0ZW1cXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcUHJvdmlkZXJzXFxcXFNlc3Npb25Qcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Chr%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Chr%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/hr/page.tsx */ \"(rsc)/./src/app/hr/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNociU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzSkFBOEYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEFjY291bnRpbmdcXFxcYWNjb3VudGluZy1zeXN0ZW1cXFxcc3JjXFxcXGFwcFxcXFxoclxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Chr%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxBY2NvdW50aW5nXFxhY2NvdW50aW5nLXN5c3RlbVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcQWNjb3VudGluZ1xcYWNjb3VudGluZy1zeXN0ZW1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/hr/page.tsx":
/*!*****************************!*\
  !*** ./src/app/hr/page.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Accounting\\accounting-system\\src\\app\\hr\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Providers_SessionProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Providers/SessionProvider */ \"(rsc)/./src/components/Providers/SessionProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"نظام المحاسبة الاحترافي\",\n    description: \"نظام محاسبة شامل للشركات الصغيرة والمتوسطة\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers_SessionProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGlCO0FBQzhDO0FBSTlELE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsS0FBSTtrQkFDbEIsNEVBQUNDO1lBQUtDLFdBQVdYLCtKQUFlO3NCQUM5Qiw0RUFBQ0MsNkVBQWVBOzBCQUNiSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJEOlxcQWNjb3VudGluZ1xcYWNjb3VudGluZy1zeXN0ZW1cXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuaW1wb3J0IFNlc3Npb25Qcm92aWRlciBmcm9tIFwiQC9jb21wb25lbnRzL1Byb3ZpZGVycy9TZXNzaW9uUHJvdmlkZXJcIjtcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFtcImxhdGluXCJdIH0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCLZhti42KfZhSDYp9mE2YXYrdin2LPYqNipINin2YTYp9it2KrYsdin2YHZilwiLFxuICBkZXNjcmlwdGlvbjogXCLZhti42KfZhSDZhdit2KfYs9io2Kkg2LTYp9mF2YQg2YTZhNi02LHZg9in2Kog2KfZhNi12LrZitix2Kkg2YjYp9mE2YXYqtmI2LPYt9ipXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJhclwiIGRpcj1cInJ0bFwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8U2Vzc2lvblByb3ZpZGVyPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9TZXNzaW9uUHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwiU2Vzc2lvblByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJkaXIiLCJib2R5IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/Providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Providers\\\\SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Accounting\\accounting-system\\src\\components\\Providers\\SessionProvider.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers/SessionProvider.tsx */ \"(ssr)/./src/components/Providers/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0FjY291bnRpbmclNUMlNUNhY2NvdW50aW5nLXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDUHJvdmlkZXJzJTVDJTVDU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdNQUFvSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXEFjY291bnRpbmdcXFxcYWNjb3VudGluZy1zeXN0ZW1cXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcUHJvdmlkZXJzXFxcXFNlc3Npb25Qcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Ccomponents%5C%5CProviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Chr%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Chr%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/hr/page.tsx */ \"(ssr)/./src/app/hr/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBY2NvdW50aW5nJTVDJTVDYWNjb3VudGluZy1zeXN0ZW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNociU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzSkFBOEYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEFjY291bnRpbmdcXFxcYWNjb3VudGluZy1zeXN0ZW1cXFxcc3JjXFxcXGFwcFxcXFxoclxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAccounting%5C%5Caccounting-system%5C%5Csrc%5C%5Capp%5C%5Chr%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/hr/page.tsx":
/*!*****************************!*\
  !*** ./src/app/hr/page.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HRDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(ssr)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Building,Calendar,CalendarPlus,CheckCircle,Clock,DollarSign,FileText,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Building,Calendar,CalendarPlus,CheckCircle,Clock,DollarSign,FileText,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Building,Calendar,CalendarPlus,CheckCircle,Clock,DollarSign,FileText,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Building,Calendar,CalendarPlus,CheckCircle,Clock,DollarSign,FileText,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Building,Calendar,CalendarPlus,CheckCircle,Clock,DollarSign,FileText,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Building,Calendar,CalendarPlus,CheckCircle,Clock,DollarSign,FileText,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Building,Calendar,CalendarPlus,CheckCircle,Clock,DollarSign,FileText,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Building,Calendar,CalendarPlus,CheckCircle,Clock,DollarSign,FileText,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Building,Calendar,CalendarPlus,CheckCircle,Clock,DollarSign,FileText,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Building,Calendar,CalendarPlus,CheckCircle,Clock,DollarSign,FileText,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Building,Calendar,CalendarPlus,CheckCircle,Clock,DollarSign,FileText,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,Building,Calendar,CalendarPlus,CheckCircle,Clock,DollarSign,FileText,TrendingUp,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction HRDashboard() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalEmployees: 0,\n        activeEmployees: 0,\n        pendingLeaves: 0,\n        pendingSalaries: 0,\n        totalSalariesAmount: 0,\n        departmentsCount: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HRDashboard.useEffect\": ()=>{\n            const fetchStats = {\n                \"HRDashboard.useEffect.fetchStats\": async ()=>{\n                    try {\n                        const response = await fetch('/api/hr/dashboard');\n                        if (response.ok) {\n                            const data = await response.json();\n                            setStats(data);\n                        } else {\n                            // Fallback to demo data\n                            setStats({\n                                totalEmployees: 25,\n                                activeEmployees: 23,\n                                pendingLeaves: 5,\n                                pendingSalaries: 8,\n                                totalSalariesAmount: 185000,\n                                departmentsCount: 6\n                            });\n                        }\n                    } catch (error) {\n                        console.error('Error fetching HR stats:', error);\n                        // Use demo data as fallback\n                        setStats({\n                            totalEmployees: 25,\n                            activeEmployees: 23,\n                            pendingLeaves: 5,\n                            pendingSalaries: 8,\n                            totalSalariesAmount: 185000,\n                            departmentsCount: 6\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HRDashboard.useEffect.fetchStats\"];\n            fetchStats();\n        }\n    }[\"HRDashboard.useEffect\"], []);\n    const quickActions = [\n        {\n            title: 'إضافة موظف جديد',\n            description: 'تسجيل موظف جديد في النظام',\n            icon: _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: '/hr/employees/new',\n            color: 'bg-blue-500'\n        },\n        {\n            title: 'طلب إجازة',\n            description: 'تقديم طلب إجازة جديد',\n            icon: _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: '/hr/leaves/new',\n            color: 'bg-green-500'\n        },\n        {\n            title: 'إنشاء رواتب الشهر',\n            description: 'إنشاء رواتب جميع الموظفين للشهر الحالي',\n            icon: _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: '/hr/salaries/generate',\n            color: 'bg-purple-500'\n        },\n        {\n            title: 'تقارير الموارد البشرية',\n            description: 'عرض تقارير شاملة عن الموظفين',\n            icon: _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: '/hr/reports',\n            color: 'bg-orange-500'\n        }\n    ];\n    const hrModules = [\n        {\n            title: 'إدارة الموظفين',\n            description: 'إدارة بيانات الموظفين ومعلوماتهم الشخصية',\n            icon: _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            href: '/hr/employees',\n            stats: `${stats.totalEmployees} موظف`,\n            color: 'border-blue-500'\n        },\n        {\n            title: 'إدارة الإجازات',\n            description: 'إدارة طلبات الإجازات والموافقة عليها',\n            icon: _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            href: '/hr/leaves',\n            stats: `${stats.pendingLeaves} طلب في الانتظار`,\n            color: 'border-green-500'\n        },\n        {\n            title: 'إدارة الرواتب',\n            description: 'إدارة رواتب الموظفين وكشوف المرتبات',\n            icon: _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: '/hr/salaries',\n            stats: `${stats.pendingSalaries} راتب في الانتظار`,\n            color: 'border-purple-500'\n        },\n        {\n            title: 'تقييم الأداء',\n            description: 'تقييم أداء الموظفين والمراجعات الدورية',\n            icon: _barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            href: '/hr/performance',\n            stats: 'قريباً',\n            color: 'border-yellow-500'\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"لوحة تحكم الموارد البشرية\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"إدارة شاملة للموظفين والإجازات والرواتب\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"إجمالي الموظفين\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: stats.totalEmployees\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"الموظفين النشطين\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: stats.activeEmployees\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"إجازات معلقة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-yellow-600\",\n                                                children: stats.pendingLeaves\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"رواتب معلقة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-purple-600\",\n                                                children: stats.pendingSalaries\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-indigo-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"إجمالي الرواتب\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl font-bold text-indigo-600\",\n                                                children: [\n                                                    stats.totalSalariesAmount.toLocaleString(),\n                                                    \" ر.س\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-orange-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"الأقسام\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-orange-600\",\n                                                children: stats.departmentsCount\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: \"الإجراءات السريعة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: quickActions.map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: action.href,\n                                    className: \"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `${action.color} p-3 rounded-lg`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: action.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: action.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: \"وحدات الموارد البشرية\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: hrModules.map((module, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: module.href,\n                                    className: `bg-white rounded-lg shadow p-6 border-r-4 ${module.color} hover:shadow-lg transition-shadow duration-200`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(module.icon, {\n                                                    className: \"h-8 w-8 text-gray-600 ml-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: module.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: module.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-blue-600 mt-2\",\n                                                            children: module.stats\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-medium text-gray-900\",\n                                children: \"الأنشطة الأخيرة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-100 p-2 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: \"تم إضافة موظف جديد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"أحمد محمد - قسم تقنية المعلومات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-auto text-sm text-gray-500\",\n                                                children: \"منذ ساعتين\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-100 p-2 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: \"تم الموافقة على إجازة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"فاطمة أحمد - إجازة سنوية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-auto text-sm text-gray-500\",\n                                                children: \"منذ 4 ساعات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-100 p-2 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: \"تم دفع راتب\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"خالد علي - راتب شهر مارس\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-auto text-sm text-gray-500\",\n                                                children: \"أمس\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-100 p-2 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_Building_Calendar_CalendarPlus_CheckCircle_Clock_DollarSign_FileText_TrendingUp_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: \"طلب إجازة جديد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"سارة محمود - إجازة مرضية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mr-auto text-sm text-gray-500\",\n                                                children: \"منذ يومين\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\hr\\\\page.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/hr/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/MainLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/Layout/MainLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MainLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/Layout/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction MainLayout({ children }) {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"MainLayout.useEffect\": ()=>{\n            if (status === 'loading') return; // Still loading\n            if (!session) {\n                router.push('/auth/signin');\n                return;\n            }\n        }\n    }[\"MainLayout.useEffect\"], [\n        session,\n        status,\n        router\n    ]);\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-100\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\MainLayout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9MYXlvdXQvTWFpbkxheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNEO0FBQ1Y7QUFDRjtBQU1oQixTQUFTSSxXQUFXLEVBQUVDLFFBQVEsRUFBbUI7SUFDOUQsTUFBTSxFQUFFQyxNQUFNQyxPQUFPLEVBQUVDLE1BQU0sRUFBRSxHQUFHUiwyREFBVUE7SUFDNUMsTUFBTVMsU0FBU1IsMERBQVNBO0lBRXhCQyxnREFBU0E7Z0NBQUM7WUFDUixJQUFJTSxXQUFXLFdBQVcsUUFBTyxnQkFBZ0I7WUFFakQsSUFBSSxDQUFDRCxTQUFTO2dCQUNaRSxPQUFPQyxJQUFJLENBQUM7Z0JBQ1o7WUFDRjtRQUNGOytCQUFHO1FBQUNIO1FBQVNDO1FBQVFDO0tBQU87SUFFNUIsSUFBSUQsV0FBVyxXQUFXO1FBQ3hCLHFCQUNFLDhEQUFDRztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7SUFHckI7SUFFQSxJQUFJLENBQUNMLFNBQVM7UUFDWixPQUFPO0lBQ1Q7SUFFQSxxQkFDRSw4REFBQ0k7UUFBSUMsV0FBVTtRQUE0QkMsS0FBSTs7MEJBQzdDLDhEQUFDVixnREFBT0E7Ozs7OzBCQUNSLDhEQUFDUTtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0U7b0JBQUtGLFdBQVU7OEJBQ2JQOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJEOlxcQWNjb3VudGluZ1xcYWNjb3VudGluZy1zeXN0ZW1cXHNyY1xcY29tcG9uZW50c1xcTGF5b3V0XFxNYWluTGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU2Vzc2lvbiB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IFNpZGViYXIgZnJvbSAnLi9TaWRlYmFyJ1xuXG5pbnRlcmZhY2UgTWFpbkxheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBNYWluTGF5b3V0KHsgY2hpbGRyZW4gfTogTWFpbkxheW91dFByb3BzKSB7XG4gIGNvbnN0IHsgZGF0YTogc2Vzc2lvbiwgc3RhdHVzIH0gPSB1c2VTZXNzaW9uKClcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzdGF0dXMgPT09ICdsb2FkaW5nJykgcmV0dXJuIC8vIFN0aWxsIGxvYWRpbmdcblxuICAgIGlmICghc2Vzc2lvbikge1xuICAgICAgcm91dGVyLnB1c2goJy9hdXRoL3NpZ25pbicpXG4gICAgICByZXR1cm5cbiAgICB9XG4gIH0sIFtzZXNzaW9uLCBzdGF0dXMsIHJvdXRlcl0pXG5cbiAgaWYgKHN0YXR1cyA9PT0gJ2xvYWRpbmcnKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTMyIHctMzIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDBcIj48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIGlmICghc2Vzc2lvbikge1xuICAgIHJldHVybiBudWxsXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLXNjcmVlbiBiZy1ncmF5LTEwMFwiIGRpcj1cInJ0bFwiPlxuICAgICAgPFNpZGViYXIgLz5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2wgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy14LWhpZGRlbiBvdmVyZmxvdy15LWF1dG8gYmctZ3JheS0xMDAgcC02XCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L21haW4+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVNlc3Npb24iLCJ1c2VSb3V0ZXIiLCJ1c2VFZmZlY3QiLCJTaWRlYmFyIiwiTWFpbkxheW91dCIsImNoaWxkcmVuIiwiZGF0YSIsInNlc3Npb24iLCJzdGF0dXMiLCJyb3V0ZXIiLCJwdXNoIiwiZGl2IiwiY2xhc3NOYW1lIiwiZGlyIiwibWFpbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/MainLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/Layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Briefcase,Building,CreditCard,FileText,LayoutDashboard,LogOut,Package,Settings,User,UserCheck,Users,Wallet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst navigation = [\n    {\n        name: 'لوحة التحكم',\n        href: '/',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: 'العملاء',\n        href: '/customers',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'الفواتير',\n        href: '/invoices',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'المنتجات',\n        href: '/products',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'المصروفات',\n        href: '/expenses',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'البنوك',\n        href: '/banking',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'الخزينة',\n        href: '/treasury',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: 'الأصول',\n        href: '/assets',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: 'الموارد البشرية',\n        href: '/hr',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: 'التقارير',\n        href: '/reports',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        name: 'المستخدمين',\n        href: '/users',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        adminOnly: true\n    },\n    {\n        name: 'الإعدادات',\n        href: '/settings',\n        icon: _barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const handleSignOut = ()=>{\n        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)({\n            callbackUrl: '/auth/signin'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full w-64 flex-col bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center justify-center bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-bold text-white\",\n                    children: \"نظام المحاسبة\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 space-y-1 px-2 py-4\",\n                children: navigation.map((item)=>{\n                    // Hide admin-only items for non-admin users\n                    if (item.adminOnly && session?.user?.role !== 'ADMIN') {\n                        return null;\n                    }\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: item.href,\n                        className: `\n                group flex items-center px-2 py-2 text-sm font-medium rounded-md\n                ${isActive ? 'bg-gray-800 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'}\n              `,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: `\n                  mr-3 h-5 w-5 flex-shrink-0\n                  ${isActive ? 'text-white' : 'text-gray-400 group-hover:text-white'}\n                `\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 15\n                            }, this),\n                            item.name\n                        ]\n                    }, item.name, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-700 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-full bg-gray-600 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-white\",\n                                        children: session?.user?.name || session?.user?.email\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: session?.user?.role === 'ADMIN' ? 'مشرف' : session?.user?.role === 'MANAGER' ? 'مدير' : 'محاسب'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSignOut,\n                        className: \"mt-3 w-full flex items-center px-2 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-gray-700 hover:text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Briefcase_Building_CreditCard_FileText_LayoutDashboard_LogOut_Package_Settings_User_UserCheck_Users_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"mr-3 h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            \"تسجيل الخروج\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/Providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\components\\\\Providers\\\\SessionProvider.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Qcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNEU7QUFNN0QsU0FBU0EsZ0JBQWdCLEVBQUVFLFFBQVEsRUFBd0I7SUFDeEUscUJBQ0UsOERBQUNELDREQUF1QkE7a0JBQ3JCQzs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkQ6XFxBY2NvdW50aW5nXFxhY2NvdW50aW5nLXN5c3RlbVxcc3JjXFxjb21wb25lbnRzXFxQcm92aWRlcnNcXFNlc3Npb25Qcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciBhcyBOZXh0QXV0aFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcblxuaW50ZXJmYWNlIFNlc3Npb25Qcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTZXNzaW9uUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiBTZXNzaW9uUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxOZXh0QXV0aFNlc3Npb25Qcm92aWRlcj5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L05leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Providers/SessionProvider.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fhr%2Fpage&page=%2Fhr%2Fpage&appPaths=%2Fhr%2Fpage&pagePath=private-next-app-dir%2Fhr%2Fpage.tsx&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();