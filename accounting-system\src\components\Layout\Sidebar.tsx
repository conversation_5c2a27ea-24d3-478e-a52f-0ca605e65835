'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useSession, signOut } from 'next-auth/react'
import {
  LayoutDashboard,
  Users,
  FileText,
  Package,
  CreditCard,
  BarChart3,
  Settings,
  LogOut,
  User,
  UserCheck
} from 'lucide-react'

const navigation = [
  { name: 'لوحة التحكم', href: '/', icon: LayoutDashboard },
  { name: 'العملاء', href: '/customers', icon: Users },
  { name: 'الفواتير', href: '/invoices', icon: FileText },
  { name: 'المنتجات', href: '/products', icon: Package },
  { name: 'المصروفات', href: '/expenses', icon: CreditCard },
  { name: 'الموارد البشرية', href: '/hr', icon: UserCheck },
  { name: 'التقارير', href: '/reports', icon: BarChart3 },
  { name: 'المستخدمين', href: '/users', icon: User, adminOnly: true },
  { name: 'الإعدادات', href: '/settings', icon: Settings },
]

export default function Sidebar() {
  const pathname = usePathname()
  const { data: session } = useSession()

  const handleSignOut = () => {
    signOut({ callbackUrl: '/auth/signin' })
  }

  return (
    <div className="flex h-full w-64 flex-col bg-gray-900">
      <div className="flex h-16 items-center justify-center bg-gray-800">
        <h1 className="text-xl font-bold text-white">نظام المحاسبة</h1>
      </div>
      
      <nav className="flex-1 space-y-1 px-2 py-4">
        {navigation.map((item) => {
          // Hide admin-only items for non-admin users
          if (item.adminOnly && session?.user?.role !== 'ADMIN') {
            return null
          }

          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`
                group flex items-center px-2 py-2 text-sm font-medium rounded-md
                ${isActive
                  ? 'bg-gray-800 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }
              `}
            >
              <item.icon
                className={`
                  mr-3 h-5 w-5 flex-shrink-0
                  ${isActive ? 'text-white' : 'text-gray-400 group-hover:text-white'}
                `}
              />
              {item.name}
            </Link>
          )
        })}
      </nav>

      <div className="border-t border-gray-700 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="h-8 w-8 rounded-full bg-gray-600 flex items-center justify-center">
              <User className="h-5 w-5 text-gray-300" />
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-white">
              {session?.user?.name || session?.user?.email}
            </p>
            <p className="text-xs text-gray-400">
              {session?.user?.role === 'ADMIN' ? 'مشرف' : 
               session?.user?.role === 'MANAGER' ? 'مدير' : 'محاسب'}
            </p>
          </div>
        </div>
        <button
          onClick={handleSignOut}
          className="mt-3 w-full flex items-center px-2 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-gray-700 hover:text-white"
        >
          <LogOut className="mr-3 h-5 w-5" />
          تسجيل الخروج
        </button>
      </div>
    </div>
  )
}
