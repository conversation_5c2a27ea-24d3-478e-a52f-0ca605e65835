{"name": "accounting-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:generate": "prisma generate", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma db push --force-reset && npm run db:seed"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.0.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.8.2", "@supabase/supabase-js": "^2.49.8", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "next": "15.3.3", "next-auth": "^4.24.11", "prisma": "^6.8.2", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tsx": "^4.19.2", "typescript": "^5"}}