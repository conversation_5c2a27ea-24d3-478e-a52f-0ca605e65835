'use client'

import { useState, useEffect } from 'react'
import MainLayout from '@/components/Layout/MainLayout'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Calendar,
  Download,
  FileText,
  DollarSign
} from 'lucide-react'

interface ReportData {
  revenue: number[]
  expenses: number[]
  profit: number[]
  months: string[]
}

interface SummaryStats {
  totalRevenue: number
  totalExpenses: number
  netProfit: number
  profitMargin: number
}

export default function ReportsPage() {
  const [reportData, setReportData] = useState<ReportData>({
    revenue: [],
    expenses: [],
    profit: [],
    months: []
  })
  const [summaryStats, setSummaryStats] = useState<SummaryStats>({
    totalRevenue: 0,
    totalExpenses: 0,
    netProfit: 0,
    profitMargin: 0
  })
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('THIS_YEAR')

  useEffect(() => {
    // Simulate API call - replace with actual API call
    setTimeout(() => {
      const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو']
      const revenue = [45000, 52000, 48000, 61000, 55000, 67000]
      const expenses = [25000, 28000, 22000, 35000, 30000, 38000]
      const profit = revenue.map((rev, index) => rev - expenses[index])
      
      setReportData({
        revenue,
        expenses,
        profit,
        months
      })

      const totalRevenue = revenue.reduce((sum, val) => sum + val, 0)
      const totalExpenses = expenses.reduce((sum, val) => sum + val, 0)
      const netProfit = totalRevenue - totalExpenses
      const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0

      setSummaryStats({
        totalRevenue,
        totalExpenses,
        netProfit,
        profitMargin
      })

      setLoading(false)
    }, 1000)
  }, [selectedPeriod])

  const generateReport = (type: string) => {
    alert(`سيتم تحميل تقرير ${type} قريباً`)
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">التقارير المالية</h1>
            <p className="mt-2 text-gray-600">تحليل الأداء المالي والتقارير الدورية</p>
          </div>
          <div className="flex gap-2">
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
            >
              <option value="THIS_MONTH">هذا الشهر</option>
              <option value="LAST_MONTH">الشهر الماضي</option>
              <option value="THIS_QUARTER">هذا الربع</option>
              <option value="THIS_YEAR">هذا العام</option>
              <option value="LAST_YEAR">العام الماضي</option>
            </select>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-500 p-3 rounded-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الإيرادات</p>
                <p className="text-2xl font-bold text-green-600">
                  {summaryStats.totalRevenue.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-red-500 p-3 rounded-lg">
                <TrendingDown className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي المصروفات</p>
                <p className="text-2xl font-bold text-red-600">
                  {summaryStats.totalExpenses.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className={`${summaryStats.netProfit >= 0 ? 'bg-blue-500' : 'bg-red-500'} p-3 rounded-lg`}>
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">صافي الربح</p>
                <p className={`text-2xl font-bold ${summaryStats.netProfit >= 0 ? 'text-blue-600' : 'text-red-600'}`}>
                  {summaryStats.netProfit.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-purple-500 p-3 rounded-lg">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">هامش الربح</p>
                <p className="text-2xl font-bold text-purple-600">
                  {summaryStats.profitMargin.toFixed(1)}%
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Revenue vs Expenses Chart */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">الإيرادات مقابل المصروفات</h3>
            <div className="h-64 flex items-end justify-between space-x-2">
              {reportData.months.map((month, index) => (
                <div key={month} className="flex flex-col items-center space-y-2 flex-1">
                  <div className="flex flex-col items-center space-y-1 w-full">
                    <div 
                      className="bg-green-500 w-full rounded-t"
                      style={{ 
                        height: `${(reportData.revenue[index] / Math.max(...reportData.revenue)) * 200}px`,
                        minHeight: '10px'
                      }}
                      title={`إيرادات: ${reportData.revenue[index].toLocaleString()} ر.س`}
                    ></div>
                    <div 
                      className="bg-red-500 w-full rounded-b"
                      style={{ 
                        height: `${(reportData.expenses[index] / Math.max(...reportData.revenue)) * 200}px`,
                        minHeight: '10px'
                      }}
                      title={`مصروفات: ${reportData.expenses[index].toLocaleString()} ر.س`}
                    ></div>
                  </div>
                  <span className="text-xs text-gray-600 transform -rotate-45">{month}</span>
                </div>
              ))}
            </div>
            <div className="flex justify-center mt-4 space-x-4">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded mr-2"></div>
                <span className="text-sm text-gray-600">الإيرادات</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-500 rounded mr-2"></div>
                <span className="text-sm text-gray-600">المصروفات</span>
              </div>
            </div>
          </div>

          {/* Profit Trend Chart */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">اتجاه الأرباح</h3>
            <div className="h-64 flex items-end justify-between space-x-2">
              {reportData.months.map((month, index) => (
                <div key={month} className="flex flex-col items-center space-y-2 flex-1">
                  <div 
                    className={`w-full rounded ${reportData.profit[index] >= 0 ? 'bg-blue-500' : 'bg-red-500'}`}
                    style={{ 
                      height: `${Math.abs(reportData.profit[index]) / Math.max(...reportData.profit.map(Math.abs)) * 200}px`,
                      minHeight: '10px'
                    }}
                    title={`ربح: ${reportData.profit[index].toLocaleString()} ر.س`}
                  ></div>
                  <span className="text-xs text-gray-600 transform -rotate-45">{month}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Monthly Breakdown Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">التفصيل الشهري</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الشهر
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإيرادات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المصروفات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    صافي الربح
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    هامش الربح
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reportData.months.map((month, index) => {
                  const profit = reportData.profit[index]
                  const margin = reportData.revenue[index] > 0 ? (profit / reportData.revenue[index]) * 100 : 0
                  
                  return (
                    <tr key={month} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {month}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">
                        {reportData.revenue[index].toLocaleString()} ر.س
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">
                        {reportData.expenses[index].toLocaleString()} ر.س
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${profit >= 0 ? 'text-blue-600' : 'text-red-600'}`}>
                        {profit.toLocaleString()} ر.س
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${margin >= 0 ? 'text-blue-600' : 'text-red-600'}`}>
                        {margin.toFixed(1)}%
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </div>

        {/* Report Generation */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">تحميل التقارير</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => generateReport('الأرباح والخسائر')}
              className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              <Download className="h-5 w-5 ml-2 text-gray-400" />
              <span className="text-sm font-medium text-gray-700">تقرير الأرباح والخسائر</span>
            </button>
            
            <button
              onClick={() => generateReport('التدفق النقدي')}
              className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              <Download className="h-5 w-5 ml-2 text-gray-400" />
              <span className="text-sm font-medium text-gray-700">تقرير التدفق النقدي</span>
            </button>
            
            <button
              onClick={() => generateReport('الميزانية العمومية')}
              className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              <Download className="h-5 w-5 ml-2 text-gray-400" />
              <span className="text-sm font-medium text-gray-700">الميزانية العمومية</span>
            </button>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
