/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/invoices/route";
exports.ids = ["app/api/invoices/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Accounting_accounting_system_src_app_api_invoices_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/invoices/route.ts */ \"(rsc)/./src/app/api/invoices/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/invoices/route\",\n        pathname: \"/api/invoices\",\n        filename: \"route\",\n        bundlePath: \"app/api/invoices/route\"\n    },\n    resolvedPagePath: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\api\\\\invoices\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Accounting_accounting_system_src_app_api_invoices_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/invoices/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/invoices/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '50');\n        const search = searchParams.get('search');\n        const status = searchParams.get('status');\n        const sortBy = searchParams.get('sortBy') || 'issueDate';\n        const sortOrder = searchParams.get('sortOrder') || 'desc';\n        const skip = (page - 1) * limit;\n        // Build where clause\n        let whereClause = {};\n        if (search) {\n            whereClause.OR = [\n                {\n                    number: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    customer: {\n                        OR: [\n                            {\n                                name: {\n                                    contains: search,\n                                    mode: 'insensitive'\n                                }\n                            },\n                            {\n                                email: {\n                                    contains: search,\n                                    mode: 'insensitive'\n                                }\n                            }\n                        ]\n                    }\n                }\n            ];\n        }\n        if (status && status !== 'ALL') {\n            whereClause.status = status;\n        }\n        // Get invoices with pagination\n        const [invoices, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.findMany({\n                where: whereClause,\n                orderBy: {\n                    [sortBy]: sortOrder\n                },\n                skip,\n                take: limit,\n                include: {\n                    customer: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            phone: true\n                        }\n                    },\n                    items: {\n                        select: {\n                            id: true,\n                            productId: true,\n                            quantity: true,\n                            price: true,\n                            total: true,\n                            product: {\n                                select: {\n                                    name: true,\n                                    description: true\n                                }\n                            }\n                        }\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.count({\n                where: whereClause\n            })\n        ]);\n        // Calculate statistics\n        const allInvoices = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.findMany({\n            select: {\n                status: true,\n                total: true\n            }\n        });\n        const stats = {\n            totalInvoices: allInvoices.length,\n            totalAmount: allInvoices.reduce((sum, inv)=>sum + inv.total, 0),\n            byStatus: {\n                DRAFT: {\n                    count: allInvoices.filter((inv)=>inv.status === 'DRAFT').length,\n                    amount: allInvoices.filter((inv)=>inv.status === 'DRAFT').reduce((sum, inv)=>sum + inv.total, 0)\n                },\n                SENT: {\n                    count: allInvoices.filter((inv)=>inv.status === 'SENT').length,\n                    amount: allInvoices.filter((inv)=>inv.status === 'SENT').reduce((sum, inv)=>sum + inv.total, 0)\n                },\n                PAID: {\n                    count: allInvoices.filter((inv)=>inv.status === 'PAID').length,\n                    amount: allInvoices.filter((inv)=>inv.status === 'PAID').reduce((sum, inv)=>sum + inv.total, 0)\n                },\n                OVERDUE: {\n                    count: allInvoices.filter((inv)=>inv.status === 'OVERDUE').length,\n                    amount: allInvoices.filter((inv)=>inv.status === 'OVERDUE').reduce((sum, inv)=>sum + inv.total, 0)\n                },\n                CANCELLED: {\n                    count: allInvoices.filter((inv)=>inv.status === 'CANCELLED').length,\n                    amount: allInvoices.filter((inv)=>inv.status === 'CANCELLED').reduce((sum, inv)=>sum + inv.total, 0)\n                }\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            invoices,\n            stats,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error('Error fetching invoices:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { number, customerId, issueDate, dueDate, status, subtotal, taxAmount, total, notes, items } = body;\n        // Validate required fields\n        if (!customerId || !items || items.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // Create invoice with items\n        const invoice = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.create({\n            data: {\n                number,\n                customerId,\n                userId: session.user.id,\n                issueDate: new Date(issueDate),\n                dueDate: new Date(dueDate),\n                status,\n                subtotal,\n                taxAmount,\n                total,\n                notes,\n                items: {\n                    create: items.map((item)=>({\n                            productId: item.productId,\n                            quantity: item.quantity,\n                            price: item.price,\n                            total: item.total\n                        }))\n                }\n            },\n            include: {\n                customer: true,\n                user: true,\n                items: {\n                    include: {\n                        product: true\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(invoice, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating invoice:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/invoices/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user) {\n                    return null;\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"].compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        signUp: '/auth/signup'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkQ6XFxBY2NvdW50aW5nXFxhY2NvdW50aW5nLXN5c3RlbVxcc3JjXFxsaWJcXHByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/@next-auth","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();