/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/invoices/route";
exports.ids = ["app/api/invoices/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Accounting_accounting_system_src_app_api_invoices_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/invoices/route.ts */ \"(rsc)/./src/app/api/invoices/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/invoices/route\",\n        pathname: \"/api/invoices\",\n        filename: \"route\",\n        bundlePath: \"app/api/invoices/route\"\n    },\n    resolvedPagePath: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\api\\\\invoices\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Accounting_accounting_system_src_app_api_invoices_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/invoices/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/invoices/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '50');\n        const search = searchParams.get('search');\n        const status = searchParams.get('status');\n        const sortBy = searchParams.get('sortBy') || 'issueDate';\n        const sortOrder = searchParams.get('sortOrder') || 'desc';\n        const skip = (page - 1) * limit;\n        // Build where clause\n        let whereClause = {};\n        if (search) {\n            whereClause.OR = [\n                {\n                    number: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    customerName: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    customerEmail: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        if (status && status !== 'ALL') {\n            whereClause.status = status;\n        }\n        // Get invoices with pagination\n        const [invoices, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.findMany({\n                where: whereClause,\n                orderBy: {\n                    [sortBy]: sortOrder\n                },\n                skip,\n                take: limit,\n                include: {\n                    items: {\n                        select: {\n                            id: true,\n                            description: true,\n                            quantity: true,\n                            unitPrice: true,\n                            total: true\n                        }\n                    }\n                }\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.count({\n                where: whereClause\n            })\n        ]);\n        // Calculate statistics\n        const allInvoices = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.findMany({\n            select: {\n                status: true,\n                total: true\n            }\n        });\n        const stats = {\n            totalInvoices: allInvoices.length,\n            totalAmount: allInvoices.reduce((sum, inv)=>sum + inv.total, 0),\n            byStatus: {\n                DRAFT: {\n                    count: allInvoices.filter((inv)=>inv.status === 'DRAFT').length,\n                    amount: allInvoices.filter((inv)=>inv.status === 'DRAFT').reduce((sum, inv)=>sum + inv.total, 0)\n                },\n                SENT: {\n                    count: allInvoices.filter((inv)=>inv.status === 'SENT').length,\n                    amount: allInvoices.filter((inv)=>inv.status === 'SENT').reduce((sum, inv)=>sum + inv.total, 0)\n                },\n                PAID: {\n                    count: allInvoices.filter((inv)=>inv.status === 'PAID').length,\n                    amount: allInvoices.filter((inv)=>inv.status === 'PAID').reduce((sum, inv)=>sum + inv.total, 0)\n                },\n                OVERDUE: {\n                    count: allInvoices.filter((inv)=>inv.status === 'OVERDUE').length,\n                    amount: allInvoices.filter((inv)=>inv.status === 'OVERDUE').reduce((sum, inv)=>sum + inv.total, 0)\n                },\n                CANCELLED: {\n                    count: allInvoices.filter((inv)=>inv.status === 'CANCELLED').length,\n                    amount: allInvoices.filter((inv)=>inv.status === 'CANCELLED').reduce((sum, inv)=>sum + inv.total, 0)\n                }\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            invoices,\n            stats,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error('Error fetching invoices:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { number, customerId, issueDate, dueDate, status, subtotal, taxAmount, total, notes, items } = body;\n        // Validate required fields\n        if (!customerId || !items || items.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // Create invoice with items\n        const invoice = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.invoice.create({\n            data: {\n                number,\n                customerId,\n                userId: session.user.id,\n                issueDate: new Date(issueDate),\n                dueDate: new Date(dueDate),\n                status,\n                subtotal,\n                taxAmount,\n                total,\n                notes,\n                items: {\n                    create: items.map((item)=>({\n                            productId: item.productId,\n                            quantity: item.quantity,\n                            price: item.price,\n                            total: item.total\n                        }))\n                }\n            },\n            include: {\n                customer: true,\n                user: true,\n                items: {\n                    include: {\n                        product: true\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(invoice, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating invoice:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/invoices/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user) {\n                    return null;\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"].compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        signUp: '/auth/signup'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkQ6XFxBY2NvdW50aW5nXFxhY2NvdW50aW5nLXN5c3RlbVxcc3JjXFxsaWJcXHByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/@next-auth","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finvoices%2Froute&page=%2Fapi%2Finvoices%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finvoices%2Froute.ts&appDir=D%3A%5CAccounting%5Caccounting-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAccounting%5Caccounting-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();