import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const dateRange = searchParams.get('dateRange')

    // Build where clause
    let whereClause: any = {}
    
    if (category && category !== 'all') {
      whereClause.category = category
    }

    // Date filtering
    if (dateRange && dateRange !== 'all') {
      const now = new Date()
      let startDate: Date

      switch (dateRange) {
        case 'thisYear':
          startDate = new Date(now.getFullYear(), 0, 1)
          break
        case 'lastYear':
          startDate = new Date(now.getFullYear() - 1, 0, 1)
          whereClause.purchaseDate = {
            gte: startDate,
            lt: new Date(now.getFullYear(), 0, 1)
          }
          break
        case 'last6Months':
          startDate = new Date()
          startDate.setMonth(startDate.getMonth() - 6)
          whereClause.purchaseDate = {
            gte: startDate
          }
          break
        default:
          break
      }

      if (dateRange === 'thisYear') {
        whereClause.purchaseDate = {
          gte: startDate
        }
      }
    }

    // Get all assets with filtering
    const assets = await prisma.asset.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' }
    })

    // Calculate statistics
    const totalAssets = assets.length
    const totalValue = assets.reduce((sum, asset) => sum + asset.currentValue, 0)
    const totalPurchaseValue = assets.reduce((sum, asset) => sum + asset.purchasePrice, 0)
    const totalDepreciation = totalPurchaseValue - totalValue

    // Status breakdown
    const activeAssets = assets.filter(asset => asset.status === 'ACTIVE').length
    const maintenanceAssets = assets.filter(asset => asset.status === 'INACTIVE').length
    const disposedAssets = assets.filter(asset => asset.status === 'DISPOSED').length

    // Category breakdown
    const categoryMap = new Map()
    assets.forEach(asset => {
      if (categoryMap.has(asset.category)) {
        const existing = categoryMap.get(asset.category)
        categoryMap.set(asset.category, {
          count: existing.count + 1,
          value: existing.value + asset.currentValue
        })
      } else {
        categoryMap.set(asset.category, {
          count: 1,
          value: asset.currentValue
        })
      }
    })

    const categoryBreakdown = Array.from(categoryMap.entries()).map(([category, data]) => ({
      category,
      count: data.count,
      value: data.value
    }))

    // Condition breakdown
    const conditionMap = new Map()
    assets.forEach(asset => {
      conditionMap.set(asset.condition, (conditionMap.get(asset.condition) || 0) + 1)
    })

    const conditionBreakdown = Array.from(conditionMap.entries()).map(([condition, count]) => ({
      condition,
      count,
      percentage: totalAssets > 0 ? (count / totalAssets) * 100 : 0
    }))

    // Monthly depreciation (calculated based on depreciation rate)
    const monthlyDepreciation = []
    const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو']
    
    for (let i = 0; i < 6; i++) {
      const monthlyAmount = assets.reduce((sum, asset) => {
        const monthlyDepreciationAmount = (asset.purchasePrice * (asset.depreciationRate / 100)) / 12
        return sum + monthlyDepreciationAmount
      }, 0)
      
      monthlyDepreciation.push({
        month: months[i],
        amount: Math.round(monthlyAmount)
      })
    }

    // Get unique categories for filtering
    const categories = [...new Set(assets.map(asset => asset.category))]

    const reportData = {
      totalAssets,
      totalValue: Math.round(totalValue),
      totalDepreciation: Math.round(totalDepreciation),
      activeAssets,
      maintenanceAssets,
      disposedAssets,
      categoryBreakdown,
      conditionBreakdown,
      monthlyDepreciation,
      categories,
      assets: assets.map(asset => ({
        id: asset.id,
        name: asset.name,
        category: asset.category,
        purchasePrice: asset.purchasePrice,
        currentValue: asset.currentValue,
        purchaseDate: asset.purchaseDate.toISOString().split('T')[0],
        depreciationRate: asset.depreciationRate,
        status: asset.status,
        location: asset.location,
        condition: asset.condition
      }))
    }

    return NextResponse.json(reportData)
  } catch (error) {
    console.error('Error fetching assets reports:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
