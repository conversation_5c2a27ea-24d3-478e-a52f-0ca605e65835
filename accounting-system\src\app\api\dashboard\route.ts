import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get customers count
    const totalCustomers = await prisma.customer.count()

    // Get invoices statistics
    const totalInvoices = await prisma.invoice.count()
    const pendingInvoices = await prisma.invoice.count({
      where: { status: 'SENT' }
    })
    const overdueInvoices = await prisma.invoice.count({
      where: { status: 'OVERDUE' }
    })

    // Get revenue from paid invoices
    const paidInvoices = await prisma.invoice.findMany({
      where: { status: 'PAID' },
      select: { total: true }
    })
    const totalRevenue = paidInvoices.reduce((sum, invoice) => sum + invoice.total, 0)

    // Get expenses
    const expenses = await prisma.expense.findMany({
      select: { amount: true }
    })
    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0)

    // Get products count
    const totalProducts = await prisma.product.count({
      where: { isActive: true }
    })

    // Get employees count
    const totalEmployees = await prisma.employee.count()

    // Get assets count
    const totalAssets = await prisma.asset.count()

    const stats = {
      totalCustomers,
      totalInvoices,
      totalRevenue: Math.round(totalRevenue),
      totalExpenses: Math.round(totalExpenses),
      pendingInvoices,
      overdueInvoices,
      totalProducts,
      totalEmployees,
      totalAssets
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
