'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import MainLayout from '@/components/Layout/MainLayout'
import { Plus, Search, TrendingUp, TrendingDown, ArrowUpRight, ArrowDownLeft, Calendar, DollarSign, Wallet } from 'lucide-react'

interface CashTransaction {
  id: string
  cashBoxId: string
  cashBoxName: string
  type: string
  amount: number
  balance: number
  description: string
  reference?: string
  category?: string
  transactionDate: string
  performedBy?: string
  status: string
  relatedType?: string
  receiptNumber?: string
  createdAt: string
}

export default function CashTransactionsPage() {
  const [transactions, setTransactions] = useState<CashTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('ALL')
  const [cashBoxFilter, setCashBoxFilter] = useState<string>('ALL')
  const [dateFilter, setDateFilter] = useState<string>('ALL')

  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        const response = await fetch('/api/treasury/transactions')
        if (response.ok) {
          const data = await response.json()
          setTransactions(data)
        } else {
          // Fallback to demo data if API fails
          setTransactions([
            {
              id: '1',
              cashBoxId: '1',
              cashBoxName: 'الصندوق الرئيسي',
              type: 'CASH_IN',
              amount: 5000,
              balance: 30000,
              description: 'إيداع من عميل - فاتورة رقم INV-001',
              reference: 'INV-001',
              category: 'مبيعات',
              transactionDate: '2024-03-15',
              performedBy: 'أحمد محمد',
              status: 'COMPLETED',
              relatedType: 'INVOICE',
              receiptNumber: 'REC-001',
              createdAt: '2024-03-15'
            },
            {
              id: '2',
              cashBoxId: '2',
              cashBoxName: 'صندوق الاستقبال',
              type: 'CASH_OUT',
              amount: 1500,
              balance: 7000,
              description: 'دفع مصروف نثري - مصاريف مكتبية',
              reference: 'PC-001',
              category: 'مصاريف إدارية',
              transactionDate: '2024-03-14',
              performedBy: 'فاطمة أحمد',
              status: 'COMPLETED',
              relatedType: 'PETTY_CASH',
              receiptNumber: 'REC-002',
              createdAt: '2024-03-14'
            },
            {
              id: '3',
              cashBoxId: '1',
              cashBoxName: 'الصندوق الرئيسي',
              type: 'TRANSFER_OUT',
              amount: 3000,
              balance: 27000,
              description: 'تحويل إلى صندوق الاستقبال',
              reference: 'TRF-001',
              category: 'تحويل داخلي',
              transactionDate: '2024-03-13',
              performedBy: 'أحمد محمد',
              status: 'COMPLETED',
              relatedType: 'TRANSFER',
              createdAt: '2024-03-13'
            },
            {
              id: '4',
              cashBoxId: '3',
              cashBoxName: 'صندوق المصروفات النثرية',
              type: 'CASH_OUT',
              amount: 500,
              balance: 2000,
              description: 'شراء مستلزمات مكتبية',
              category: 'مصاريف مكتبية',
              transactionDate: '2024-03-12',
              performedBy: 'خالد علي',
              status: 'COMPLETED',
              relatedType: 'EXPENSE',
              receiptNumber: 'REC-003',
              createdAt: '2024-03-12'
            }
          ])
        }
      } catch (error) {
        console.error('Error fetching cash transactions:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchTransactions()
  }, [])

  const cashBoxes = [...new Set(transactions.map(t => t.cashBoxName))]

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = 
      transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.reference?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.cashBoxName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.performedBy?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = typeFilter === 'ALL' || transaction.type === typeFilter
    const matchesCashBox = cashBoxFilter === 'ALL' || transaction.cashBoxName === cashBoxFilter
    
    let matchesDate = true
    if (dateFilter !== 'ALL') {
      const transactionDate = new Date(transaction.transactionDate)
      const now = new Date()
      
      switch (dateFilter) {
        case 'TODAY':
          matchesDate = transactionDate.toDateString() === now.toDateString()
          break
        case 'WEEK':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          matchesDate = transactionDate >= weekAgo
          break
        case 'MONTH':
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          matchesDate = transactionDate >= monthAgo
          break
      }
    }
    
    return matchesSearch && matchesType && matchesCashBox && matchesDate
  })

  const getTransactionTypeLabel = (type: string) => {
    switch (type) {
      case 'CASH_IN':
        return 'إيداع نقدي'
      case 'CASH_OUT':
        return 'سحب نقدي'
      case 'TRANSFER_IN':
        return 'تحويل وارد'
      case 'TRANSFER_OUT':
        return 'تحويل صادر'
      default:
        return type
    }
  }

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'CASH_IN':
      case 'TRANSFER_IN':
        return 'text-green-600'
      case 'CASH_OUT':
      case 'TRANSFER_OUT':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'CASH_IN':
      case 'TRANSFER_IN':
        return <ArrowDownLeft className="h-4 w-4 text-green-600" />
      case 'CASH_OUT':
      case 'TRANSFER_OUT':
        return <ArrowUpRight className="h-4 w-4 text-red-600" />
      default:
        return <DollarSign className="h-4 w-4 text-gray-600" />
    }
  }

  const totalCashIn = filteredTransactions
    .filter(t => t.type === 'CASH_IN' || t.type === 'TRANSFER_IN')
    .reduce((sum, t) => sum + t.amount, 0)

  const totalCashOut = filteredTransactions
    .filter(t => t.type === 'CASH_OUT' || t.type === 'TRANSFER_OUT')
    .reduce((sum, t) => sum + t.amount, 0)

  const netFlow = totalCashIn - totalCashOut

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">المعاملات النقدية</h1>
            <p className="mt-2 text-gray-600">عرض وإدارة جميع المعاملات النقدية</p>
          </div>
          <div className="flex gap-2">
            <Link
              href="/treasury/transactions/new"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
            >
              <Plus className="h-5 w-5" />
              إضافة معاملة
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-500 p-3 rounded-lg">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي المعاملات</p>
                <p className="text-2xl font-bold text-blue-600">{filteredTransactions.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-500 p-3 rounded-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الإيداعات</p>
                <p className="text-xl font-bold text-green-600">
                  {totalCashIn.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-red-500 p-3 rounded-lg">
                <TrendingDown className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي السحوبات</p>
                <p className="text-xl font-bold text-red-600">
                  {totalCashOut.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className={`${netFlow >= 0 ? 'bg-green-500' : 'bg-red-500'} p-3 rounded-lg`}>
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">صافي التدفق</p>
                <p className={`text-xl font-bold ${netFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {netFlow.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="البحث في المعاملات..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
            >
              <option value="ALL">جميع الأنواع</option>
              <option value="CASH_IN">إيداع نقدي</option>
              <option value="CASH_OUT">سحب نقدي</option>
              <option value="TRANSFER_IN">تحويل وارد</option>
              <option value="TRANSFER_OUT">تحويل صادر</option>
            </select>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={cashBoxFilter}
              onChange={(e) => setCashBoxFilter(e.target.value)}
            >
              <option value="ALL">جميع الصناديق</option>
              {cashBoxes.map(cashBox => (
                <option key={cashBox} value={cashBox}>{cashBox}</option>
              ))}
            </select>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
            >
              <option value="ALL">جميع التواريخ</option>
              <option value="TODAY">اليوم</option>
              <option value="WEEK">آخر أسبوع</option>
              <option value="MONTH">آخر شهر</option>
            </select>
          </div>
        </div>

        {/* Transactions Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التاريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الصندوق
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    النوع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الوصف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المبلغ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الرصيد
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المنفذ
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTransactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {new Date(transaction.transactionDate).toLocaleDateString('ar-SA')}
                      </div>
                      <div className="text-xs text-gray-500">
                        {new Date(transaction.transactionDate).toLocaleTimeString('ar-SA')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Wallet className="h-4 w-4 text-gray-400 ml-2" />
                        <div className="text-sm font-medium text-gray-900">{transaction.cashBoxName}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        {getTransactionIcon(transaction.type)}
                        <span className={`text-sm font-medium ${getTransactionTypeColor(transaction.type)}`}>
                          {getTransactionTypeLabel(transaction.type)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">{transaction.description}</div>
                      {transaction.reference && (
                        <div className="text-xs text-gray-500">مرجع: {transaction.reference}</div>
                      )}
                      {transaction.category && (
                        <div className="text-xs text-blue-600">{transaction.category}</div>
                      )}
                      {transaction.receiptNumber && (
                        <div className="text-xs text-green-600">إيصال: {transaction.receiptNumber}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm font-bold ${getTransactionTypeColor(transaction.type)}`}>
                        {(transaction.type === 'CASH_IN' || transaction.type === 'TRANSFER_IN') ? '+' : '-'}
                        {transaction.amount.toLocaleString()} ر.س
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {transaction.balance.toLocaleString()} ر.س
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{transaction.performedBy || '-'}</div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredTransactions.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد معاملات مطابقة لبحثك</p>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  )
}
