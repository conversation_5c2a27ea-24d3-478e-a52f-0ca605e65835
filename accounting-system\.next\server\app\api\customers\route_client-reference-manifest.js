globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/customers/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/Providers/SessionProvider.tsx":{"*":{"id":"(ssr)/./src/components/Providers/SessionProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/settings/page.tsx":{"*":{"id":"(ssr)/./src/app/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/settings/users/new/page.tsx":{"*":{"id":"(ssr)/./src/app/settings/users/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customers/page.tsx":{"*":{"id":"(ssr)/./src/app/customers/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/invoices/page.tsx":{"*":{"id":"(ssr)/./src/app/invoices/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/reports/page.tsx":{"*":{"id":"(ssr)/./src/app/reports/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/signin/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/signin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/products/page.tsx":{"*":{"id":"(ssr)/./src/app/products/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/expenses/page.tsx":{"*":{"id":"(ssr)/./src/app/expenses/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/banking/page.tsx":{"*":{"id":"(ssr)/./src/app/banking/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/treasury/page.tsx":{"*":{"id":"(ssr)/./src/app/treasury/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/assets/page.tsx":{"*":{"id":"(ssr)/./src/app/assets/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/assets/reports/page.tsx":{"*":{"id":"(ssr)/./src/app/assets/reports/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/promote/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/promote/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/hr/page.tsx":{"*":{"id":"(ssr)/./src/app/hr/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/treasury/cash-boxes/page.tsx":{"*":{"id":"(ssr)/./src/app/treasury/cash-boxes/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/treasury/transactions/new/page.tsx":{"*":{"id":"(ssr)/./src/app/treasury/transactions/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/invoices/new/page.tsx":{"*":{"id":"(ssr)/./src/app/invoices/new/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Accounting\\accounting-system\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Accounting\\accounting-system\\src\\components\\Providers\\SessionProvider.tsx":{"id":"(app-pages-browser)/./src/components/Providers/SessionProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\settings\\page.tsx":{"id":"(app-pages-browser)/./src/app/settings/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\settings\\users\\new\\page.tsx":{"id":"(app-pages-browser)/./src/app/settings/users/new/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\customers\\page.tsx":{"id":"(app-pages-browser)/./src/app/customers/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\invoices\\page.tsx":{"id":"(app-pages-browser)/./src/app/invoices/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\reports\\page.tsx":{"id":"(app-pages-browser)/./src/app/reports/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\auth\\signin\\page.tsx":{"id":"(app-pages-browser)/./src/app/auth/signin/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\products\\page.tsx":{"id":"(app-pages-browser)/./src/app/products/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\expenses\\page.tsx":{"id":"(app-pages-browser)/./src/app/expenses/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\banking\\page.tsx":{"id":"(app-pages-browser)/./src/app/banking/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\treasury\\page.tsx":{"id":"(app-pages-browser)/./src/app/treasury/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\assets\\page.tsx":{"id":"(app-pages-browser)/./src/app/assets/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\assets\\reports\\page.tsx":{"id":"(app-pages-browser)/./src/app/assets/reports/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\admin\\promote\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/promote/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\hr\\page.tsx":{"id":"(app-pages-browser)/./src/app/hr/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\treasury\\cash-boxes\\page.tsx":{"id":"(app-pages-browser)/./src/app/treasury/cash-boxes/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\treasury\\transactions\\new\\page.tsx":{"id":"(app-pages-browser)/./src/app/treasury/transactions/new/page.tsx","name":"*","chunks":[],"async":false},"D:\\Accounting\\accounting-system\\src\\app\\invoices\\new\\page.tsx":{"id":"(app-pages-browser)/./src/app/invoices/new/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\Accounting\\accounting-system\\src\\":[],"D:\\Accounting\\accounting-system\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\Accounting\\accounting-system\\src\\app\\page":[],"D:\\Accounting\\accounting-system\\src\\app\\api\\customers\\route":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Providers/SessionProvider.tsx":{"*":{"id":"(rsc)/./src/components/Providers/SessionProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/settings/page.tsx":{"*":{"id":"(rsc)/./src/app/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/settings/users/new/page.tsx":{"*":{"id":"(rsc)/./src/app/settings/users/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customers/page.tsx":{"*":{"id":"(rsc)/./src/app/customers/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/invoices/page.tsx":{"*":{"id":"(rsc)/./src/app/invoices/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/reports/page.tsx":{"*":{"id":"(rsc)/./src/app/reports/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/signin/page.tsx":{"*":{"id":"(rsc)/./src/app/auth/signin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/products/page.tsx":{"*":{"id":"(rsc)/./src/app/products/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/expenses/page.tsx":{"*":{"id":"(rsc)/./src/app/expenses/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/banking/page.tsx":{"*":{"id":"(rsc)/./src/app/banking/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/treasury/page.tsx":{"*":{"id":"(rsc)/./src/app/treasury/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/assets/page.tsx":{"*":{"id":"(rsc)/./src/app/assets/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/assets/reports/page.tsx":{"*":{"id":"(rsc)/./src/app/assets/reports/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/promote/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/promote/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/hr/page.tsx":{"*":{"id":"(rsc)/./src/app/hr/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/treasury/cash-boxes/page.tsx":{"*":{"id":"(rsc)/./src/app/treasury/cash-boxes/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/treasury/transactions/new/page.tsx":{"*":{"id":"(rsc)/./src/app/treasury/transactions/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/invoices/new/page.tsx":{"*":{"id":"(rsc)/./src/app/invoices/new/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}