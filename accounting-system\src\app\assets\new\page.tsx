'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import MainLayout from '@/components/Layout/MainLayout'
import { Save, ArrowLeft, Package, Upload, X } from 'lucide-react'

const assetCategories = [
  { value: 'EQUIPMENT', label: 'معدات' },
  { value: 'FURNITURE', label: 'أثاث' },
  { value: 'VEHICLE', label: 'مركبات' },
  { value: 'BUILDING', label: 'مباني' },
  { value: 'COMPUTER', label: 'حاسوب' },
  { value: 'SOFTWARE', label: 'برمجيات' }
]

const conditions = [
  { value: 'EXCELLENT', label: 'ممتاز' },
  { value: 'GOOD', label: 'جيد' },
  { value: 'FAIR', label: 'مقبول' },
  { value: 'POOR', label: 'ضعيف' },
  { value: 'DAMAGED', label: 'تالف' }
]

const depreciationMethods = [
  { value: 'STRAIGHT_LINE', label: 'القسط الثابت' },
  { value: 'DECLINING_BALANCE', label: 'الرصيد المتناقص' }
]

const departments = [
  'الإدارة العامة',
  'المحاسبة',
  'المبيعات',
  'التسويق',
  'تقنية المعلومات',
  'الموارد البشرية',
  'المشتريات',
  'المخزن'
]

export default function NewAssetPage() {
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [asset, setAsset] = useState({
    assetCode: '',
    name: '',
    description: '',
    category: '',
    subCategory: '',
    brand: '',
    model: '',
    serialNumber: '',
    purchaseDate: '',
    purchasePrice: '',
    depreciationRate: '',
    depreciationMethod: 'STRAIGHT_LINE',
    usefulLife: '',
    location: '',
    department: '',
    assignedTo: '',
    condition: 'GOOD',
    warrantyExpiry: '',
    insuranceExpiry: '',
    supplier: '',
    invoiceNumber: '',
    notes: '',
    image: null as File | null
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setAsset(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setAsset(prev => ({ ...prev, image: file }))
      
      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const removeImage = () => {
    setAsset(prev => ({ ...prev, image: null }))
    setImagePreview(null)
  }

  const generateAssetCode = () => {
    const prefix = asset.category ? asset.category.substring(0, 3).toUpperCase() : 'AST'
    const timestamp = Date.now().toString().slice(-6)
    const code = `${prefix}-${timestamp}`
    setAsset(prev => ({ ...prev, assetCode: code }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!asset.name || !asset.category || !asset.purchaseDate || !asset.purchasePrice) {
      alert('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    if (parseFloat(asset.purchasePrice) <= 0) {
      alert('يجب أن يكون سعر الشراء أكبر من صفر')
      return
    }

    if (asset.usefulLife && parseInt(asset.usefulLife) <= 0) {
      alert('يجب أن يكون العمر الافتراضي أكبر من صفر')
      return
    }

    setSaving(true)

    try {
      const formData = new FormData()
      
      // Add all asset data
      Object.entries(asset).forEach(([key, value]) => {
        if (key === 'image' && value instanceof File) {
          formData.append('image', value)
        } else if (value !== null && value !== '') {
          formData.append(key, value.toString())
        }
      })

      const response = await fetch('/api/assets', {
        method: 'POST',
        body: formData,
      })

      if (response.ok) {
        alert('تم إضافة الأصل بنجاح!')
        router.push('/assets/list')
      } else {
        const error = await response.json()
        alert(`حدث خطأ: ${error.error || 'خطأ غير معروف'}`)
      }
    } catch (error) {
      console.error('Error saving asset:', error)
      alert('حدث خطأ أثناء حفظ الأصل')
    } finally {
      setSaving(false)
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.push('/assets/list')}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5" />
            العودة لقائمة الأصول
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إضافة أصل جديد</h1>
            <p className="mt-2 text-gray-600">إضافة أصل جديد إلى نظام إدارة الأصول</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                <Package className="h-5 w-5" />
                المعلومات الأساسية
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="assetCode" className="block text-sm font-medium text-gray-700 mb-2">
                    رمز الأصل *
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      id="assetCode"
                      name="assetCode"
                      value={asset.assetCode}
                      onChange={handleChange}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="مثل: EQP-001"
                      required
                    />
                    <button
                      type="button"
                      onClick={generateAssetCode}
                      className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
                    >
                      توليد
                    </button>
                  </div>
                </div>

                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    اسم الأصل *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={asset.name}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثل: جهاز كمبيوتر محمول"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                    الوصف
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={asset.description}
                    onChange={handleChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="وصف تفصيلي للأصل..."
                  />
                </div>

                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                    الفئة *
                  </label>
                  <select
                    id="category"
                    name="category"
                    value={asset.category}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  >
                    <option value="">اختر الفئة</option>
                    {assetCategories.map(category => (
                      <option key={category.value} value={category.value}>{category.label}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="subCategory" className="block text-sm font-medium text-gray-700 mb-2">
                    الفئة الفرعية
                  </label>
                  <input
                    type="text"
                    id="subCategory"
                    name="subCategory"
                    value={asset.subCategory}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثل: أجهزة محمولة"
                  />
                </div>

                <div>
                  <label htmlFor="brand" className="block text-sm font-medium text-gray-700 mb-2">
                    العلامة التجارية
                  </label>
                  <input
                    type="text"
                    id="brand"
                    name="brand"
                    value={asset.brand}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثل: Dell, HP, Apple"
                  />
                </div>

                <div>
                  <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-2">
                    الموديل
                  </label>
                  <input
                    type="text"
                    id="model"
                    name="model"
                    value={asset.model}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثل: Latitude 5520"
                  />
                </div>

                <div>
                  <label htmlFor="serialNumber" className="block text-sm font-medium text-gray-700 mb-2">
                    الرقم التسلسلي
                  </label>
                  <input
                    type="text"
                    id="serialNumber"
                    name="serialNumber"
                    value={asset.serialNumber}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="الرقم التسلسلي للجهاز"
                  />
                </div>

                <div>
                  <label htmlFor="condition" className="block text-sm font-medium text-gray-700 mb-2">
                    الحالة
                  </label>
                  <select
                    id="condition"
                    name="condition"
                    value={asset.condition}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {conditions.map(condition => (
                      <option key={condition.value} value={condition.value}>{condition.label}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Financial Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">المعلومات المالية</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="purchaseDate" className="block text-sm font-medium text-gray-700 mb-2">
                    تاريخ الشراء *
                  </label>
                  <input
                    type="date"
                    id="purchaseDate"
                    name="purchaseDate"
                    value={asset.purchaseDate}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="purchasePrice" className="block text-sm font-medium text-gray-700 mb-2">
                    سعر الشراء (ر.س) *
                  </label>
                  <input
                    type="number"
                    id="purchasePrice"
                    name="purchasePrice"
                    value={asset.purchasePrice}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0.00"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="depreciationRate" className="block text-sm font-medium text-gray-700 mb-2">
                    معدل الإهلاك السنوي (%)
                  </label>
                  <input
                    type="number"
                    id="depreciationRate"
                    name="depreciationRate"
                    value={asset.depreciationRate}
                    onChange={handleChange}
                    min="0"
                    max="100"
                    step="0.1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="10.0"
                  />
                </div>

                <div>
                  <label htmlFor="depreciationMethod" className="block text-sm font-medium text-gray-700 mb-2">
                    طريقة الإهلاك
                  </label>
                  <select
                    id="depreciationMethod"
                    name="depreciationMethod"
                    value={asset.depreciationMethod}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {depreciationMethods.map(method => (
                      <option key={method.value} value={method.value}>{method.label}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="usefulLife" className="block text-sm font-medium text-gray-700 mb-2">
                    العمر الافتراضي (سنوات)
                  </label>
                  <input
                    type="number"
                    id="usefulLife"
                    name="usefulLife"
                    value={asset.usefulLife}
                    onChange={handleChange}
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="5"
                  />
                </div>

                <div>
                  <label htmlFor="invoiceNumber" className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الفاتورة
                  </label>
                  <input
                    type="text"
                    id="invoiceNumber"
                    name="invoiceNumber"
                    value={asset.invoiceNumber}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="رقم فاتورة الشراء"
                  />
                </div>
              </div>
            </div>

            {/* Location and Assignment */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">الموقع والتخصيص</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                    الموقع
                  </label>
                  <input
                    type="text"
                    id="location"
                    name="location"
                    value={asset.location}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="مثل: مكتب الإدارة، الطابق الثاني"
                  />
                </div>

                <div>
                  <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-2">
                    القسم
                  </label>
                  <select
                    id="department"
                    name="department"
                    value={asset.department}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">اختر القسم</option>
                    {departments.map(dept => (
                      <option key={dept} value={dept}>{dept}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="assignedTo" className="block text-sm font-medium text-gray-700 mb-2">
                    مخصص لـ
                  </label>
                  <input
                    type="text"
                    id="assignedTo"
                    name="assignedTo"
                    value={asset.assignedTo}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="اسم الموظف أو القسم المخصص له الأصل"
                  />
                </div>

                <div>
                  <label htmlFor="supplier" className="block text-sm font-medium text-gray-700 mb-2">
                    المورد
                  </label>
                  <input
                    type="text"
                    id="supplier"
                    name="supplier"
                    value={asset.supplier}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="اسم المورد أو الشركة"
                  />
                </div>
              </div>
            </div>

            {/* Warranty and Insurance */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">الضمان والتأمين</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="warrantyExpiry" className="block text-sm font-medium text-gray-700 mb-2">
                    انتهاء الضمان
                  </label>
                  <input
                    type="date"
                    id="warrantyExpiry"
                    name="warrantyExpiry"
                    value={asset.warrantyExpiry}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label htmlFor="insuranceExpiry" className="block text-sm font-medium text-gray-700 mb-2">
                    انتهاء التأمين
                  </label>
                  <input
                    type="date"
                    id="insuranceExpiry"
                    name="insuranceExpiry"
                    value={asset.insuranceExpiry}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {/* Image Upload */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">صورة الأصل</h3>
              <div className="space-y-4">
                {imagePreview ? (
                  <div className="relative inline-block">
                    <img
                      src={imagePreview}
                      alt="معاينة الأصل"
                      className="w-32 h-32 object-cover rounded-lg border border-gray-300"
                    />
                    <button
                      type="button"
                      onClick={removeImage}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ) : (
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="mt-4">
                      <label htmlFor="image" className="cursor-pointer">
                        <span className="mt-2 block text-sm font-medium text-gray-900">
                          اضغط لرفع صورة الأصل
                        </span>
                        <input
                          id="image"
                          name="image"
                          type="file"
                          accept="image/*"
                          onChange={handleImageChange}
                          className="sr-only"
                        />
                      </label>
                      <p className="mt-1 text-xs text-gray-500">PNG, JPG, GIF حتى 10MB</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Notes */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                ملاحظات إضافية
              </label>
              <textarea
                id="notes"
                name="notes"
                value={asset.notes}
                onChange={handleChange}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="أي ملاحظات إضافية حول الأصل..."
              />
            </div>

            <div className="flex justify-end gap-4 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => router.push('/assets/list')}
                className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                إلغاء
              </button>
              <button
                type="submit"
                disabled={saving}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {saving ? 'جاري الحفظ...' : 'حفظ الأصل'}
              </button>
            </div>
          </form>
        </div>

        {/* Help Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">نصائح لإضافة الأصول:</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• استخدم رموز أصول واضحة ومنطقية لسهولة التتبع</li>
            <li>• أدخل معلومات الإهلاك بدقة لحساب القيمة الحالية</li>
            <li>• حدد الموقع والقسم المخصص بوضوح</li>
            <li>• ارفع صورة واضحة للأصل للمساعدة في التعرف عليه</li>
            <li>• سجل تواريخ الضمان والتأمين لتجنب انتهائها</li>
          </ul>
        </div>
      </div>
    </MainLayout>
  )
}
