'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import MainLayout from '@/components/Layout/MainLayout'
import { Plus, Search, ArrowRightLeft, CheckCircle, Clock, XCircle, Calendar, DollarSign } from 'lucide-react'

interface BankTransfer {
  id: string
  fromAccountId: string
  fromAccountName: string
  toAccountId: string
  toAccountName: string
  amount: number
  description: string
  reference?: string
  transferDate: string
  status: string
  fees: number
  exchangeRate: number
  notes?: string
  createdAt: string
}

export default function BankTransfersPage() {
  const [transfers, setTransfers] = useState<BankTransfer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('ALL')

  useEffect(() => {
    const fetchTransfers = async () => {
      try {
        const response = await fetch('/api/banking/transfers')
        if (response.ok) {
          const data = await response.json()
          setTransfers(data)
        } else {
          // Fallback to demo data if API fails
          setTransfers([
            {
              id: '1',
              fromAccountId: '1',
              fromAccountName: 'الحساب الجاري الرئيسي',
              toAccountId: '2',
              toAccountName: 'حساب التوفير',
              amount: 50000,
              description: 'تحويل للاحتياطي النقدي',
              reference: 'TRF-001',
              transferDate: '2024-03-15',
              status: 'COMPLETED',
              fees: 0,
              exchangeRate: 1,
              notes: 'تحويل داخلي بين حسابات الشركة',
              createdAt: '2024-03-15'
            },
            {
              id: '2',
              fromAccountId: '2',
              fromAccountName: 'حساب التوفير',
              toAccountId: '1',
              toAccountName: 'الحساب الجاري الرئيسي',
              amount: 25000,
              description: 'تحويل لتغطية المصاريف',
              reference: 'TRF-002',
              transferDate: '2024-03-14',
              status: 'COMPLETED',
              fees: 0,
              exchangeRate: 1,
              createdAt: '2024-03-14'
            },
            {
              id: '3',
              fromAccountId: '1',
              fromAccountName: 'الحساب الجاري الرئيسي',
              toAccountId: '3',
              toAccountName: 'حساب بالدولار',
              amount: 15000,
              description: 'تحويل للحساب بالدولار',
              reference: 'TRF-003',
              transferDate: '2024-03-13',
              status: 'PENDING',
              fees: 50,
              exchangeRate: 3.75,
              notes: 'تحويل عملة من ريال إلى دولار',
              createdAt: '2024-03-13'
            }
          ])
        }
      } catch (error) {
        console.error('Error fetching bank transfers:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchTransfers()
  }, [])

  const filteredTransfers = transfers.filter(transfer => {
    const matchesSearch = 
      transfer.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transfer.reference?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transfer.fromAccountName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transfer.toAccountName.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'ALL' || transfer.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const handleApproveTransfer = async (id: string) => {
    try {
      const response = await fetch(`/api/banking/transfers/${id}/approve`, {
        method: 'POST',
      })

      if (response.ok) {
        setTransfers(transfers.map(transfer => 
          transfer.id === id 
            ? { ...transfer, status: 'COMPLETED' }
            : transfer
        ))
        alert('تم الموافقة على التحويل!')
      } else {
        alert('حدث خطأ أثناء الموافقة على التحويل')
      }
    } catch (error) {
      console.error('Error approving transfer:', error)
      alert('حدث خطأ أثناء الموافقة على التحويل')
    }
  }

  const handleCancelTransfer = async (id: string) => {
    if (confirm('هل أنت متأكد من إلغاء هذا التحويل؟')) {
      try {
        const response = await fetch(`/api/banking/transfers/${id}/cancel`, {
          method: 'POST',
        })

        if (response.ok) {
          setTransfers(transfers.map(transfer => 
            transfer.id === id 
              ? { ...transfer, status: 'CANCELLED' }
              : transfer
          ))
          alert('تم إلغاء التحويل!')
        } else {
          alert('حدث خطأ أثناء إلغاء التحويل')
        }
      } catch (error) {
        console.error('Error cancelling transfer:', error)
        alert('حدث خطأ أثناء إلغاء التحويل')
      }
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800'
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'مكتمل'
      case 'PENDING':
        return 'في الانتظار'
      case 'CANCELLED':
        return 'ملغي'
      default:
        return status
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'CANCELLED':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return null
    }
  }

  const totalTransfers = filteredTransfers.length
  const completedTransfers = filteredTransfers.filter(t => t.status === 'COMPLETED').length
  const pendingTransfers = filteredTransfers.filter(t => t.status === 'PENDING').length
  const totalAmount = filteredTransfers
    .filter(t => t.status === 'COMPLETED')
    .reduce((sum, t) => sum + t.amount, 0)

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">التحويلات البنكية</h1>
            <p className="mt-2 text-gray-600">إدارة التحويلات بين الحسابات البنكية</p>
          </div>
          <Link
            href="/banking/transfers/new"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
          >
            <Plus className="h-5 w-5" />
            تحويل جديد
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-500 p-3 rounded-lg">
                <ArrowRightLeft className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي التحويلات</p>
                <p className="text-2xl font-bold text-blue-600">{totalTransfers}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-500 p-3 rounded-lg">
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">مكتملة</p>
                <p className="text-2xl font-bold text-green-600">{completedTransfers}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-yellow-500 p-3 rounded-lg">
                <Clock className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">في الانتظار</p>
                <p className="text-2xl font-bold text-yellow-600">{pendingTransfers}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-purple-500 p-3 rounded-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي المبالغ</p>
                <p className="text-xl font-bold text-purple-600">
                  {totalAmount.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="البحث في التحويلات..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="ALL">جميع الحالات</option>
              <option value="PENDING">في الانتظار</option>
              <option value="COMPLETED">مكتملة</option>
              <option value="CANCELLED">ملغية</option>
            </select>
          </div>
        </div>

        {/* Transfers Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التاريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    من الحساب
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    إلى الحساب
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المبلغ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الوصف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTransfers.map((transfer) => (
                  <tr key={transfer.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {new Date(transfer.transferDate).toLocaleDateString('ar-SA')}
                      </div>
                      {transfer.reference && (
                        <div className="text-xs text-gray-500">مرجع: {transfer.reference}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{transfer.fromAccountName}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{transfer.toAccountName}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-bold text-gray-900">
                        {transfer.amount.toLocaleString()} ر.س
                      </div>
                      {transfer.fees > 0 && (
                        <div className="text-xs text-red-600">رسوم: {transfer.fees} ر.س</div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">{transfer.description}</div>
                      {transfer.notes && (
                        <div className="text-xs text-gray-500 mt-1">{transfer.notes}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(transfer.status)}
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(transfer.status)}`}>
                          {getStatusLabel(transfer.status)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        {transfer.status === 'PENDING' && (
                          <>
                            <button
                              onClick={() => handleApproveTransfer(transfer.id)}
                              className="text-green-600 hover:text-green-900"
                              title="موافقة"
                            >
                              <CheckCircle className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleCancelTransfer(transfer.id)}
                              className="text-red-600 hover:text-red-900"
                              title="إلغاء"
                            >
                              <XCircle className="h-4 w-4" />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredTransfers.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد تحويلات مطابقة لبحثك</p>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  )
}
