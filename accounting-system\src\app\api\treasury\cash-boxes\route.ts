import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const cashBoxes = await prisma.cashBox.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(cashBoxes)
  } catch (error) {
    console.error('Error fetching cash boxes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      description,
      currency,
      balance,
      location,
      responsible,
      isActive,
      dailyLimit,
      notes
    } = body

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Cash box name is required' },
        { status: 400 }
      )
    }

    // Validate balance
    if (balance < 0) {
      return NextResponse.json(
        { error: 'Balance cannot be negative' },
        { status: 400 }
      )
    }

    const cashBox = await prisma.cashBox.create({
      data: {
        name,
        description: description || null,
        currency: currency || 'SAR',
        balance: parseFloat(balance) || 0,
        location: location || null,
        responsible: responsible || null,
        isActive: isActive !== false,
        dailyLimit: dailyLimit ? parseFloat(dailyLimit) : null,
        notes: notes || null
      }
    })

    // Create initial transaction if balance > 0
    if (parseFloat(balance) > 0) {
      await prisma.cashTransaction.create({
        data: {
          cashBoxId: cashBox.id,
          type: 'CASH_IN',
          amount: parseFloat(balance),
          balance: parseFloat(balance),
          description: 'الرصيد الابتدائي للصندوق',
          transactionDate: new Date(),
          performedBy: session.user.name || 'النظام',
          status: 'COMPLETED'
        }
      })
    }

    return NextResponse.json(cashBox, { status: 201 })
  } catch (error) {
    console.error('Error creating cash box:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
