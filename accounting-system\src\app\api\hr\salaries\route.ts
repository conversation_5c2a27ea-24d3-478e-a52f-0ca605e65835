import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const salaries = await prisma.salary.findMany({
      include: {
        employee: true
      },
      orderBy: [
        { year: 'desc' },
        { month: 'desc' },
        { createdAt: 'desc' }
      ]
    })

    // Map to include employee name
    const salariesWithEmployeeName = salaries.map(salary => ({
      ...salary,
      employeeName: `${salary.employee.firstName} ${salary.employee.lastName}`
    }))

    return NextResponse.json(salariesWithEmployeeName)
  } catch (error) {
    console.error('Error fetching salaries:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      employeeId,
      month,
      year,
      basicSalary,
      allowances = 0,
      deductions = 0,
      overtime = 0,
      bonus = 0,
      notes
    } = body

    // Validate required fields
    if (!employeeId || !month || !year || !basicSalary) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Calculate net salary
    const netSalary = basicSalary + allowances + overtime + bonus - deductions

    const salary = await prisma.salary.create({
      data: {
        employeeId,
        month: parseInt(month),
        year: parseInt(year),
        basicSalary: parseFloat(basicSalary),
        allowances: parseFloat(allowances),
        deductions: parseFloat(deductions),
        overtime: parseFloat(overtime),
        bonus: parseFloat(bonus),
        netSalary,
        notes: notes || null,
        status: 'PENDING'
      },
      include: {
        employee: true
      }
    })

    return NextResponse.json({
      ...salary,
      employeeName: `${salary.employee.firstName} ${salary.employee.lastName}`
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating salary:', error)
    
    // Handle unique constraint violations
    if (error instanceof Error && error.message.includes('Unique constraint')) {
      return NextResponse.json(
        { error: 'Salary for this employee and month already exists' },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
