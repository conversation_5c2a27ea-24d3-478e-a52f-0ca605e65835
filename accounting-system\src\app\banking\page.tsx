'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import MainLayout from '@/components/Layout/MainLayout'
import { 
  CreditCard, 
  ArrowRightLeft, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Building,
  Plus,
  Eye,
  Calendar,
  BarChart3
} from 'lucide-react'

interface BankingStats {
  totalAccounts: number
  activeAccounts: number
  totalBalance: number
  totalTransactions: number
  pendingTransfers: number
  monthlyInflow: number
  monthlyOutflow: number
}

export default function BankingDashboard() {
  const [stats, setStats] = useState<BankingStats>({
    totalAccounts: 0,
    activeAccounts: 0,
    totalBalance: 0,
    totalTransactions: 0,
    pendingTransfers: 0,
    monthlyInflow: 0,
    monthlyOutflow: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/banking/dashboard')
        if (response.ok) {
          const data = await response.json()
          setStats(data)
        } else {
          // Fallback to demo data
          setStats({
            totalAccounts: 5,
            activeAccounts: 4,
            totalBalance: 425000,
            totalTransactions: 156,
            pendingTransfers: 3,
            monthlyInflow: 185000,
            monthlyOutflow: 142000
          })
        }
      } catch (error) {
        console.error('Error fetching banking stats:', error)
        // Use demo data as fallback
        setStats({
          totalAccounts: 5,
          activeAccounts: 4,
          totalBalance: 425000,
          totalTransactions: 156,
          pendingTransfers: 3,
          monthlyInflow: 185000,
          monthlyOutflow: 142000
        })
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  const quickActions = [
    {
      title: 'إضافة حساب بنكي',
      description: 'إضافة حساب بنكي جديد إلى النظام',
      icon: Plus,
      href: '/banking/accounts/new',
      color: 'bg-blue-500'
    },
    {
      title: 'تحويل جديد',
      description: 'إجراء تحويل بين الحسابات البنكية',
      icon: ArrowRightLeft,
      href: '/banking/transfers/new',
      color: 'bg-green-500'
    },
    {
      title: 'إضافة معاملة',
      description: 'تسجيل معاملة بنكية جديدة',
      icon: DollarSign,
      href: '/banking/transactions/new',
      color: 'bg-purple-500'
    },
    {
      title: 'تقارير البنوك',
      description: 'عرض تقارير مفصلة عن الحسابات',
      icon: BarChart3,
      href: '/banking/reports',
      color: 'bg-orange-500'
    }
  ]

  const bankingModules = [
    {
      title: 'الحسابات البنكية',
      description: 'إدارة جميع الحسابات البنكية وأرصدتها',
      icon: CreditCard,
      href: '/banking/accounts',
      stats: `${stats.totalAccounts} حساب`,
      color: 'border-blue-500'
    },
    {
      title: 'المعاملات البنكية',
      description: 'عرض وإدارة جميع المعاملات البنكية',
      icon: TrendingUp,
      href: '/banking/transactions',
      stats: `${stats.totalTransactions} معاملة`,
      color: 'border-green-500'
    },
    {
      title: 'التحويلات البنكية',
      description: 'إدارة التحويلات بين الحسابات',
      icon: ArrowRightLeft,
      href: '/banking/transfers',
      stats: `${stats.pendingTransfers} في الانتظار`,
      color: 'border-purple-500'
    },
    {
      title: 'تقارير البنوك',
      description: 'تقارير مالية شاملة للحسابات البنكية',
      icon: BarChart3,
      href: '/banking/reports',
      stats: 'تقارير متقدمة',
      color: 'border-orange-500'
    }
  ]

  const netFlow = stats.monthlyInflow - stats.monthlyOutflow

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">لوحة تحكم البنوك</h1>
          <p className="mt-2 text-gray-600">إدارة شاملة للحسابات البنكية والمعاملات المالية</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-500 p-3 rounded-lg">
                <CreditCard className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الحسابات</p>
                <p className="text-2xl font-bold text-blue-600">{stats.totalAccounts}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-500 p-3 rounded-lg">
                <Building className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الحسابات النشطة</p>
                <p className="text-2xl font-bold text-green-600">{stats.activeAccounts}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-purple-500 p-3 rounded-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الأرصدة</p>
                <p className="text-xl font-bold text-purple-600">
                  {stats.totalBalance.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-indigo-500 p-3 rounded-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">المعاملات</p>
                <p className="text-2xl font-bold text-indigo-600">{stats.totalTransactions}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-yellow-500 p-3 rounded-lg">
                <ArrowRightLeft className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">تحويلات معلقة</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pendingTransfers}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-600 p-3 rounded-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">التدفق الوارد</p>
                <p className="text-lg font-bold text-green-600">
                  {stats.monthlyInflow.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className={`${netFlow >= 0 ? 'bg-green-500' : 'bg-red-500'} p-3 rounded-lg`}>
                {netFlow >= 0 ? (
                  <TrendingUp className="h-6 w-6 text-white" />
                ) : (
                  <TrendingDown className="h-6 w-6 text-white" />
                )}
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">صافي التدفق</p>
                <p className={`text-lg font-bold ${netFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {netFlow.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">الإجراءات السريعة</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action, index) => (
              <Link
                key={index}
                href={action.href}
                className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow duration-200"
              >
                <div className="flex items-center mb-4">
                  <div className={`${action.color} p-3 rounded-lg`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{action.title}</h3>
                <p className="text-sm text-gray-600">{action.description}</p>
              </Link>
            ))}
          </div>
        </div>

        {/* Banking Modules */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">وحدات البنوك</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {bankingModules.map((module, index) => (
              <Link
                key={index}
                href={module.href}
                className={`bg-white rounded-lg shadow p-6 border-r-4 ${module.color} hover:shadow-lg transition-shadow duration-200`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    <module.icon className="h-8 w-8 text-gray-600 ml-4" />
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{module.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{module.description}</p>
                      <p className="text-sm font-medium text-blue-600 mt-2">{module.stats}</p>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Recent Activities */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">الأنشطة الأخيرة</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center">
                <div className="bg-green-100 p-2 rounded-full">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                </div>
                <div className="mr-3">
                  <p className="text-sm font-medium text-gray-900">إيداع جديد</p>
                  <p className="text-sm text-gray-500">25,000 ر.س - الحساب الجاري الرئيسي</p>
                </div>
                <div className="mr-auto text-sm text-gray-500">منذ ساعتين</div>
              </div>
              
              <div className="flex items-center">
                <div className="bg-blue-100 p-2 rounded-full">
                  <ArrowRightLeft className="h-4 w-4 text-blue-600" />
                </div>
                <div className="mr-3">
                  <p className="text-sm font-medium text-gray-900">تحويل بين الحسابات</p>
                  <p className="text-sm text-gray-500">50,000 ر.س - من الجاري إلى التوفير</p>
                </div>
                <div className="mr-auto text-sm text-gray-500">منذ 4 ساعات</div>
              </div>
              
              <div className="flex items-center">
                <div className="bg-purple-100 p-2 rounded-full">
                  <Plus className="h-4 w-4 text-purple-600" />
                </div>
                <div className="mr-3">
                  <p className="text-sm font-medium text-gray-900">حساب جديد</p>
                  <p className="text-sm text-gray-500">تم إضافة حساب بالدولار الأمريكي</p>
                </div>
                <div className="mr-auto text-sm text-gray-500">أمس</div>
              </div>
              
              <div className="flex items-center">
                <div className="bg-red-100 p-2 rounded-full">
                  <TrendingDown className="h-4 w-4 text-red-600" />
                </div>
                <div className="mr-3">
                  <p className="text-sm font-medium text-gray-900">سحب</p>
                  <p className="text-sm text-gray-500">8,000 ر.س - دفع راتب موظف</p>
                </div>
                <div className="mr-auto text-sm text-gray-500">منذ يومين</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
