"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/invoices/new/page",{

/***/ "(app-pages-browser)/./src/app/invoices/new/page.tsx":
/*!***************************************!*\
  !*** ./src/app/invoices/new/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewInvoicePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(app-pages-browser)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction NewInvoicePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [invoice, setInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: '',\n        issueDate: new Date().toISOString().split('T')[0],\n        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n        status: 'DRAFT',\n        notes: '',\n        taxRate: 15 // 15% VAT\n    });\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            productId: '',\n            productName: '',\n            quantity: 1,\n            price: 0,\n            total: 0\n        }\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NewInvoicePage.useEffect\": ()=>{\n            const fetchData = {\n                \"NewInvoicePage.useEffect.fetchData\": async ()=>{\n                    try {\n                        const [customersRes, productsRes] = await Promise.all([\n                            fetch('/api/customers'),\n                            fetch('/api/products')\n                        ]);\n                        if (customersRes.ok && productsRes.ok) {\n                            const customersData = await customersRes.json();\n                            const productsData = await productsRes.json();\n                            setCustomers(customersData);\n                            setProducts(productsData);\n                        } else {\n                            // Fallback to demo data if API fails\n                            setCustomers([\n                                {\n                                    id: '1',\n                                    name: 'شركة الأمل للتجارة',\n                                    email: '<EMAIL>',\n                                    phone: '+966501234567',\n                                    address: 'الرياض، المملكة العربية السعودية',\n                                    taxNumber: '*********'\n                                },\n                                {\n                                    id: '2',\n                                    name: 'مؤسسة النور للخدمات',\n                                    email: '<EMAIL>',\n                                    phone: '+966507654321',\n                                    address: 'جدة، المملكة العربية السعودية',\n                                    taxNumber: '*********'\n                                },\n                                {\n                                    id: '3',\n                                    name: 'شركة الفجر للتطوير',\n                                    email: '<EMAIL>',\n                                    phone: '+966551234567',\n                                    address: 'الدمام، المملكة العربية السعودية',\n                                    taxNumber: '*********'\n                                }\n                            ]);\n                            setProducts([\n                                {\n                                    id: '1',\n                                    name: 'خدمة استشارات إدارية',\n                                    price: 500,\n                                    unit: 'ساعة'\n                                },\n                                {\n                                    id: '2',\n                                    name: 'تصميم موقع إلكتروني',\n                                    price: 5000,\n                                    unit: 'مشروع'\n                                },\n                                {\n                                    id: '3',\n                                    name: 'خدمة التسويق الرقمي',\n                                    price: 2000,\n                                    unit: 'شهر'\n                                }\n                            ]);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching data:', error);\n                    // Use demo data as fallback\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"NewInvoicePage.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"NewInvoicePage.useEffect\"], []);\n    const addItem = ()=>{\n        const newItem = {\n            id: Date.now().toString(),\n            productId: '',\n            productName: '',\n            quantity: 1,\n            price: 0,\n            total: 0\n        };\n        setItems([\n            ...items,\n            newItem\n        ]);\n    };\n    const removeItem = (id)=>{\n        if (items.length > 1) {\n            setItems(items.filter((item)=>item.id !== id));\n        }\n    };\n    const updateItem = (id, field, value)=>{\n        setItems(items.map((item)=>{\n            if (item.id === id) {\n                const updatedItem = {\n                    ...item,\n                    [field]: value\n                };\n                // If product is selected, update price and name\n                if (field === 'productId') {\n                    const product = products.find((p)=>p.id === value);\n                    if (product) {\n                        updatedItem.productName = product.name;\n                        updatedItem.price = product.price;\n                    }\n                }\n                // Calculate total\n                updatedItem.total = updatedItem.quantity * updatedItem.price;\n                return updatedItem;\n            }\n            return item;\n        }));\n    };\n    const calculateSubtotal = ()=>{\n        return items.reduce((sum, item)=>sum + item.total, 0);\n    };\n    const calculateTax = ()=>{\n        return calculateSubtotal() * invoice.taxRate / 100;\n    };\n    const calculateTotal = ()=>{\n        return calculateSubtotal() + calculateTax();\n    };\n    const generateInvoiceNumber = ()=>{\n        const now = new Date();\n        const year = now.getFullYear();\n        const month = String(now.getMonth() + 1).padStart(2, '0');\n        const day = String(now.getDate()).padStart(2, '0');\n        const time = String(now.getTime()).slice(-4);\n        return \"INV-\".concat(year).concat(month).concat(day, \"-\").concat(time);\n    };\n    const getStatusLabel = (status)=>{\n        const statusLabels = {\n            DRAFT: 'مسودة',\n            SENT: 'مرسلة',\n            PAID: 'مدفوعة',\n            OVERDUE: 'متأخرة',\n            CANCELLED: 'ملغية'\n        };\n        return statusLabels[status] || status;\n    };\n    const handleSave = async (overrideStatus)=>{\n        if (!invoice.customerId) {\n            alert('يرجى اختيار العميل');\n            return;\n        }\n        if (items.some((item)=>!item.productId || item.quantity <= 0)) {\n            alert('يرجى التأكد من صحة جميع عناصر الفاتورة');\n            return;\n        }\n        setSaving(true);\n        try {\n            const finalStatus = overrideStatus || invoice.status;\n            const invoiceData = {\n                number: generateInvoiceNumber(),\n                customerId: invoice.customerId,\n                issueDate: invoice.issueDate,\n                dueDate: invoice.dueDate,\n                status: finalStatus,\n                subtotal: calculateSubtotal(),\n                taxAmount: calculateTax(),\n                total: calculateTotal(),\n                notes: invoice.notes,\n                items: items.filter((item)=>item.productId)\n            };\n            const response = await fetch('/api/invoices', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(invoiceData)\n            });\n            if (response.ok) {\n                const statusLabels = {\n                    DRAFT: 'حفظ كمسودة',\n                    SENT: 'إرسال',\n                    PAID: 'تسجيل كمدفوعة',\n                    OVERDUE: 'تسجيل كمتأخرة',\n                    CANCELLED: 'إلغاء'\n                };\n                alert(\"تم \".concat(statusLabels[finalStatus], \" الفاتورة بنجاح!\"));\n                router.push('/invoices');\n            } else {\n                const error = await response.json();\n                alert(\"حدث خطأ: \".concat(error.error || 'خطأ غير معروف'));\n            }\n        } catch (error) {\n            console.error('Error saving invoice:', error);\n            alert('حدث خطأ أثناء حفظ الفاتورة');\n        } finally{\n            setSaving(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n            lineNumber: 268,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/invoices'),\n                                className: \"flex items-center gap-2 text-gray-600 hover:text-gray-900\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"العودة للفواتير\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"إنشاء فاتورة جديدة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"إنشاء فاتورة جديدة للعملاء\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"العميل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: invoice.customerId,\n                                            onChange: (e)=>setInvoice({\n                                                    ...invoice,\n                                                    customerId: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            required: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"اختر العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: customer.id,\n                                                        children: customer.name\n                                                    }, customer.id, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"تاريخ الإصدار\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"date\",\n                                                    value: invoice.issueDate,\n                                                    onChange: (e)=>setInvoice({\n                                                            ...invoice,\n                                                            issueDate: e.target.value\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"تاريخ الاستحقاق\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"date\",\n                                                    value: invoice.dueDate,\n                                                    onChange: (e)=>setInvoice({\n                                                            ...invoice,\n                                                            dueDate: e.target.value\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"حالة الفاتورة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: invoice.status,\n                                                            onChange: (e)=>setInvoice({\n                                                                    ...invoice,\n                                                                    status: e.target.value\n                                                                }),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"DRAFT\",\n                                                                    children: \"مسودة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"SENT\",\n                                                                    children: \"مرسلة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"PAID\",\n                                                                    children: \"مدفوعة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"OVERDUE\",\n                                                                    children: \"متأخرة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"CANCELLED\",\n                                                                    children: \"ملغية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(getStatusColor(invoice.status)),\n                                                                children: getStatusLabel(invoice.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"عناصر الفاتورة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addItem,\n                                            className: \"flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"إضافة عنصر\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"min-w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"المنتج/الخدمة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"الكمية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"السعر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"المجموع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"الإجراءات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: item.productId,\n                                                                    onChange: (e)=>updateItem(item.id, 'productId', e.target.value),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"اختر المنتج/الخدمة\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 401,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: product.id,\n                                                                                children: [\n                                                                                    product.name,\n                                                                                    \" - \",\n                                                                                    product.price,\n                                                                                    \" ر.س/\",\n                                                                                    product.unit\n                                                                                ]\n                                                                            }, product.id, true, {\n                                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 403,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    min: \"1\",\n                                                                    value: item.quantity,\n                                                                    onChange: (e)=>updateItem(item.id, 'quantity', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    min: \"0\",\n                                                                    step: \"0.01\",\n                                                                    value: item.price,\n                                                                    onChange: (e)=>updateItem(item.id, 'price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4 font-medium\",\n                                                                children: [\n                                                                    item.total.toLocaleString(),\n                                                                    \" ر.س\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>removeItem(item.id),\n                                                                    disabled: items.length === 1,\n                                                                    className: \"text-red-600 hover:text-red-900 disabled:text-gray-400 disabled:cursor-not-allowed\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 431,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"ملاحظات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: invoice.notes,\n                                            onChange: (e)=>setInvoice({\n                                                    ...invoice,\n                                                    notes: e.target.value\n                                                }),\n                                            rows: 4,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"ملاحظات إضافية...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"المجموع الفرعي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            calculateSubtotal().toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            \"ضريبة القيمة المضافة (\",\n                                                            invoice.taxRate,\n                                                            \"%):\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            calculateTax().toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 pt-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-lg font-bold\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"المجموع الكلي:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-600\",\n                                                            children: [\n                                                                calculateTotal().toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row justify-end gap-4 mt-8 pt-6 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/invoices'),\n                                    className: \"px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleSave('DRAFT'),\n                                            disabled: saving,\n                                            className: \"px-4 py-2 border border-gray-600 text-gray-600 rounded-lg hover:bg-gray-50 disabled:opacity-50 text-sm\",\n                                            children: saving ? 'جاري الحفظ...' : 'حفظ كمسودة'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleSave('SENT'),\n                                            disabled: saving,\n                                            className: \"px-4 py-2 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 disabled:opacity-50 text-sm\",\n                                            children: saving ? 'جاري الإرسال...' : 'حفظ وإرسال'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSave(),\n                                    disabled: saving,\n                                    className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 font-medium\",\n                                    children: saving ? 'جاري الحفظ...' : \"حفظ (\".concat(getStatusLabel(invoice.status), \")\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 483,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n            lineNumber: 278,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n        lineNumber: 277,\n        columnNumber: 5\n    }, this);\n}\n_s(NewInvoicePage, \"CUxGI2QNNfmCtzWNzINLVJHyqX8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewInvoicePage;\nvar _c;\n$RefreshReg$(_c, \"NewInvoicePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/invoices/new/page.tsx\n"));

/***/ })

});