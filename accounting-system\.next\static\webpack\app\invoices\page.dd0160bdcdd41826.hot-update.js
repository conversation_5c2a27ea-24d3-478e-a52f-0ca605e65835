"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/invoices/page",{

/***/ "(app-pages-browser)/./src/app/invoices/page.tsx":
/*!***********************************!*\
  !*** ./src/app/invoices/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InvoicesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(app-pages-browser)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,FileText,Plus,Search,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,FileText,Plus,Search,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,FileText,Plus,Search,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,FileText,Plus,Search,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,FileText,Plus,Search,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,FileText,Plus,Search,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,FileText,Plus,Search,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,FileText,Plus,Search,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst statusColors = {\n    DRAFT: 'bg-gray-100 text-gray-800',\n    SENT: 'bg-blue-100 text-blue-800',\n    PAID: 'bg-green-100 text-green-800',\n    OVERDUE: 'bg-red-100 text-red-800',\n    CANCELLED: 'bg-red-100 text-red-800'\n};\nconst statusLabels = {\n    DRAFT: 'مسودة',\n    SENT: 'مرسلة',\n    PAID: 'مدفوعة',\n    OVERDUE: 'متأخرة',\n    CANCELLED: 'ملغية'\n};\nfunction InvoicesPage() {\n    var _stats_byStatus_PAID, _stats_byStatus, _stats_byStatus_SENT, _stats_byStatus1, _stats_byStatus_OVERDUE, _stats_byStatus2, _stats_byStatus_DRAFT, _stats_byStatus3;\n    _s();\n    const [invoices, setInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvoicesPage.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"InvoicesPage.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"InvoicesPage.useEffect.timer\"], 500);\n            return ({\n                \"InvoicesPage.useEffect\": ()=>clearTimeout(timer)\n            })[\"InvoicesPage.useEffect\"];\n        }\n    }[\"InvoicesPage.useEffect\"], [\n        searchTerm\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvoicesPage.useEffect\": ()=>{\n            const fetchInvoices = {\n                \"InvoicesPage.useEffect.fetchInvoices\": async ()=>{\n                    setLoading(true);\n                    try {\n                        const params = new URLSearchParams();\n                        if (debouncedSearchTerm) params.append('search', debouncedSearchTerm);\n                        if (statusFilter !== 'ALL') params.append('status', statusFilter);\n                        const response = await fetch(\"/api/invoices?\".concat(params.toString()));\n                        if (response.ok) {\n                            const data = await response.json();\n                            setInvoices(data.invoices || []);\n                            setStats(data.stats || {});\n                        } else {\n                            // Fallback to demo data if API fails\n                            setInvoices([\n                                {\n                                    id: '1',\n                                    number: 'INV-001',\n                                    issueDate: '2024-01-15',\n                                    dueDate: '2024-02-15',\n                                    status: 'PAID',\n                                    total: 15000,\n                                    customer: {\n                                        id: '1',\n                                        name: 'شركة الأمل للتجارة',\n                                        email: '<EMAIL>',\n                                        phone: '0501234567'\n                                    },\n                                    items: []\n                                },\n                                {\n                                    id: '2',\n                                    number: 'INV-002',\n                                    issueDate: '2024-01-20',\n                                    dueDate: '2024-02-20',\n                                    status: 'SENT',\n                                    total: 8500,\n                                    customer: {\n                                        id: '2',\n                                        name: 'مؤسسة النور للخدمات',\n                                        email: '<EMAIL>',\n                                        phone: '0507654321'\n                                    },\n                                    items: []\n                                },\n                                {\n                                    id: '3',\n                                    number: 'INV-003',\n                                    issueDate: '2024-02-01',\n                                    dueDate: '2024-02-15',\n                                    status: 'OVERDUE',\n                                    total: 12000,\n                                    customer: {\n                                        id: '3',\n                                        name: 'شركة الفجر للتطوير',\n                                        email: '<EMAIL>',\n                                        phone: '0501112233'\n                                    },\n                                    items: []\n                                },\n                                {\n                                    id: '4',\n                                    number: 'INV-004',\n                                    issueDate: '2024-02-10',\n                                    dueDate: '2024-03-10',\n                                    status: 'DRAFT',\n                                    total: 5500,\n                                    customer: {\n                                        id: '1',\n                                        name: 'شركة الأمل للتجارة',\n                                        email: '<EMAIL>',\n                                        phone: '0501234567'\n                                    },\n                                    items: []\n                                }\n                            ]);\n                            setStats({\n                                totalInvoices: 4,\n                                byStatus: {\n                                    PAID: {\n                                        count: 1,\n                                        amount: 15000\n                                    },\n                                    SENT: {\n                                        count: 1,\n                                        amount: 8500\n                                    },\n                                    OVERDUE: {\n                                        count: 1,\n                                        amount: 12000\n                                    },\n                                    DRAFT: {\n                                        count: 1,\n                                        amount: 5500\n                                    },\n                                    CANCELLED: {\n                                        count: 0,\n                                        amount: 0\n                                    }\n                                }\n                            });\n                        }\n                    } catch (error) {\n                        console.error('Error fetching invoices:', error);\n                        setInvoices([]);\n                        setStats({});\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"InvoicesPage.useEffect.fetchInvoices\"];\n            fetchInvoices();\n        }\n    }[\"InvoicesPage.useEffect\"], [\n        debouncedSearchTerm,\n        statusFilter\n    ]);\n    const filteredInvoices = invoices // API already handles filtering\n    ;\n    const handleDeleteInvoice = async (id)=>{\n        if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {\n            try {\n                const response = await fetch(\"/api/invoices/\".concat(id), {\n                    method: 'DELETE'\n                });\n                if (response.ok) {\n                    setInvoices(invoices.filter((invoice)=>invoice.id !== id));\n                } else {\n                    alert('حدث خطأ أثناء حذف الفاتورة');\n                }\n            } catch (error) {\n                console.error('Error deleting invoice:', error);\n                alert('حدث خطأ أثناء حذف الفاتورة');\n            }\n        }\n    };\n    const getTotalByStatus = (status)=>{\n        if (stats.byStatus && stats.byStatus[status]) {\n            return stats.byStatus[status].amount;\n        }\n        return 0;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n            lineNumber: 196,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"إدارة الفواتير\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-gray-600\",\n                                    children: \"إنشاء وإدارة فواتير العملاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/invoices/new\",\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                \"إنشاء فاتورة جديدة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-5 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"إجمالي الفواتير\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.totalInvoices || 0\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: stats.totalAmount ? \"\".concat(stats.totalAmount.toLocaleString(), \" ر.س\") : '0 ر.س'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"المدفوعة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: [\n                                                    getTotalByStatus('PAID').toLocaleString(),\n                                                    \" ر.س\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: [\n                                                    ((_stats_byStatus = stats.byStatus) === null || _stats_byStatus === void 0 ? void 0 : (_stats_byStatus_PAID = _stats_byStatus.PAID) === null || _stats_byStatus_PAID === void 0 ? void 0 : _stats_byStatus_PAID.count) || 0,\n                                                    \" فاتورة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"المرسلة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: [\n                                                    getTotalByStatus('SENT').toLocaleString(),\n                                                    \" ر.س\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: [\n                                                    ((_stats_byStatus1 = stats.byStatus) === null || _stats_byStatus1 === void 0 ? void 0 : (_stats_byStatus_SENT = _stats_byStatus1.SENT) === null || _stats_byStatus_SENT === void 0 ? void 0 : _stats_byStatus_SENT.count) || 0,\n                                                    \" فاتورة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"المتأخرة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-red-600\",\n                                                children: [\n                                                    getTotalByStatus('OVERDUE').toLocaleString(),\n                                                    \" ر.س\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: [\n                                                    ((_stats_byStatus2 = stats.byStatus) === null || _stats_byStatus2 === void 0 ? void 0 : (_stats_byStatus_OVERDUE = _stats_byStatus2.OVERDUE) === null || _stats_byStatus_OVERDUE === void 0 ? void 0 : _stats_byStatus_OVERDUE.count) || 0,\n                                                    \" فاتورة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"المسودات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-600\",\n                                                children: [\n                                                    getTotalByStatus('DRAFT').toLocaleString(),\n                                                    \" ر.س\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: [\n                                                    ((_stats_byStatus3 = stats.byStatus) === null || _stats_byStatus3 === void 0 ? void 0 : (_stats_byStatus_DRAFT = _stats_byStatus3.DRAFT) === null || _stats_byStatus_DRAFT === void 0 ? void 0 : _stats_byStatus_DRAFT.count) || 0,\n                                                    \" فاتورة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-500 p-3 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"البحث في الفواتير...\",\n                                        className: \"w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this),\n                                    loading && searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                value: statusFilter,\n                                onChange: (e)=>setStatusFilter(e.target.value),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"ALL\",\n                                        children: \"جميع الحالات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"DRAFT\",\n                                        children: \"مسودة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"SENT\",\n                                        children: \"مرسلة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PAID\",\n                                        children: \"مدفوعة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"OVERDUE\",\n                                        children: \"متأخرة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"CANCELLED\",\n                                        children: \"ملغية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setSearchTerm('');\n                                            setStatusFilter('ALL');\n                                        },\n                                        className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\",\n                                        children: \"إعادة تعيين\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: [\n                                            filteredInvoices.length,\n                                            \" من \",\n                                            stats.totalInvoices || 0,\n                                            \" فاتورة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"رقم الفاتورة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"تاريخ الإصدار\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"تاريخ الاستحقاق\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"الحالة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"المبلغ\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"الإجراءات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: filteredInvoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: invoice.number\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: invoice.customerName\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: new Date(invoice.issueDate).toLocaleDateString('ar-SA')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: new Date(invoice.dueDate).toLocaleDateString('ar-SA')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(statusColors[invoice.status]),\n                                                            children: statusLabels[invoice.status]\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: [\n                                                                invoice.total.toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-blue-600 hover:text-blue-900\",\n                                                                    title: \"عرض\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-green-600 hover:text-green-900\",\n                                                                    title: \"تحميل\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-purple-600 hover:text-purple-900\",\n                                                                    title: \"إرسال\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-yellow-600 hover:text-yellow-900\",\n                                                                    title: \"تعديل\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleDeleteInvoice(invoice.id),\n                                                                    className: \"text-red-600 hover:text-red-900\",\n                                                                    title: \"حذف\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_FileText_Plus_Search_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, invoice.id, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this),\n                        filteredInvoices.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"لا توجد فواتير مطابقة لبحثك\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n            lineNumber: 206,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\page.tsx\",\n        lineNumber: 205,\n        columnNumber: 5\n    }, this);\n}\n_s(InvoicesPage, \"zxy1BrvLaFS+H2XDnjGfdUh58Nw=\");\n_c = InvoicesPage;\nvar _c;\n$RefreshReg$(_c, \"InvoicesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/invoices/page.tsx\n"));

/***/ })

});