'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import MainLayout from '@/components/Layout/MainLayout'
import { Plus, Trash2, Save, ArrowLeft } from 'lucide-react'

interface Customer {
  id: string
  name: string
  email?: string
  phone?: string
  address?: string
  taxNumber?: string
}

interface Product {
  id: string
  name: string
  price: number
  unit?: string
}

interface InvoiceItem {
  id: string
  productId: string
  productName: string
  quantity: number
  price: number
  total: number
}

export default function NewInvoicePage() {
  const router = useRouter()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  const [invoice, setInvoice] = useState({
    customerId: '',
    issueDate: new Date().toISOString().split('T')[0],
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
    status: 'DRAFT' as 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED',
    notes: '',
    taxRate: 15 // 15% VAT
  })

  const [items, setItems] = useState<InvoiceItem[]>([
    {
      id: '1',
      productId: '',
      productName: '',
      quantity: 1,
      price: 0,
      total: 0
    }
  ])

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [customersRes, productsRes] = await Promise.all([
          fetch('/api/customers'),
          fetch('/api/products')
        ])

        if (customersRes.ok && productsRes.ok) {
          const customersData = await customersRes.json()
          const productsData = await productsRes.json()

          setCustomers(customersData)
          setProducts(productsData)
        } else {
          // Fallback to demo data if API fails
          setCustomers([
            {
              id: '1',
              name: 'شركة الأمل للتجارة',
              email: '<EMAIL>',
              phone: '+966501234567',
              address: 'الرياض، المملكة العربية السعودية',
              taxNumber: '*********'
            },
            {
              id: '2',
              name: 'مؤسسة النور للخدمات',
              email: '<EMAIL>',
              phone: '+966507654321',
              address: 'جدة، المملكة العربية السعودية',
              taxNumber: '*********'
            },
            {
              id: '3',
              name: 'شركة الفجر للتطوير',
              email: '<EMAIL>',
              phone: '+966551234567',
              address: 'الدمام، المملكة العربية السعودية',
              taxNumber: '*********'
            }
          ])

          setProducts([
            {
              id: '1',
              name: 'خدمة استشارات إدارية',
              price: 500,
              unit: 'ساعة'
            },
            {
              id: '2',
              name: 'تصميم موقع إلكتروني',
              price: 5000,
              unit: 'مشروع'
            },
            {
              id: '3',
              name: 'خدمة التسويق الرقمي',
              price: 2000,
              unit: 'شهر'
            }
          ])
        }
      } catch (error) {
        console.error('Error fetching data:', error)
        // Use demo data as fallback
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  const addItem = () => {
    const newItem: InvoiceItem = {
      id: Date.now().toString(),
      productId: '',
      productName: '',
      quantity: 1,
      price: 0,
      total: 0
    }
    setItems([...items, newItem])
  }

  const removeItem = (id: string) => {
    if (items.length > 1) {
      setItems(items.filter(item => item.id !== id))
    }
  }

  const updateItem = (id: string, field: keyof InvoiceItem, value: any) => {
    setItems(items.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value }
        
        // If product is selected, update price and name
        if (field === 'productId') {
          const product = products.find(p => p.id === value)
          if (product) {
            updatedItem.productName = product.name
            updatedItem.price = product.price
          }
        }
        
        // Calculate total
        updatedItem.total = updatedItem.quantity * updatedItem.price
        
        return updatedItem
      }
      return item
    }))
  }

  const calculateSubtotal = () => {
    return items.reduce((sum, item) => sum + item.total, 0)
  }

  const calculateTax = () => {
    return (calculateSubtotal() * invoice.taxRate) / 100
  }

  const calculateTotal = () => {
    return calculateSubtotal() + calculateTax()
  }

  const generateInvoiceNumber = () => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const time = String(now.getTime()).slice(-4)
    return `INV-${year}${month}${day}-${time}`
  }

  const getStatusLabel = (status: string) => {
    const statusLabels = {
      DRAFT: 'مسودة',
      SENT: 'مرسلة',
      PAID: 'مدفوعة',
      OVERDUE: 'متأخرة',
      CANCELLED: 'ملغية'
    }
    return statusLabels[status as keyof typeof statusLabels] || status
  }

  const getStatusColor = (status: string) => {
    const statusColors = {
      DRAFT: 'bg-gray-100 text-gray-800',
      SENT: 'bg-blue-100 text-blue-800',
      PAID: 'bg-green-100 text-green-800',
      OVERDUE: 'bg-red-100 text-red-800',
      CANCELLED: 'bg-red-100 text-red-800'
    }
    return statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'
  }

  const handleSave = async (overrideStatus?: 'DRAFT' | 'SENT') => {
    if (!invoice.customerId) {
      alert('يرجى اختيار العميل')
      return
    }

    if (items.some(item => !item.productId || item.quantity <= 0)) {
      alert('يرجى التأكد من صحة جميع عناصر الفاتورة')
      return
    }

    setSaving(true)

    try {
      const finalStatus = overrideStatus || invoice.status
      const invoiceData = {
        number: generateInvoiceNumber(),
        customerId: invoice.customerId,
        issueDate: invoice.issueDate,
        dueDate: invoice.dueDate,
        status: finalStatus,
        subtotal: calculateSubtotal(),
        taxAmount: calculateTax(),
        total: calculateTotal(),
        notes: invoice.notes,
        items: items.filter(item => item.productId)
      }

      const response = await fetch('/api/invoices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invoiceData),
      })

      if (response.ok) {
        const statusLabels = {
          DRAFT: 'حفظ كمسودة',
          SENT: 'إرسال',
          PAID: 'تسجيل كمدفوعة',
          OVERDUE: 'تسجيل كمتأخرة',
          CANCELLED: 'إلغاء'
        }
        alert(`تم ${statusLabels[finalStatus]} الفاتورة بنجاح!`)
        router.push('/invoices')
      } else {
        const error = await response.json()
        alert(`حدث خطأ: ${error.error || 'خطأ غير معروف'}`)
      }
    } catch (error) {
      console.error('Error saving invoice:', error)
      alert('حدث خطأ أثناء حفظ الفاتورة')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={() => router.push('/invoices')}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-5 w-5" />
              العودة للفواتير
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">إنشاء فاتورة جديدة</h1>
              <p className="mt-2 text-gray-600">إنشاء فاتورة جديدة للعملاء</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          {/* Invoice Header */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                العميل *
              </label>
              <select
                value={invoice.customerId}
                onChange={(e) => setInvoice({...invoice, customerId: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">اختر العميل</option>
                {customers.map(customer => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تاريخ الإصدار
                </label>
                <input
                  type="date"
                  value={invoice.issueDate}
                  onChange={(e) => setInvoice({...invoice, issueDate: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تاريخ الاستحقاق
                </label>
                <input
                  type="date"
                  value={invoice.dueDate}
                  onChange={(e) => setInvoice({...invoice, dueDate: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  حالة الفاتورة
                </label>
                <div className="relative">
                  <select
                    value={invoice.status}
                    onChange={(e) => setInvoice({...invoice, status: e.target.value as any})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none"
                  >
                    <option value="DRAFT">مسودة</option>
                    <option value="SENT">مرسلة</option>
                    <option value="PAID">مدفوعة</option>
                    <option value="OVERDUE">متأخرة</option>
                    <option value="CANCELLED">ملغية</option>
                  </select>
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(invoice.status)}`}>
                      {getStatusLabel(invoice.status)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Invoice Items */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">عناصر الفاتورة</h3>
              <button
                onClick={addItem}
                className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <Plus className="h-4 w-4" />
                إضافة عنصر
              </button>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-right py-3 px-4 font-medium text-gray-700">المنتج/الخدمة</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">الكمية</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">السعر</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">المجموع</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {items.map((item) => (
                    <tr key={item.id} className="border-b border-gray-100">
                      <td className="py-3 px-4">
                        <select
                          value={item.productId}
                          onChange={(e) => updateItem(item.id, 'productId', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="">اختر المنتج/الخدمة</option>
                          {products.map(product => (
                            <option key={product.id} value={product.id}>
                              {product.name} - {product.price} ر.س/{product.unit}
                            </option>
                          ))}
                        </select>
                      </td>
                      <td className="py-3 px-4">
                        <input
                          type="number"
                          min="1"
                          value={item.quantity}
                          onChange={(e) => updateItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </td>
                      <td className="py-3 px-4">
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          value={item.price}
                          onChange={(e) => updateItem(item.id, 'price', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </td>
                      <td className="py-3 px-4 font-medium">
                        {item.total.toLocaleString()} ر.س
                      </td>
                      <td className="py-3 px-4">
                        <button
                          onClick={() => removeItem(item.id)}
                          disabled={items.length === 1}
                          className="text-red-600 hover:text-red-900 disabled:text-gray-400 disabled:cursor-not-allowed"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Invoice Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ملاحظات
              </label>
              <textarea
                value={invoice.notes}
                onChange={(e) => setInvoice({...invoice, notes: e.target.value})}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="ملاحظات إضافية..."
              />
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">المجموع الفرعي:</span>
                  <span className="font-medium">{calculateSubtotal().toLocaleString()} ر.س</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">ضريبة القيمة المضافة ({invoice.taxRate}%):</span>
                  <span className="font-medium">{calculateTax().toLocaleString()} ر.س</span>
                </div>
                <div className="border-t border-gray-200 pt-2">
                  <div className="flex justify-between text-lg font-bold">
                    <span>المجموع الكلي:</span>
                    <span className="text-blue-600">{calculateTotal().toLocaleString()} ر.س</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row justify-end gap-4 mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={() => router.push('/invoices')}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              إلغاء
            </button>

            {/* Quick Action Buttons */}
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => handleSave('DRAFT')}
                disabled={saving}
                className="px-4 py-2 border border-gray-600 text-gray-600 rounded-lg hover:bg-gray-50 disabled:opacity-50 text-sm"
              >
                {saving ? 'جاري الحفظ...' : 'حفظ كمسودة'}
              </button>
              <button
                onClick={() => handleSave('SENT')}
                disabled={saving}
                className="px-4 py-2 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 disabled:opacity-50 text-sm"
              >
                {saving ? 'جاري الإرسال...' : 'حفظ وإرسال'}
              </button>
            </div>

            {/* Main Save Button */}
            <button
              onClick={() => handleSave()}
              disabled={saving}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 font-medium"
            >
              {saving ? 'جاري الحفظ...' : `حفظ (${getStatusLabel(invoice.status)})`}
            </button>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
