'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import MainLayout from '@/components/Layout/MainLayout'
import { 
  Wallet, 
  Plus, 
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  ArrowRightLeft,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  CreditCard
} from 'lucide-react'

interface TreasuryStats {
  totalCashBoxes: number
  activeCashBoxes: number
  totalBalance: number
  todayTransactions: number
  todayCashIn: number
  todayCashOut: number
  pendingPettyCash: number
  pendingReports: number
}

export default function TreasuryDashboard() {
  const [stats, setStats] = useState<TreasuryStats>({
    totalCashBoxes: 0,
    activeCashBoxes: 0,
    totalBalance: 0,
    todayTransactions: 0,
    todayCashIn: 0,
    todayCashOut: 0,
    pendingPettyCash: 0,
    pendingReports: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/treasury/dashboard')
        if (response.ok) {
          const data = await response.json()
          setStats(data)
        } else {
          // Fallback to demo data
          setStats({
            totalCashBoxes: 4,
            activeCashBoxes: 3,
            totalBalance: 45000,
            todayTransactions: 12,
            todayCashIn: 15000,
            todayCashOut: 8500,
            pendingPettyCash: 5,
            pendingReports: 2
          })
        }
      } catch (error) {
        console.error('Error fetching treasury stats:', error)
        // Use demo data as fallback
        setStats({
          totalCashBoxes: 4,
          activeCashBoxes: 3,
          totalBalance: 45000,
          todayTransactions: 12,
          todayCashIn: 15000,
          todayCashOut: 8500,
          pendingPettyCash: 5,
          pendingReports: 2
        })
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  const quickActions = [
    {
      title: 'إضافة صندوق نقدي',
      description: 'إنشاء صندوق نقدي جديد',
      icon: Plus,
      href: '/treasury/cash-boxes/new',
      color: 'bg-blue-500'
    },
    {
      title: 'معاملة نقدية',
      description: 'تسجيل معاملة نقدية جديدة',
      icon: DollarSign,
      href: '/treasury/transactions/new',
      color: 'bg-green-500'
    },
    {
      title: 'طلب مصروف نثري',
      description: 'إنشاء طلب مصروف نثري جديد',
      icon: CreditCard,
      href: '/treasury/petty-cash/new',
      color: 'bg-purple-500'
    },
    {
      title: 'تقرير يومي',
      description: 'إنشاء تقرير يومي للخزينة',
      icon: FileText,
      href: '/treasury/reports/daily',
      color: 'bg-orange-500'
    }
  ]

  const treasuryModules = [
    {
      title: 'الصناديق النقدية',
      description: 'إدارة الصناديق النقدية وأرصدتها',
      icon: Wallet,
      href: '/treasury/cash-boxes',
      stats: `${stats.totalCashBoxes} صندوق`,
      color: 'border-blue-500'
    },
    {
      title: 'المعاملات النقدية',
      description: 'عرض وإدارة جميع المعاملات النقدية',
      icon: TrendingUp,
      href: '/treasury/transactions',
      stats: `${stats.todayTransactions} معاملة اليوم`,
      color: 'border-green-500'
    },
    {
      title: 'المصروفات النثرية',
      description: 'إدارة طلبات المصروفات النثرية',
      icon: CreditCard,
      href: '/treasury/petty-cash',
      stats: `${stats.pendingPettyCash} طلب معلق`,
      color: 'border-purple-500'
    },
    {
      title: 'التقارير اليومية',
      description: 'تقارير يومية للخزينة والصناديق',
      icon: FileText,
      href: '/treasury/reports',
      stats: `${stats.pendingReports} تقرير معلق`,
      color: 'border-orange-500'
    }
  ]

  const netCashFlow = stats.todayCashIn - stats.todayCashOut

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">لوحة تحكم الخزينة</h1>
          <p className="mt-2 text-gray-600">إدارة شاملة للخزينة والصناديق النقدية</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-500 p-3 rounded-lg">
                <Wallet className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الصناديق</p>
                <p className="text-2xl font-bold text-blue-600">{stats.totalCashBoxes}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-500 p-3 rounded-lg">
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الصناديق النشطة</p>
                <p className="text-2xl font-bold text-green-600">{stats.activeCashBoxes}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-purple-500 p-3 rounded-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الأرصدة</p>
                <p className="text-xl font-bold text-purple-600">
                  {stats.totalBalance.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-indigo-500 p-3 rounded-lg">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">معاملات اليوم</p>
                <p className="text-2xl font-bold text-indigo-600">{stats.todayTransactions}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-600 p-3 rounded-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">النقد الوارد</p>
                <p className="text-lg font-bold text-green-600">
                  {stats.todayCashIn.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-red-500 p-3 rounded-lg">
                <TrendingDown className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">النقد الصادر</p>
                <p className="text-lg font-bold text-red-600">
                  {stats.todayCashOut.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className={`${netCashFlow >= 0 ? 'bg-green-500' : 'bg-red-500'} p-3 rounded-lg`}>
                {netCashFlow >= 0 ? (
                  <TrendingUp className="h-6 w-6 text-white" />
                ) : (
                  <TrendingDown className="h-6 w-6 text-white" />
                )}
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">صافي التدفق</p>
                <p className={`text-lg font-bold ${netCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {netCashFlow.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-yellow-500 p-3 rounded-lg">
                <Clock className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">طلبات معلقة</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pendingPettyCash}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">الإجراءات السريعة</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action, index) => (
              <Link
                key={index}
                href={action.href}
                className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow duration-200"
              >
                <div className="flex items-center mb-4">
                  <div className={`${action.color} p-3 rounded-lg`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{action.title}</h3>
                <p className="text-sm text-gray-600">{action.description}</p>
              </Link>
            ))}
          </div>
        </div>

        {/* Treasury Modules */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">وحدات الخزينة</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {treasuryModules.map((module, index) => (
              <Link
                key={index}
                href={module.href}
                className={`bg-white rounded-lg shadow p-6 border-r-4 ${module.color} hover:shadow-lg transition-shadow duration-200`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    <module.icon className="h-8 w-8 text-gray-600 ml-4" />
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{module.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{module.description}</p>
                      <p className="text-sm font-medium text-blue-600 mt-2">{module.stats}</p>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Alerts and Recent Activities */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
                تنبيهات مهمة
              </h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-center p-3 bg-yellow-50 rounded-lg">
                  <Clock className="h-5 w-5 text-yellow-600 ml-3" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800">طلبات نثرية معلقة</p>
                    <p className="text-sm text-yellow-600">{stats.pendingPettyCash} طلبات تحتاج موافقة</p>
                  </div>
                </div>
                
                <div className="flex items-center p-3 bg-red-50 rounded-lg">
                  <AlertTriangle className="h-5 w-5 text-red-600 ml-3" />
                  <div>
                    <p className="text-sm font-medium text-red-800">تقارير معلقة</p>
                    <p className="text-sm text-red-600">{stats.pendingReports} تقارير يومية لم تُرسل</p>
                  </div>
                </div>
                
                <div className="flex items-center p-3 bg-blue-50 rounded-lg">
                  <Wallet className="h-5 w-5 text-blue-600 ml-3" />
                  <div>
                    <p className="text-sm font-medium text-blue-800">رصيد منخفض</p>
                    <p className="text-sm text-blue-600">صندوق الاستقبال يحتاج تعبئة</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">الأنشطة الأخيرة</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="bg-green-100 p-2 rounded-full">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="mr-3">
                    <p className="text-sm font-medium text-gray-900">إيداع نقدي</p>
                    <p className="text-sm text-gray-500">5,000 ر.س - صندوق الاستقبال</p>
                  </div>
                  <div className="mr-auto text-sm text-gray-500">منذ ساعة</div>
                </div>
                
                <div className="flex items-center">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <CreditCard className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="mr-3">
                    <p className="text-sm font-medium text-gray-900">مصروف نثري</p>
                    <p className="text-sm text-gray-500">500 ر.س - مصاريف مكتبية</p>
                  </div>
                  <div className="mr-auto text-sm text-gray-500">منذ 3 ساعات</div>
                </div>
                
                <div className="flex items-center">
                  <div className="bg-purple-100 p-2 rounded-full">
                    <ArrowRightLeft className="h-4 w-4 text-purple-600" />
                  </div>
                  <div className="mr-3">
                    <p className="text-sm font-medium text-gray-900">تحويل بين الصناديق</p>
                    <p className="text-sm text-gray-500">2,000 ر.س - من الرئيسي إلى الفرعي</p>
                  </div>
                  <div className="mr-auto text-sm text-gray-500">منذ 5 ساعات</div>
                </div>
                
                <div className="flex items-center">
                  <div className="bg-orange-100 p-2 rounded-full">
                    <FileText className="h-4 w-4 text-orange-600" />
                  </div>
                  <div className="mr-3">
                    <p className="text-sm font-medium text-gray-900">تقرير يومي</p>
                    <p className="text-sm text-gray-500">تم إرسال تقرير أمس للمراجعة</p>
                  </div>
                  <div className="mr-auto text-sm text-gray-500">أمس</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
