'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import MainLayout from '@/components/Layout/MainLayout'
import { Plus, Search, Edit, Trash2, CreditCard, Calendar, Receipt } from 'lucide-react'

interface Expense {
  id: string
  title: string
  description?: string
  amount: number
  date: string
  category: string
  categoryColor: string
  receipt?: string
}

export default function ExpensesPage() {
  const [expenses, setExpenses] = useState<Expense[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('ALL')
  const [dateFilter, setDateFilter] = useState<string>('ALL')

  useEffect(() => {
    const fetchExpenses = async () => {
      try {
        const response = await fetch('/api/expenses')
        if (response.ok) {
          const data = await response.json()
          // Map API data to match component interface
          const mappedExpenses = data.map((expense: any) => ({
            id: expense.id,
            title: expense.description,
            description: expense.notes || expense.description,
            amount: expense.amount,
            date: expense.date,
            category: expense.category,
            categoryColor: getCategoryColor(expense.category)
          }))
          setExpenses(mappedExpenses)
        } else {
          // Fallback to demo data if API fails
          setExpenses([
            {
              id: '1',
              title: 'إيجار المكتب',
              description: 'إيجار شهر يناير 2024',
              amount: 8000,
              date: '2024-01-01',
              category: 'إيجار',
              categoryColor: 'bg-blue-500'
            },
            {
              id: '2',
              title: 'فواتير الكهرباء',
              description: 'فاتورة كهرباء شهر يناير',
              amount: 450,
              date: '2024-01-15',
              category: 'مرافق',
              categoryColor: 'bg-yellow-500'
            },
            {
              id: '3',
              title: 'مواد مكتبية',
              description: 'أقلام وأوراق ومستلزمات مكتبية',
              amount: 320,
              date: '2024-01-20',
              category: 'مستلزمات',
              categoryColor: 'bg-green-500'
            },
            {
              id: '4',
              title: 'وقود السيارات',
              description: 'تعبئة وقود للسيارات الخدمية',
              amount: 600,
              date: '2024-02-01',
              category: 'مواصلات',
              categoryColor: 'bg-red-500'
            },
            {
              id: '5',
              title: 'اشتراك الإنترنت',
              description: 'اشتراك شهري للإنترنت',
              amount: 200,
              date: '2024-02-05',
              category: 'مرافق',
              categoryColor: 'bg-yellow-500'
            }
          ])
        }
      } catch (error) {
        console.error('Error fetching expenses:', error)
        // Use demo data as fallback
      } finally {
        setLoading(false)
      }
    }

    fetchExpenses()
  }, [])

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'إيجار': 'bg-blue-500',
      'مرافق': 'bg-yellow-500',
      'مستلزمات': 'bg-green-500',
      'مواصلات': 'bg-red-500',
      'مصاريف إدارية': 'bg-purple-500',
      'مصاريف تشغيلية': 'bg-indigo-500',
      'مصاريف تسويق': 'bg-pink-500',
      'مصاريف سفر': 'bg-orange-500',
      'مصاريف مكتبية': 'bg-green-500',
      'مصاريف صيانة': 'bg-gray-500',
      'مصاريف اتصالات': 'bg-cyan-500',
      'مصاريف أخرى': 'bg-slate-500'
    }
    return colors[category] || 'bg-gray-500'
  }

  const categories = [...new Set(expenses.map(expense => expense.category))]

  const filteredExpenses = expenses.filter(expense => {
    const matchesSearch = expense.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter === 'ALL' || expense.category === categoryFilter
    
    let matchesDate = true
    if (dateFilter !== 'ALL') {
      const expenseDate = new Date(expense.date)
      const now = new Date()
      
      switch (dateFilter) {
        case 'THIS_MONTH':
          matchesDate = expenseDate.getMonth() === now.getMonth() && 
                       expenseDate.getFullYear() === now.getFullYear()
          break
        case 'LAST_MONTH':
          const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1)
          matchesDate = expenseDate.getMonth() === lastMonth.getMonth() && 
                       expenseDate.getFullYear() === lastMonth.getFullYear()
          break
        case 'THIS_YEAR':
          matchesDate = expenseDate.getFullYear() === now.getFullYear()
          break
      }
    }
    
    return matchesSearch && matchesCategory && matchesDate
  })

  const handleDeleteExpense = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
      try {
        const response = await fetch(`/api/expenses/${id}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          setExpenses(expenses.filter(expense => expense.id !== id))
          alert('تم حذف المصروف بنجاح!')
        } else {
          alert('حدث خطأ أثناء حذف المصروف')
        }
      } catch (error) {
        console.error('Error deleting expense:', error)
        alert('حدث خطأ أثناء حذف المصروف')
      }
    }
  }

  const getTotalExpenses = () => {
    return filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0)
  }

  const getExpensesByCategory = () => {
    const categoryTotals: { [key: string]: number } = {}
    filteredExpenses.forEach(expense => {
      categoryTotals[expense.category] = (categoryTotals[expense.category] || 0) + expense.amount
    })
    return categoryTotals
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة المصروفات</h1>
            <p className="mt-2 text-gray-600">تتبع وإدارة مصروفات الشركة</p>
          </div>
          <Link
            href="/expenses/new"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
          >
            <Plus className="h-5 w-5" />
            إضافة مصروف جديد
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-red-500 p-3 rounded-lg">
                <CreditCard className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي المصروفات</p>
                <p className="text-2xl font-bold text-red-600">
                  {getTotalExpenses().toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-500 p-3 rounded-lg">
                <Receipt className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">عدد المصروفات</p>
                <p className="text-2xl font-bold text-blue-600">{filteredExpenses.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-500 p-3 rounded-lg">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">متوسط المصروف</p>
                <p className="text-2xl font-bold text-green-600">
                  {filteredExpenses.length > 0 
                    ? Math.round(getTotalExpenses() / filteredExpenses.length).toLocaleString() 
                    : 0} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-purple-500 p-3 rounded-lg">
                <CreditCard className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">التصنيفات</p>
                <p className="text-2xl font-bold text-purple-600">{categories.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="البحث في المصروفات..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
            >
              <option value="ALL">جميع التصنيفات</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
            >
              <option value="ALL">جميع الفترات</option>
              <option value="THIS_MONTH">هذا الشهر</option>
              <option value="LAST_MONTH">الشهر الماضي</option>
              <option value="THIS_YEAR">هذا العام</option>
            </select>
          </div>
        </div>

        {/* Expenses Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    عنوان المصروف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الوصف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التصنيف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المبلغ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التاريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredExpenses.map((expense) => (
                  <tr key={expense.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {expense.title}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 max-w-xs truncate">
                        {expense.description || '-'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full text-white ${expense.categoryColor}`}>
                        {expense.category}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-red-600">
                        -{expense.amount.toLocaleString()} ر.س
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {new Date(expense.date).toLocaleDateString('ar-SA')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <button className="text-blue-600 hover:text-blue-900">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button 
                          onClick={() => handleDeleteExpense(expense.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredExpenses.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد مصروفات مطابقة لبحثك</p>
            </div>
          )}
        </div>

        {/* Category Summary */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">ملخص المصروفات حسب التصنيف</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(getExpensesByCategory()).map(([category, total]) => (
              <div key={category} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium text-gray-900">{category}</span>
                <span className="text-sm font-bold text-red-600">
                  {total.toLocaleString()} ر.س
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
