'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import MainLayout from '@/components/Layout/MainLayout'
import { Plus, Search, Filter, TrendingUp, TrendingDown, ArrowUpRight, ArrowDownLeft, Calendar, DollarSign } from 'lucide-react'

interface BankTransaction {
  id: string
  accountId: string
  accountName: string
  type: string
  amount: number
  balance: number
  description: string
  reference?: string
  category?: string
  transactionDate: string
  status: string
  relatedType?: string
  createdAt: string
}

export default function BankTransactionsPage() {
  const [transactions, setTransactions] = useState<BankTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('ALL')
  const [accountFilter, setAccountFilter] = useState<string>('ALL')
  const [dateFilter, setDateFilter] = useState<string>('ALL')

  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        const response = await fetch('/api/banking/transactions')
        if (response.ok) {
          const data = await response.json()
          setTransactions(data)
        } else {
          // Fallback to demo data if API fails
          setTransactions([
            {
              id: '1',
              accountId: '1',
              accountName: 'الحساب الجاري الرئيسي',
              type: 'DEPOSIT',
              amount: 25000,
              balance: 175000,
              description: 'إيداع من عميل - فاتورة رقم INV-001',
              reference: 'INV-001',
              category: 'مبيعات',
              transactionDate: '2024-03-15',
              status: 'COMPLETED',
              relatedType: 'INVOICE',
              createdAt: '2024-03-15'
            },
            {
              id: '2',
              accountId: '1',
              accountName: 'الحساب الجاري الرئيسي',
              type: 'WITHDRAWAL',
              amount: 8000,
              balance: 167000,
              description: 'دفع راتب موظف - أحمد محمد',
              reference: 'SAL-001',
              category: 'رواتب',
              transactionDate: '2024-03-14',
              status: 'COMPLETED',
              relatedType: 'SALARY',
              createdAt: '2024-03-14'
            },
            {
              id: '3',
              accountId: '2',
              accountName: 'حساب التوفير',
              type: 'TRANSFER_IN',
              amount: 50000,
              balance: 125000,
              description: 'تحويل من الحساب الجاري',
              reference: 'TRF-001',
              category: 'تحويل داخلي',
              transactionDate: '2024-03-13',
              status: 'COMPLETED',
              relatedType: 'TRANSFER',
              createdAt: '2024-03-13'
            },
            {
              id: '4',
              accountId: '1',
              accountName: 'الحساب الجاري الرئيسي',
              type: 'WITHDRAWAL',
              amount: 3500,
              balance: 163500,
              description: 'دفع مصروف - إيجار المكتب',
              reference: 'EXP-001',
              category: 'مصاريف إدارية',
              transactionDate: '2024-03-12',
              status: 'COMPLETED',
              relatedType: 'EXPENSE',
              createdAt: '2024-03-12'
            }
          ])
        }
      } catch (error) {
        console.error('Error fetching bank transactions:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchTransactions()
  }, [])

  const accounts = [...new Set(transactions.map(t => t.accountName))]

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = 
      transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.reference?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.accountName.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = typeFilter === 'ALL' || transaction.type === typeFilter
    const matchesAccount = accountFilter === 'ALL' || transaction.accountName === accountFilter
    
    let matchesDate = true
    if (dateFilter !== 'ALL') {
      const transactionDate = new Date(transaction.transactionDate)
      const now = new Date()
      
      switch (dateFilter) {
        case 'TODAY':
          matchesDate = transactionDate.toDateString() === now.toDateString()
          break
        case 'WEEK':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          matchesDate = transactionDate >= weekAgo
          break
        case 'MONTH':
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          matchesDate = transactionDate >= monthAgo
          break
      }
    }
    
    return matchesSearch && matchesType && matchesAccount && matchesDate
  })

  const getTransactionTypeLabel = (type: string) => {
    switch (type) {
      case 'DEPOSIT':
        return 'إيداع'
      case 'WITHDRAWAL':
        return 'سحب'
      case 'TRANSFER_IN':
        return 'تحويل وارد'
      case 'TRANSFER_OUT':
        return 'تحويل صادر'
      default:
        return type
    }
  }

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'DEPOSIT':
      case 'TRANSFER_IN':
        return 'text-green-600'
      case 'WITHDRAWAL':
      case 'TRANSFER_OUT':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'DEPOSIT':
      case 'TRANSFER_IN':
        return <ArrowDownLeft className="h-4 w-4 text-green-600" />
      case 'WITHDRAWAL':
      case 'TRANSFER_OUT':
        return <ArrowUpRight className="h-4 w-4 text-red-600" />
      default:
        return <DollarSign className="h-4 w-4 text-gray-600" />
    }
  }

  const totalDeposits = filteredTransactions
    .filter(t => t.type === 'DEPOSIT' || t.type === 'TRANSFER_IN')
    .reduce((sum, t) => sum + t.amount, 0)

  const totalWithdrawals = filteredTransactions
    .filter(t => t.type === 'WITHDRAWAL' || t.type === 'TRANSFER_OUT')
    .reduce((sum, t) => sum + t.amount, 0)

  const netFlow = totalDeposits - totalWithdrawals

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">المعاملات البنكية</h1>
            <p className="mt-2 text-gray-600">عرض وإدارة جميع المعاملات البنكية</p>
          </div>
          <div className="flex gap-2">
            <Link
              href="/banking/transactions/new"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
            >
              <Plus className="h-5 w-5" />
              إضافة معاملة
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-blue-500 p-3 rounded-lg">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي المعاملات</p>
                <p className="text-2xl font-bold text-blue-600">{filteredTransactions.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-green-500 p-3 rounded-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الإيداعات</p>
                <p className="text-xl font-bold text-green-600">
                  {totalDeposits.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="bg-red-500 p-3 rounded-lg">
                <TrendingDown className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي السحوبات</p>
                <p className="text-xl font-bold text-red-600">
                  {totalWithdrawals.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className={`${netFlow >= 0 ? 'bg-green-500' : 'bg-red-500'} p-3 rounded-lg`}>
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">صافي التدفق</p>
                <p className={`text-xl font-bold ${netFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {netFlow.toLocaleString()} ر.س
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="البحث في المعاملات..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
            >
              <option value="ALL">جميع الأنواع</option>
              <option value="DEPOSIT">إيداع</option>
              <option value="WITHDRAWAL">سحب</option>
              <option value="TRANSFER_IN">تحويل وارد</option>
              <option value="TRANSFER_OUT">تحويل صادر</option>
            </select>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={accountFilter}
              onChange={(e) => setAccountFilter(e.target.value)}
            >
              <option value="ALL">جميع الحسابات</option>
              {accounts.map(account => (
                <option key={account} value={account}>{account}</option>
              ))}
            </select>
            
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
            >
              <option value="ALL">جميع التواريخ</option>
              <option value="TODAY">اليوم</option>
              <option value="WEEK">آخر أسبوع</option>
              <option value="MONTH">آخر شهر</option>
            </select>
          </div>
        </div>

        {/* Transactions Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التاريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحساب
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    النوع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الوصف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المبلغ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الرصيد
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTransactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {new Date(transaction.transactionDate).toLocaleDateString('ar-SA')}
                      </div>
                      <div className="text-xs text-gray-500">
                        {new Date(transaction.transactionDate).toLocaleTimeString('ar-SA')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{transaction.accountName}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        {getTransactionIcon(transaction.type)}
                        <span className={`text-sm font-medium ${getTransactionTypeColor(transaction.type)}`}>
                          {getTransactionTypeLabel(transaction.type)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">{transaction.description}</div>
                      {transaction.reference && (
                        <div className="text-xs text-gray-500">مرجع: {transaction.reference}</div>
                      )}
                      {transaction.category && (
                        <div className="text-xs text-blue-600">{transaction.category}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm font-bold ${getTransactionTypeColor(transaction.type)}`}>
                        {(transaction.type === 'DEPOSIT' || transaction.type === 'TRANSFER_IN') ? '+' : '-'}
                        {transaction.amount.toLocaleString()} ر.س
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {transaction.balance.toLocaleString()} ر.س
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredTransactions.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد معاملات مطابقة لبحثك</p>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  )
}
