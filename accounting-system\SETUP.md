# دليل الإعداد - نظام المحاسبة الاحترافي

## متطلبات النظام

- Node.js 18 أو أحدث
- npm أو yarn
- قاعدة بيانات (SQLite للتطوير، PostgreSQL للإنتاج)

## خطوات الإعداد

### 1. تثبيت التبعيات

```bash
npm install
```

### 2. إعداد متغيرات البيئة

انسخ ملف `.env.local` وقم بتحديث القيم:

```bash
# Database (SQLite للتطوير)
DATABASE_URL="file:./dev.db"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-change-in-production"

# Supabase (اختياري - للإنتاج)
NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"
```

### 3. إعداد قاعدة البيانات

```bash
# إنشاء قاعدة البيانات وتطبيق المخطط
npx prisma db push

# توليد Prisma Client
npx prisma generate
```

### 4. تشغيل التطبيق

```bash
npm run dev
```

التطبيق سيكون متاحاً على: http://localhost:3000

## إعداد قاعدة البيانات للإنتاج

### PostgreSQL

1. قم بإنشاء قاعدة بيانات PostgreSQL
2. حدث `DATABASE_URL` في ملف `.env.local`:

```
DATABASE_URL="postgresql://username:password@localhost:5432/accounting_db?schema=public"
```

3. حدث ملف `prisma/schema.prisma`:

```prisma
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}
```

4. قم بتطبيق المخطط:

```bash
npx prisma db push
```

### Supabase

1. أنشئ مشروع جديد في Supabase
2. احصل على URL ومفاتيح API
3. حدث متغيرات البيئة
4. قم بتطبيق المخطط

## إنشاء المستخدم الأول

1. انتقل إلى `/auth/signup`
2. أنشئ حساب جديد واختر دور "مشرف"
3. سجل الدخول باستخدام بياناتك

## البيانات التجريبية

لإضافة بيانات تجريبية، يمكنك إنشاء ملف `prisma/seed.ts`:

```typescript
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  // إنشاء مستخدم مشرف
  const hashedPassword = await bcrypt.hash('admin123', 12)
  
  const admin = await prisma.user.create({
    data: {
      name: 'المشرف العام',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'ADMIN'
    }
  })

  // إنشاء تصنيفات المصروفات
  const categories = await Promise.all([
    prisma.expenseCategory.create({
      data: { name: 'إيجار', color: 'bg-blue-500' }
    }),
    prisma.expenseCategory.create({
      data: { name: 'مرافق', color: 'bg-yellow-500' }
    }),
    prisma.expenseCategory.create({
      data: { name: 'مستلزمات', color: 'bg-green-500' }
    })
  ])

  // إنشاء عملاء تجريبيين
  const customers = await Promise.all([
    prisma.customer.create({
      data: {
        name: 'شركة الأمل للتجارة',
        email: '<EMAIL>',
        phone: '+966501234567',
        address: 'الرياض، المملكة العربية السعودية',
        taxNumber: '*********'
      }
    }),
    prisma.customer.create({
      data: {
        name: 'مؤسسة النور للخدمات',
        email: '<EMAIL>',
        phone: '+966507654321',
        address: 'جدة، المملكة العربية السعودية',
        taxNumber: '*********'
      }
    })
  ])

  console.log('تم إنشاء البيانات التجريبية بنجاح!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
```

ثم قم بتشغيل:

```bash
npx tsx prisma/seed.ts
```

## استكشاف الأخطاء

### مشكلة الاتصال بقاعدة البيانات

- تأكد من أن `DATABASE_URL` صحيح
- تأكد من تشغيل خادم قاعدة البيانات
- تحقق من صلاحيات المستخدم

### مشكلة Prisma Client

```bash
# إعادة توليد Prisma Client
npx prisma generate

# إعادة تطبيق المخطط
npx prisma db push
```

### مشكلة NextAuth

- تأكد من أن `NEXTAUTH_SECRET` محدد
- تأكد من أن `NEXTAUTH_URL` صحيح

## الأوامر المفيدة

```bash
# عرض قاعدة البيانات في المتصفح
npx prisma studio

# إعادة تعيين قاعدة البيانات
npx prisma db push --force-reset

# إنشاء migration جديد
npx prisma migrate dev --name init

# تطبيق migrations في الإنتاج
npx prisma migrate deploy
```

## الدعم

إذا واجهت أي مشاكل، يرجى:

1. التحقق من هذا الدليل
2. مراجعة ملفات السجل
3. التأكد من إعدادات البيئة
4. فتح issue في GitHub
