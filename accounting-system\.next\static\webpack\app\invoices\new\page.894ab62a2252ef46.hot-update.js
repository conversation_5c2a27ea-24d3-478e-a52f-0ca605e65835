"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/invoices/new/page",{

/***/ "(app-pages-browser)/./src/app/invoices/new/page.tsx":
/*!***************************************!*\
  !*** ./src/app/invoices/new/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewInvoicePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(app-pages-browser)/./src/components/Layout/MainLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction NewInvoicePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [invoice, setInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: '',\n        issueDate: new Date().toISOString().split('T')[0],\n        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n        status: 'DRAFT',\n        notes: '',\n        taxRate: 15 // 15% VAT\n    });\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            productId: '',\n            productName: '',\n            quantity: 1,\n            price: 0,\n            total: 0\n        }\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NewInvoicePage.useEffect\": ()=>{\n            const fetchData = {\n                \"NewInvoicePage.useEffect.fetchData\": async ()=>{\n                    try {\n                        const [customersRes, productsRes] = await Promise.all([\n                            fetch('/api/customers'),\n                            fetch('/api/products')\n                        ]);\n                        if (customersRes.ok && productsRes.ok) {\n                            const customersData = await customersRes.json();\n                            const productsData = await productsRes.json();\n                            setCustomers(customersData);\n                            setProducts(productsData);\n                        } else {\n                            // Fallback to demo data if API fails\n                            setCustomers([\n                                {\n                                    id: '1',\n                                    name: 'شركة الأمل للتجارة',\n                                    email: '<EMAIL>',\n                                    phone: '+966501234567',\n                                    address: 'الرياض، المملكة العربية السعودية',\n                                    taxNumber: '*********'\n                                },\n                                {\n                                    id: '2',\n                                    name: 'مؤسسة النور للخدمات',\n                                    email: '<EMAIL>',\n                                    phone: '+966507654321',\n                                    address: 'جدة، المملكة العربية السعودية',\n                                    taxNumber: '*********'\n                                },\n                                {\n                                    id: '3',\n                                    name: 'شركة الفجر للتطوير',\n                                    email: '<EMAIL>',\n                                    phone: '+966551234567',\n                                    address: 'الدمام، المملكة العربية السعودية',\n                                    taxNumber: '*********'\n                                }\n                            ]);\n                            setProducts([\n                                {\n                                    id: '1',\n                                    name: 'خدمة استشارات إدارية',\n                                    price: 500,\n                                    unit: 'ساعة'\n                                },\n                                {\n                                    id: '2',\n                                    name: 'تصميم موقع إلكتروني',\n                                    price: 5000,\n                                    unit: 'مشروع'\n                                },\n                                {\n                                    id: '3',\n                                    name: 'خدمة التسويق الرقمي',\n                                    price: 2000,\n                                    unit: 'شهر'\n                                }\n                            ]);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching data:', error);\n                    // Use demo data as fallback\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"NewInvoicePage.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"NewInvoicePage.useEffect\"], []);\n    const addItem = ()=>{\n        const newItem = {\n            id: Date.now().toString(),\n            productId: '',\n            productName: '',\n            quantity: 1,\n            price: 0,\n            total: 0\n        };\n        setItems([\n            ...items,\n            newItem\n        ]);\n    };\n    const removeItem = (id)=>{\n        if (items.length > 1) {\n            setItems(items.filter((item)=>item.id !== id));\n        }\n    };\n    const updateItem = (id, field, value)=>{\n        setItems(items.map((item)=>{\n            if (item.id === id) {\n                const updatedItem = {\n                    ...item,\n                    [field]: value\n                };\n                // If product is selected, update price and name\n                if (field === 'productId') {\n                    const product = products.find((p)=>p.id === value);\n                    if (product) {\n                        updatedItem.productName = product.name;\n                        updatedItem.price = product.price;\n                    }\n                }\n                // Calculate total\n                updatedItem.total = updatedItem.quantity * updatedItem.price;\n                return updatedItem;\n            }\n            return item;\n        }));\n    };\n    const calculateSubtotal = ()=>{\n        return items.reduce((sum, item)=>sum + item.total, 0);\n    };\n    const calculateTax = ()=>{\n        return calculateSubtotal() * invoice.taxRate / 100;\n    };\n    const calculateTotal = ()=>{\n        return calculateSubtotal() + calculateTax();\n    };\n    const generateInvoiceNumber = ()=>{\n        const now = new Date();\n        const year = now.getFullYear();\n        const month = String(now.getMonth() + 1).padStart(2, '0');\n        const day = String(now.getDate()).padStart(2, '0');\n        const time = String(now.getTime()).slice(-4);\n        return \"INV-\".concat(year).concat(month).concat(day, \"-\").concat(time);\n    };\n    const handleSave = async (overrideStatus)=>{\n        if (!invoice.customerId) {\n            alert('يرجى اختيار العميل');\n            return;\n        }\n        if (items.some((item)=>!item.productId || item.quantity <= 0)) {\n            alert('يرجى التأكد من صحة جميع عناصر الفاتورة');\n            return;\n        }\n        setSaving(true);\n        try {\n            const finalStatus = overrideStatus || invoice.status;\n            const invoiceData = {\n                number: generateInvoiceNumber(),\n                customerId: invoice.customerId,\n                issueDate: invoice.issueDate,\n                dueDate: invoice.dueDate,\n                status: finalStatus,\n                subtotal: calculateSubtotal(),\n                taxAmount: calculateTax(),\n                total: calculateTotal(),\n                notes: invoice.notes,\n                items: items.filter((item)=>item.productId)\n            };\n            const response = await fetch('/api/invoices', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(invoiceData)\n            });\n            if (response.ok) {\n                const statusLabels = {\n                    DRAFT: 'حفظ كمسودة',\n                    SENT: 'إرسال',\n                    PAID: 'تسجيل كمدفوعة',\n                    OVERDUE: 'تسجيل كمتأخرة',\n                    CANCELLED: 'إلغاء'\n                };\n                alert(\"تم \".concat(statusLabels[finalStatus], \" الفاتورة بنجاح!\"));\n                router.push('/invoices');\n            } else {\n                const error = await response.json();\n                alert(\"حدث خطأ: \".concat(error.error || 'خطأ غير معروف'));\n            }\n        } catch (error) {\n            console.error('Error saving invoice:', error);\n            alert('حدث خطأ أثناء حفظ الفاتورة');\n        } finally{\n            setSaving(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n            lineNumber: 257,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/invoices'),\n                                className: \"flex items-center gap-2 text-gray-600 hover:text-gray-900\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"العودة للفواتير\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"إنشاء فاتورة جديدة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"إنشاء فاتورة جديدة للعملاء\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"العميل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: invoice.customerId,\n                                            onChange: (e)=>setInvoice({\n                                                    ...invoice,\n                                                    customerId: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            required: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"اختر العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: customer.id,\n                                                        children: customer.name\n                                                    }, customer.id, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"تاريخ الإصدار\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"date\",\n                                                    value: invoice.issueDate,\n                                                    onChange: (e)=>setInvoice({\n                                                            ...invoice,\n                                                            issueDate: e.target.value\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"تاريخ الاستحقاق\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"date\",\n                                                    value: invoice.dueDate,\n                                                    onChange: (e)=>setInvoice({\n                                                            ...invoice,\n                                                            dueDate: e.target.value\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"حالة الفاتورة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: invoice.status,\n                                                    onChange: (e)=>setInvoice({\n                                                            ...invoice,\n                                                            status: e.target.value\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"DRAFT\",\n                                                            children: \"مسودة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"SENT\",\n                                                            children: \"مرسلة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"PAID\",\n                                                            children: \"مدفوعة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"OVERDUE\",\n                                                            children: \"متأخرة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"CANCELLED\",\n                                                            children: \"ملغية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"عناصر الفاتورة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addItem,\n                                            className: \"flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"إضافة عنصر\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"min-w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"المنتج/الخدمة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"الكمية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"السعر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"المجموع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 px-4 font-medium text-gray-700\",\n                                                            children: \"الإجراءات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: item.productId,\n                                                                    onChange: (e)=>updateItem(item.id, 'productId', e.target.value),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"اختر المنتج/الخدمة\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 383,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: product.id,\n                                                                                children: [\n                                                                                    product.name,\n                                                                                    \" - \",\n                                                                                    product.price,\n                                                                                    \" ر.س/\",\n                                                                                    product.unit\n                                                                                ]\n                                                                            }, product.id, true, {\n                                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 385,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    min: \"1\",\n                                                                    value: item.quantity,\n                                                                    onChange: (e)=>updateItem(item.id, 'quantity', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    min: \"0\",\n                                                                    step: \"0.01\",\n                                                                    value: item.price,\n                                                                    onChange: (e)=>updateItem(item.id, 'price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4 font-medium\",\n                                                                children: [\n                                                                    item.total.toLocaleString(),\n                                                                    \" ر.س\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>removeItem(item.id),\n                                                                    disabled: items.length === 1,\n                                                                    className: \"text-red-600 hover:text-red-900 disabled:text-gray-400 disabled:cursor-not-allowed\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"ملاحظات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: invoice.notes,\n                                            onChange: (e)=>setInvoice({\n                                                    ...invoice,\n                                                    notes: e.target.value\n                                                }),\n                                            rows: 4,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"ملاحظات إضافية...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"المجموع الفرعي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            calculateSubtotal().toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            \"ضريبة القيمة المضافة (\",\n                                                            invoice.taxRate,\n                                                            \"%):\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            calculateTax().toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 pt-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-lg font-bold\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"المجموع الكلي:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-600\",\n                                                            children: [\n                                                                calculateTotal().toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row justify-end gap-4 mt-8 pt-6 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/invoices'),\n                                    className: \"px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleSave('DRAFT'),\n                                            disabled: saving,\n                                            className: \"px-4 py-2 border border-gray-600 text-gray-600 rounded-lg hover:bg-gray-50 disabled:opacity-50 text-sm\",\n                                            children: saving ? 'جاري الحفظ...' : 'حفظ كمسودة'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleSave('SENT'),\n                                            disabled: saving,\n                                            className: \"px-4 py-2 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 disabled:opacity-50 text-sm\",\n                                            children: saving ? 'جاري الإرسال...' : 'حفظ وإرسال'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSave(),\n                                    disabled: saving,\n                                    className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 font-medium\",\n                                    children: saving ? 'جاري الحفظ...' : \"حفظ (\".concat(getStatusLabel(invoice.status), \")\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n            lineNumber: 267,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Accounting\\\\accounting-system\\\\src\\\\app\\\\invoices\\\\new\\\\page.tsx\",\n        lineNumber: 266,\n        columnNumber: 5\n    }, this);\n}\n_s(NewInvoicePage, \"CUxGI2QNNfmCtzWNzINLVJHyqX8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewInvoicePage;\nvar _c;\n$RefreshReg$(_c, \"NewInvoicePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/invoices/new/page.tsx\n"));

/***/ })

});